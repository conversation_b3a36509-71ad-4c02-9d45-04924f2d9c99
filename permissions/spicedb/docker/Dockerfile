# Dockerfile - Create a custom wrapper binary
FROM golang:1.25-alpine AS builder

# Create a Go wrapper that runs migrate then serve
WORKDIR /app
COPY start.go main.go

# Build the wrapper binary
RUN CGO_ENABLED=0 GOOS=linux go build -o spicedb-wrapper main.go

# Final stage using the SpiceDB distroless image
FROM ghcr.io/authzed/spicedb:v1.45.3

# Copy our custom wrapper binary
COPY --from=builder /app/spicedb-wrapper /usr/local/bin/spicedb-wrapper

# healthcheck
EXPOSE 8443
# rest api
EXPOSE 9090
# grpc api
EXPOSE 50051

# Run our wrapper binary instead of the original spicedb - override the original entrypoint
# This allows us to run migrations before starting the server
ENTRYPOINT []
CMD ["spicedb-wrapper"]