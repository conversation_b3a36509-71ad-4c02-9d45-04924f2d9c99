#!/bin/zsh
# delete all data in SpiceDB
HOST=************

#brew install grpcurl

#grpcurl -plaintext -d '{
#  "relationship_filter": {
#    "resource_type": "form"
#  }
#}' \
#-H "Authorization: Bearer somerandomkeyhere" \
#$HOST:50051 authzed.api.v1.PermissionsService/DeleteRelationships

#docker kill $(docker ps -q --filter "ancestor=authzed/spicedb")

RESOURCE_TYPES=("form" "foundation" "workspace")

for RESOURCE_TYPE in "${RESOURCE_TYPES[@]}"; do
  grpcurl -plaintext -d "{
    \"relationship_filter\": {
      \"resource_type\": \"${RESOURCE_TYPE}\"
    }
  }" \
  -H "Authorization: Bearer somerandomkeyhere" \
  $HOST:50051 authzed.api.v1.PermissionsService/DeleteRelationships
done