# https://authzed.com/docs/spicedb/modeling/validation-testing-debugging#zed-validate

# Unit test for use cases in the OT Data Model:
#  https://www.figma.com/board/AObyvNSSZk77qjQw7G9KXD/OT-Data-Modelling?node-id=2308-2752&t=XMFkWlYKjCKdH9Am-4

# Run from directory permissions/spicedb:
#  docker run -v ./resources/:/tmp/resources/ -w /tmp/resources authzed/zed validate ./collection-unittest.yaml

schemaFile: "./schema.zed"
relationships: |-
  //
  // 1
  //
  
  foundation:corporate_tax#manager@subject:1
  foundation:corporate_tax#write@subject:5
  
  //
  // 2
  //
  
  foundation:client_abc#parent@foundation:corporate_tax
  foundation:client_abc#inherit_from@foundation:corporate_tax
  
  // invite only
  foundation:client_def#parent@foundation:corporate_tax
  foundation:client_def#read@subject:2
  foundation:client_def#write@subject:5
  
  form:form_1#parent@foundation:corporate_tax
  form:form_1#inherit_from@foundation:corporate_tax
  form:form_1#read@subject:1
  form:form_1#read@subject:3
  form:form_1#write@subject:5
  
  // invite only
  form:form_2#parent@foundation:corporate_tax
  form:form_2#write@subject:1
  form:form_2#read@subject:5

  //
  // 3
  //

  foundation:entity_z#parent@foundation:client_abc
  foundation:entity_z#write@subject:5
  
  foundation:entity_y#parent@foundation:client_abc
  foundation:entity_y#inherit_from@foundation:client_abc
  
  form:form_3#parent@foundation:client_abc
  form:form_3#inherit_from@foundation:client_abc
  
  foundation:entity_x#parent@foundation:client_def
  foundation:entity_x#inherit_from@foundation:client_def
  foundation:entity_x#read@subject:1
  
  foundation:entity_w#parent@foundation:client_def
  foundation:entity_w#inherit_from@foundation:client_def
  
  form:form_4#parent@foundation:client_def
  form:form_4#write@subject:5
  
  form:form_5#parent@foundation:client_def
  form:form_5#inherit_from@foundation:client_def
  form:form_5#write@subject:4

  //
  // 4
  //

  form:form_6#parent@foundation:entity_z
  form:form_6#inherit_from@foundation:entity_z
  
  form:form_7#parent@foundation:entity_y
  form:form_7#write@subject:4

  form:form_8#parent@foundation:entity_w
  form:form_8#inherit_from@foundation:entity_w

  form:form_9#parent@foundation:entity_w
  form:form_9#write@subject:2

assertions:
  assertTrue:

    - foundation:corporate_tax#edit@subject:1
    - foundation:corporate_tax#view@subject:1
    - foundation:client_abc#edit@subject:1
    - foundation:client_abc#view@subject:1
    - foundation:entity_y#edit@subject:1
    - foundation:entity_y#view@subject:1
    - foundation:entity_x#view@subject:1
    - form:form_3#edit@subject:1
    - form:form_3#view@subject:1
    - form:form_1#edit@subject:1
    - form:form_1#view@subject:1
    - form:form_2#edit@subject:1
    - form:form_2#view@subject:1

    - foundation:client_def#view@subject:2
    - foundation:entity_x#view@subject:2
    - foundation:entity_w#view@subject:2
    - form:form_5#view@subject:2
    - form:form_8#view@subject:2
    - form:form_9#edit@subject:2

    - form:form_1#view@subject:3

    - form:form_5#edit@subject:4
    - form:form_7#edit@subject:4

    - foundation:corporate_tax#edit@subject:5
    - foundation:client_abc#edit@subject:5
    - foundation:client_def#edit@subject:5
    - form:form_1#edit@subject:5
    - form:form_2#view@subject:5
    - foundation:entity_z#edit@subject:5
    - foundation:entity_y#edit@subject:5
    - foundation:entity_x#edit@subject:5
    - foundation:entity_w#edit@subject:5
    - form:form_3#edit@subject:5
    - form:form_4#edit@subject:5
    - form:form_5#edit@subject:5
    - form:form_6#edit@subject:5
    - form:form_8#edit@subject:5

  assertFalse:
    - foundation:client_def#view@subject:1
    - foundation:client_def#edit@subject:1
    - foundation:entity_z#view@subject:1
    - foundation:entity_z#edit@subject:1
    - foundation:entity_w#view@subject:1
    - foundation:entity_w#edit@subject:1
    - foundation:entity_x#edit@subject:1
    - form:form_4#view@subject:1
    - form:form_4#edit@subject:1
    - form:form_5#view@subject:1
    - form:form_5#edit@subject:1
    - form:form_6#view@subject:1
    - form:form_6#edit@subject:1
    - form:form_7#view@subject:1
    - form:form_7#edit@subject:1
    - form:form_8#view@subject:1
    - form:form_8#edit@subject:1
    - form:form_9#view@subject:1
    - form:form_9#edit@subject:1

    - foundation:corporate_tax#view@subject:2
    - foundation:client_abc#view@subject:2
    - foundation:entity_z#view@subject:2
    - foundation:entity_y#view@subject:2
    - form:form_1#view@subject:2
    - form:form_2#view@subject:2
    - form:form_3#view@subject:2
    - form:form_4#view@subject:2
    - form:form_6#view@subject:2
    - form:form_7#view@subject:2

    - foundation:corporate_tax#view@subject:3
    - foundation:client_abc#view@subject:3
    - foundation:client_def#view@subject:3
    - foundation:entity_z#view@subject:3
    - foundation:entity_y#view@subject:3
    - foundation:entity_x#view@subject:3
    - foundation:entity_w#view@subject:3
    - form:form_2#view@subject:3
    - form:form_3#view@subject:3
    - form:form_4#view@subject:3
    - form:form_5#view@subject:3
    - form:form_6#view@subject:3
    - form:form_7#view@subject:3
    - form:form_8#view@subject:3
    - form:form_9#view@subject:3

    - foundation:corporate_tax#view@subject:4
    - foundation:client_abc#view@subject:4
    - foundation:client_def#view@subject:4
    - foundation:entity_z#view@subject:4
    - foundation:entity_y#view@subject:4
    - foundation:entity_x#view@subject:4
    - foundation:entity_w#view@subject:4
    - form:form_1#view@subject:4
    - form:form_2#view@subject:4
    - form:form_3#view@subject:4
    - form:form_4#view@subject:4
    - form:form_6#view@subject:4
    - form:form_8#view@subject:4
    - form:form_9#view@subject:4

    - form:form_7#view@subject:5
    - form:form_9#view@subject:5
validation:
  foundation:corporate_tax#view:
    - "[subject:1] is <foundation:corporate_tax#manager>"
    - "[subject:5] is <foundation:corporate_tax#write>"
  foundation:corporate_tax#edit:
    - "[subject:1] is <foundation:corporate_tax#manager>"
    - "[subject:5] is <foundation:corporate_tax#write>"
  foundation:client_abc#view:
    - "[subject:1] is <foundation:corporate_tax#manager>"
    - "[subject:5] is <foundation:corporate_tax#write>"
  foundation:client_abc#edit:
    - "[subject:1] is <foundation:corporate_tax#manager>"
    - "[subject:5] is <foundation:corporate_tax#write>"
  foundation:client_def#view:
    - "[subject:2] is <foundation:client_def#read>"
    - "[subject:5] is <foundation:client_def#write>"
  foundation:client_def#edit:
    - "[subject:5] is <foundation:client_def#write>"
  form:form_1#view:
    - "[subject:1] is <form:form_1#read>/<foundation:corporate_tax#manager>"
    - "[subject:3] is <form:form_1#read>"
    - "[subject:5] is <form:form_1#write>/<foundation:corporate_tax#write>"
  form:form_1#edit:
    - "[subject:1] is <foundation:corporate_tax#manager>"
    - "[subject:5] is <form:form_1#write>/<foundation:corporate_tax#write>"
  form:form_2#view:
    - "[subject:1] is <form:form_2#write>"
    - "[subject:5] is <form:form_2#read>"
  foundation:entity_z#view:
    - "[subject:5] is <foundation:entity_z#write>"
  foundation:entity_z#edit:
    - "[subject:5] is <foundation:entity_z#write>"
  foundation:entity_y#view:
    - "[subject:1] is <foundation:corporate_tax#manager>"
    - "[subject:5] is <foundation:corporate_tax#write>"
  foundation:entity_y#edit:
    - "[subject:1] is <foundation:corporate_tax#manager>"
    - "[subject:5] is <foundation:corporate_tax#write>"
  foundation:entity_x#view:
    - "[subject:1] is <foundation:entity_x#read>"
    - "[subject:2] is <foundation:client_def#read>"
    - "[subject:5] is <foundation:client_def#write>"
  foundation:entity_x#edit:
    - "[subject:5] is <foundation:client_def#write>"
  foundation:entity_w#view:
    - "[subject:2] is <foundation:client_def#read>"
    - "[subject:5] is <foundation:client_def#write>"
  foundation:entity_w#edit:
    - "[subject:5] is <foundation:client_def#write>"
  form:form_3#view:
    - "[subject:1] is <foundation:corporate_tax#manager>"
    - "[subject:5] is <foundation:corporate_tax#write>"
  form:form_3#edit:
    - "[subject:1] is <foundation:corporate_tax#manager>"
    - "[subject:5] is <foundation:corporate_tax#write>"
  form:form_4#view:
    - "[subject:5] is <form:form_4#write>"
  form:form_4#edit:
    - "[subject:5] is <form:form_4#write>"
  form:form_5#view:
    - "[subject:4] is <form:form_5#write>"
    - "[subject:5] is <foundation:client_def#write>"
    - "[subject:2] is <foundation:client_def#read>"
  form:form_5#edit:
    - "[subject:4] is <form:form_5#write>"
    - "[subject:5] is <foundation:client_def#write>"
  form:form_6#view:
    - "[subject:5] is <foundation:entity_z#write>"
  form:form_6#edit:
    - "[subject:5] is <foundation:entity_z#write>"
  form:form_7#view:
    - "[subject:4] is <form:form_7#write>"
  form:form_7#edit:
    - "[subject:4] is <form:form_7#write>"
  form:form_8#view:
    - "[subject:2] is <foundation:client_def#read>"
    - "[subject:5] is <foundation:client_def#write>"
  form:form_8#edit:
    - "[subject:5] is <foundation:client_def#write>"
  form:form_9#view:
    - "[subject:2] is <form:form_9#write>"
  form:form_9#edit:
    - "[subject:2] is <form:form_9#write>"
