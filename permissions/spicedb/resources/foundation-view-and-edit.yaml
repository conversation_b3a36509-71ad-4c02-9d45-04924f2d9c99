#  https://authzed.com/docs/spicedb/modeling/validation-testing-debugging#zed-validate

# Run from `permissions/spicedb` directory with:
#  docker run -v ./resources/:/tmp/resources/ -w /tmp/resources authzed/zed validate ./foundation-view-and-edit.yaml
schemaFile: "./schema.zed"
relationships: |-  # Set up workspace w1 with owner AA and member AAB
  foundation:FN1#admin@subject:AA

  // invite AAB to read in FN1
  foundation:FN1#read@subject:AAB
  // invite AAC to write in FN1
  foundation:FN1#write@subject:AAC

assertions:
  assertTrue:
    - foundation:FN1#view@subject:AAB
    - foundation:FN1#view@subject:AAC
    - foundation:FN1#edit@subject:AAC

  assertFalse:
    - foundation:FN1#edit@subject:AAB

validation:
  foundation:FN1#view:
    # owner should always be able to view
    - "[subject:AA] is <foundation:FN1#admin>"
    # read should be able to view
    - "[subject:AAB] is <foundation:FN1#read>"
    # write should be able to view
    - "[subject:AAC] is <foundation:FN1#write>"
  foundation:FN1#edit:
    # owner should always be able to edit
    - "[subject:AA] is <foundation:FN1#admin>"
    # write should be able to edit
    - "[subject:AAC] is <foundation:FN1#write>"

