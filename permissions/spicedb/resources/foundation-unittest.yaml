#  https://authzed.com/docs/spicedb/modeling/validation-testing-debugging#zed-validate

schemaFile: "./schema.zed"
relationships: |-  # Set up workspace w1 with owner AA and member AAB
  workspace:w1#tenant@tenant:tenant1
  workspace:w1#owner@subject:AA
  // AAB has collections in w1 so can view collections
  workspace:w1#collections@subject:AAB  
  
  // Global foundation FN1 is under workspace w1
  foundation:FN1#parent@workspace:w1 
  foundation:FN1#owner@subject:AA
  foundation:FN1#tenant@tenant:tenant1
  foundation:FN1#workspace@workspace:w1

  // these should propagate to FN2

  // invite AAB to read in FN1
  foundation:FN1#read@subject:AAB
  // invite AAC to write in FN1
  foundation:FN1#write@subject:AAC

  // Foundation FN2 is under global foundation FN1
  // FN2 inherits from FN1 so permissions are inherited
  foundation:FN2#parent@foundation:FN1 
  foundation:FN2#inherit_from@foundation:FN1 
  foundation:FN2#owner@subject:AA
  foundation:FN2#tenant@tenant:tenant1
  foundation:FN2#workspace@workspace:w1


  // Foundation FN3A and FN3B are under foundation FN2
  // they do not inherit so permissions are not inherited
  
  foundation:FN3A#parent@foundation:FN2 
  foundation:FN3A#owner@subject:AA
  foundation:FN3A#tenant@tenant:tenant1
  foundation:FN3A#read@subject:AAD
  foundation:FN3A#workspace@workspace:w1

  foundation:FN3B#parent@foundation:FN2 
  foundation:FN3B#owner@subject:AA
  foundation:FN3B#tenant@tenant:tenant1
  foundation:FN3B#read@subject:AAE
  foundation:FN3B#workspace@workspace:w1

assertions:
  assertTrue:
    - workspace:w1#owner@subject:AA
    - foundation:FN1#owner@subject:AA
    - foundation:FN1#view@subject:AA
    - foundation:FN2#owner@subject:AA
    - foundation:FN2#view@subject:AA
    - foundation:FN3A#owner@subject:AA
    - foundation:FN3A#view@subject:AA
    - foundation:FN3B#owner@subject:AA
    - foundation:FN3B#view@subject:AA

    - workspace:w1#has_collections@subject:AAB
    - workspace:w1#view@subject:AAB

    - foundation:FN1#view@subject:AAB
    - foundation:FN2#view@subject:AAB

    - foundation:FN1#view@subject:AAC
    - foundation:FN2#view@subject:AAC

    - foundation:FN3A#view@subject:AAD
    - foundation:FN3B#view@subject:AAE

  assertFalse:
    - workspace:w1#owner@subject:AAB
    - workspace:w1#has_configuration@subject:AAB
    - workspace:w1#has_settings@subject:AAB

    - foundation:FN3A#view@subject:AAB
    - foundation:FN3B#view@subject:AAB

    - foundation:FN3A#view@subject:AAC
    - foundation:FN3B#view@subject:AAC

    - foundation:FN1#view@subject:AAD
    - foundation:FN1#view@subject:AAE
    - foundation:FN2#view@subject:AAD
    - foundation:FN2#view@subject:AAE
validation:
  workspace:w1#view:
    - "[subject:AA] is <workspace:w1#owner>"
    - "[subject:AAB] is <workspace:w1#collections>"
  workspace:w1#has_collections:
    - "[subject:AA] is <workspace:w1#owner>"
    - "[subject:AAB] is <workspace:w1#collections>"

