#  https://authzed.com/docs/spicedb/modeling/validation-testing-debugging#zed-validate

# Run from `permissions/spicedb` directory with:
#  docker run -v ./resources/:/tmp/resources/ -w /tmp/resources authzed/zed validate ./foundation-view-and-edit.yaml
schemaFile: "./schema.zed"
relationships: |-
  form:form_1#admin@subject:AA
  // form 1 is public
  form:form_1#public@subject:*

  // form 2 is private
  form:form_2#admin@subject:AA
  form:form_2#read@subject:user_1

assertions:
  assertTrue:
    - form:form_1#view@subject:AA
    - form:form_1#edit@subject:AA
    - form:form_1#manage@subject:AA

    # form 1 is public
    - form:form_1#view@subject:user_1
    - form:form_1#view@subject:user_2

  assertFalse:
    # form 2 is public
    - form:form_2#view@subject:user_2

validation:
  form:form_1#view:
    # owner should always be able to view
    - "[subject:AA] is <form:form_1#admin>"
    # read should be able to view
    - "[subject:*] is <form:form_1#public>"
  form:form_1#edit:
    # owner should always be able to edit
    - "[subject:AA] is <form:form_1#admin>"

