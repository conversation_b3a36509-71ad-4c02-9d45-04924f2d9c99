#  https://authzed.com/docs/spicedb/modeling/validation-testing-debugging#zed-validate

# Run from `permissions/spicedb` directory with:
#  docker run -v ./resources/:/tmp/resources/ -w /tmp/resources authzed/zed validate ./foundation-groups-unittest.yaml
schemaFile: "./schema.zed"
relationships: |-
  group:group_1view#member@subject:AABv
  group:group_1edit#member@subject:AABe
  group:group_1admin#member@subject:AABa
  
  foundation:foundation_1#admin@subject:AA
  foundation:foundation_1#read@group:group_1view
  foundation:foundation_1#write@group:group_1edit
  foundation:foundation_1#admin@group:group_1admin

  foundation:foundation_2#read@group:group_2

assertions:
  assertTrue:
    - foundation:foundation_1#view@subject:AABv
    - foundation:foundation_1#edit@subject:AABe
    - foundation:foundation_1#manage@subject:AABa

  assertFalse:
    - foundation:foundation_1#view@subject:AAC
    - foundation:foundation_1#edit@subject:AAB
    - foundation:foundation_2#view@subject:AA

validation:
  foundation:foundation_1#view:
    - "[subject:AA] is <foundation:foundation_1#admin>"
    - "[subject:AABv] is <group:group_1view#member>"
    - "[group:group_1view] is <foundation:foundation_1#read>"
    - "[group:group_1edit] is <foundation:foundation_1#write>"
    - "[group:group_1admin] is <foundation:foundation_1#admin>"
  foundation:foundation_1#edit:
    - "[subject:AA] is <foundation:foundation_1#admin>"
    - "[subject:AABe] is <group:group_1edit#member>"
    - "[group:group_1edit] is <foundation:foundation_1#write>"
    - "[group:group_1admin] is <foundation:foundation_1#admin>"
  foundation:foundation_1#admin:
    - "[subject:AA] is <foundation:foundation_1#admin>"
    - "[group:group_1admin] is <foundation:foundation_1#admin>"

