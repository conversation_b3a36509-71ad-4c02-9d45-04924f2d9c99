#  https://authzed.com/docs/spicedb/modeling/validation-testing-debugging#zed-validate

schemaFile: "./schema.zed"
relationships: |-
  workspace:w1#collection@subject:AA
  workspace:w1#configuration@subject:AA
  workspace:w1#settings@subject:AA
  workspace:w1#configuration@subject:AAB
  workspace:w2#collection@subject:BB
assertions:
  assertTrue:
    - workspace:w1#collection@subject:AA
    - workspace:w1#has_collection@subject:AA
    - workspace:w1#has_configuration@subject:AA
    - workspace:w1#has_settings@subject:AA
    - workspace:w1#view@subject:AAB
    - workspace:w1#has_configuration@subject:AAB
  assertFalse:
    - workspace:w1#collection@subject:AAB
    - workspace:w1#has_settings@subject:AAB
    - workspace:w1#has_settings@subject:BB
    - workspace:w1#has_configuration@subject:BB
    - workspace:w1#has_collection@subject:BB

    - workspace:w2#collection@subject:AA
    - workspace:w2#view@subject:AA
validation:
  workspace:w1#view:
    - "[subject:AA] is <workspace:w1#configuration>/<workspace:w1#collection>/<workspace:w1#settings>"
    - "[subject:AAB] is <workspace:w1#configuration>"
  workspace:w1#has_collection:
    - "[subject:AA] is <workspace:w1#collection>"
  workspace:w1#has_configuration:
    - "[subject:AAB] is <workspace:w1#configuration>"
    - "[subject:AA] is <workspace:w1#configuration>"
  workspace:w2#view:
    - "[subject:BB] is <workspace:w2#collection>"