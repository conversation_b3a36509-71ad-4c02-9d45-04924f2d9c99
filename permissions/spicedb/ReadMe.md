# SpiceDB Deployment with Chainguard Images: Notes & Workflow

## Background

- **Chainguard images** for SpiceDB are minimal and do not include a shell, so shell scripts cannot be run inside the
  container.
- To execute commands (like migrations and serving), a **Go program** is used as a wrapper. Go is chosen because it
  compiles to a standalone executable, requiring no shell or runtime dependencies—making it secure and portable.

## Build Process

1. **Builder Image**: The Dockerfile uses a builder image to compile the Go program.
2. **Copy Executable**: The compiled Go executable is copied into the SpiceDB container.
3. **Override Entrypoint**: The container's original entrypoint is overridden to run the Go wrapper.

## Running the Container

Run the container with environment variables for configuration:

```
docker run --rm -p 50051:50051 \
  -e SPICEDB_DATASTORE_ENGINE="postgres" \
  -e SPICEDB_DATASTORE_CONNECTION_STRING="postgres://..." \
  -e SPICEDB_AUTH_TOKEN="development-token" \
  otai_spicedb
```

- The Go wrapper will handle migration and then start the SpiceDB server.

## Environment Variable Injection

- The Go program reads configuration from system environment variables and passes these through to the SpiceDB
  binary.
- Set these variables in your orchestration layer (e.g., App Service, CI/CD pipeline) to inject secrets and
  configuration.
- No need to declare them in the Dockerfile if you set them at runtime.

## Run locally

Note that once you have your database set up, you can run this from the startup convenience scripts in otai devops.
See
the [otai-devops repo](https://bitbucket.org/devops-martinit/oneteam-ai-devops/src/main/localDev/startup/start-spicedb.sh).

### Clean up

If you are running this multiple times, you may want to clean up the old database - connect as otai_superuser and
run:

```sql
DROP DATABASE otai_spicedb;
REVOKE ALL PRIVILEGES ON ALL TABLES IN SCHEMA public FROM spicedb_user;
REVOKE ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public FROM spicedb_user;
REVOKE ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public FROM spicedb_user;
REVOKE USAGE ON SCHEMA public FROM spicedb_user;
DROP USER spicedb_user;
DROP USER spicedb_superuser;
```

### Set up Postgres database and user:

The will create two users:

- `spicedb_superuser` - used for migrations
- `spicedb_user` - used for normal operations

Right click to run this SQL to create the database and user - connect as otai_superuser and
run:

```sql
CREATE DATABASE otai_spicedb;
CREATE USER "spicedb_superuser" WITH LOGIN SUPERUSER ENCRYPTED PASSWORD 'spicedb_superuser';
CREATE USER "spicedb_user" WITH ENCRYPTED PASSWORD 'spicedb_user';
GRANT ALL PRIVILEGES ON DATABASE otai_spicedb TO spicedb_superuser;
GRANT CONNECT ON DATABASE otai_spicedb TO spicedb_user;
```

Now connect to the new database as spicedb_superuser and set up permissions:

```sql
GRANT USAGE, CREATE ON SCHEMA public TO spicedb_superuser;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO spicedb_superuser;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO spicedb_superuser;

GRANT USAGE ON SCHEMA public TO spicedb_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO spicedb_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO spicedb_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
    GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO spicedb_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public
    GRANT USAGE, SELECT ON SEQUENCES TO spicedb_user;
```

### Build the image

From the permissions/spicedb folder, run:

```shell
docker build -t otai_spicedb ./docker
```

### Run the container

```shell
docker run --rm \
 -p 50051:50051 \
 -p 9999:9090 \
 -p 9998:8443 \
 -e SPICEDB_DATASTORE_ENGINE="postgres" \
 -e SPICEDB_MIGRATION_DATASTORE_CONNECTION_STRING="postgres://spicedb_superuser:<EMAIL>:5432/otai_spicedb?sslmode=disable" \
 -e SPICEDB_DATASTORE_CONNECTION_STRING="postgres://spicedb_user:<EMAIL>:5432/otai_spicedb?sslmode=disable" \
 -e SPICEDB_AUTH_TOKEN="development-token" \
 otai_spicedb
```

These ports are mapped:

- http://localhost:9998/healthz
- http://localhost:9999/metrics
- plus the gRPC port 50051 which we use via the client

When the container starts, you should see output like this:

```
% docker run --rm -p 50051:50051  \
>  -e SPICEDB_DATASTORE_ENGINE="postgres" \
>  -e SPICEDB_MIGRATION_DATASTORE_CONNECTION_STRING="postgres://spicedb_superuser:<EMAIL>:5432/otai_spicedb?sslmode=disable" \
>  -e SPICEDB_DATASTORE_CONNECTION_STRING="postgres://spicedb_user:<EMAIL>:5432/otai_spicedb?sslmode=disable" \
>  -e SPICEDB_AUTH_TOKEN="development-token" \
>  otai_spicedb
Running spicedb migrate head...
{"level":"info","format":"auto","log_level":"info","provider":"zerolog","async":false,"time":"2025-08-25T01:44:07Z","message":"configured logging"}
{"level":"info","time":"2025-08-25T01:44:07Z","GOMEMLIMIT":**********,"package":"github.com/KimMachineGun/automemlimit/memlimit","previous":9223372036854775807,"time":"2025-08-25T01:44:07Z","message":"GOMEMLIMIT is updated"}
{"level":"info","v":0,"provider":"none","endpoint":"","service":"spicedb","insecure":false,"sampleRatio":0.01,"time":"2025-08-25T01:44:07Z","message":"configured opentelemetry tracing"}
{"level":"warn","this-version":"v1.45.1","latest-released-version":"v1.45.2","time":"2025-08-25T01:44:08Z","message":"this version of SpiceDB is out of date. See: https://github.com/authzed/spicedb/releases/tag/v1.45.2"}
{"level":"info","time":"2025-08-25T01:44:08Z","message":"migrating postgres datastore"}
{"level":"info","details-url":"https://spicedb.dev/d/query-exec-mode","time":"2025-08-25T01:44:08Z","message":"found default_query_exec_mode in DB URI; leaving as-is"}
{"level":"info","targetRevision":"head","time":"2025-08-25T01:44:08Z","message":"running migrations"}
{"level":"info","targetRevision":"head","time":"2025-08-25T01:44:08Z","message":"server already at requested revision"}
Migration completed successfully
Starting spicedb serve...
{"level":"info","format":"auto","log_level":"info","provider":"zerolog","async":false,"time":"2025-08-25T01:44:08Z","message":"configured logging"}
{"level":"info","time":"2025-08-25T01:44:08Z","GOMEMLIMIT":**********,"package":"github.com/KimMachineGun/automemlimit/memlimit","previous":9223372036854775807,"time":"2025-08-25T01:44:08Z","message":"GOMEMLIMIT is updated"}
{"level":"info","v":0,"provider":"none","endpoint":"","service":"spicedb","insecure":false,"sampleRatio":0.01,"time":"2025-08-25T01:44:08Z","message":"configured opentelemetry tracing"}
{"level":"warn","this-version":"v1.45.1","latest-released-version":"v1.45.2","time":"2025-08-25T01:44:08Z","message":"this version of SpiceDB is out of date. See: https://github.com/authzed/spicedb/releases/tag/v1.45.2"}
{"level":"info","time":"2025-08-25T01:44:08Z","message":"using postgres datastore engine"}
{"level":"info","details-url":"https://spicedb.dev/d/query-exec-mode","time":"2025-08-25T01:44:08Z","message":"found default_query_exec_mode in DB URI; leaving as-is"}
{"level":"info","interval":"5s","time":"2025-08-25T01:44:08Z","message":"starting revision heartbeat"}
{"level":"info","interval":180000,"time":"2025-08-25T01:44:08Z","message":"datastore garbage collection worker started"}
{"level":"info","maxCost":"32 MiB","numCounters":1000,"defaultTTL":0,"time":"2025-08-25T01:44:08Z","message":"configured namespace cache"}
{"level":"info","time":"2025-08-25T01:44:08Z","message":"schema watch explicitly disabled"}
{"level":"info","maxCost":"1.3 GiB","numCounters":10000,"defaultTTL":20600,"time":"2025-08-25T01:44:08Z","message":"configured dispatch cache"}
{"level":"info","concurrency-limit-check-permission":50,"concurrency-limit-lookup-resources":50,"concurrency-limit-lookup-subjects":50,"concurrency-limit-reachable-resources":50,"balancerconfig":{"loadBalancingConfig":[{"consistent-hashring":{"replicationFactor":100,"spread":1}}]},"time":"2025-08-25T01:44:08Z","message":"configured dispatcher"}
{"level":"info","addr":":50051","network":"tcp","service":"grpc","workers":0,"insecure":true,"time":"2025-08-25T01:44:08Z","message":"grpc server started serving"}
{"level":"info","ClusterDispatchCacheConfig.CacheKindForTesting":"(empty)","ClusterDispatchCacheConfig.Enabled":true,"ClusterDispatchCacheConfig.MaxCost":"70%","ClusterDispatchCacheConfig.Metrics":true,"ClusterDispatchCacheConfig.Name":"cluster_dispatch","ClusterDispatchCacheConfig.NumCounters":100000,"Datastore":"nil","DatastoreConfig.AllowedMigrations":"(slice of size 0)","DatastoreConfig.BootstrapFileContents":"(map of size 0)","DatastoreConfig.BootstrapFiles":"[]","DatastoreConfig.BootstrapOverwrite":false,"DatastoreConfig.BootstrapTimeout":10000,"DatastoreConfig.ConnectRate":100,"DatastoreConfig.CredentialsProviderName":"(empty)","DatastoreConfig.DisableStats":false,"DatastoreConfig.EnableConnectionBalancing":true,"DatastoreConfig.EnableDatastoreMetrics":true,"DatastoreConfig.EnableExperimentalRelationshipExpiration":false,"DatastoreConfig.EnableRevisionHeartbeat":false,"DatastoreConfig.Engine":"postgres","DatastoreConfig.ExperimentalColumnOptimization":true,"DatastoreConfig.FollowerReadDelay":4800,"DatastoreConfig.GCInterval":180000,"DatastoreConfig.GCMaxOperationTime":60000,"DatastoreConfig.GCWindow":86400000,"DatastoreConfig.IncludeQueryParametersInTraces":false,"DatastoreConfig.LegacyFuzzing":-0.000001,"DatastoreConfig.MaxRetries":10,"DatastoreConfig.MaxRevisionStalenessPercent":0.1,"DatastoreConfig.MigrationPhase":"(empty)","DatastoreConfig.OverlapKey":"key","DatastoreConfig.OverlapStrategy":"static","DatastoreConfig.ReadConnPool.HealthCheckInterval":30000,"DatastoreConfig.ReadConnPool.MaxIdleTime":1800000,"DatastoreConfig.ReadConnPool.MaxLifetime":1800000,"DatastoreConfig.ReadConnPool.MaxLifetimeJitter":0,"DatastoreConfig.ReadConnPool.MaxOpenConns":20,"DatastoreConfig.ReadConnPool.MinOpenConns":20,"DatastoreConfig.ReadOnly":false,"DatastoreConfig.ReadReplicaConnPool.HealthCheckInterval":30000,"DatastoreConfig.ReadReplicaConnPool.MaxIdleTime":1800000,"DatastoreConfig.ReadReplicaConnPool.MaxLifetime":1800000,"DatastoreConfig.ReadReplicaConnPool.MaxLifetimeJitter":0,"DatastoreConfig.ReadReplicaConnPool.MaxOpenConns":20,"DatastoreConfig.ReadReplicaConnPool.MinOpenConns":20,"DatastoreConfig.ReadReplicaCredentialsProviderName":"(empty)","DatastoreConfig.ReadReplicaURIs":"(sensitive)","DatastoreConfig.RelationshipIntegrityCurrentKey.KeyFilename":"(empty)","DatastoreConfig.RelationshipIntegrityCurrentKey.KeyID":"(empty)","DatastoreConfig.RelationshipIntegrityEnabled":false,"DatastoreConfig.RelationshipIntegrityExpiredKeys":"(slice of size 0)","DatastoreConfig.RelaxedIsolationLevel":false,"DatastoreConfig.RequestHedgingEnabled":false,"DatastoreConfig.RequestHedgingInitialSlowValue":10,"DatastoreConfig.RequestHedgingMaxRequests":1000000,"DatastoreConfig.RequestHedgingQuantile":0.95,"DatastoreConfig.RevisionQuantization":5000,"DatastoreConfig.SpannerCredentialsFile":"(empty)","DatastoreConfig.SpannerCredentialsJSON":"(sensitive)","DatastoreConfig.SpannerDatastoreMetricsOption":"otel","DatastoreConfig.SpannerEmulatorHost":"(empty)","DatastoreConfig.SpannerMaxSessions":400,"DatastoreConfig.SpannerMinSessions":100,"DatastoreConfig.TablePrefix":"(empty)","DatastoreConfig.URI":"(sensitive)","DatastoreConfig.WatchBufferLength":1024,"DatastoreConfig.WatchBufferWriteTimeout":1000,"DatastoreConfig.WatchConnectTimeout":1000,"DatastoreConfig.WriteConnPool.HealthCheckInterval":30000,"DatastoreConfig.WriteConnPool.MaxIdleTime":1800000,"DatastoreConfig.WriteConnPool.MaxLifetime":1800000,"DatastoreConfig.WriteConnPool.MaxLifetimeJitter":0,"DatastoreConfig.WriteConnPool.MaxOpenConns":10,"DatastoreConfig.WriteConnPool.MinOpenConns":10,"DisableGRPCLatencyHistogram":false,"DisableV1SchemaAPI":false,"DisableVersionResponse":false,"DispatchCacheConfig.CacheKindForTesting":"(empty)","DispatchCacheConfig.Enabled":true,"DispatchCacheConfig.MaxCost":"30%","DispatchCacheConfig.Metrics":true,"DispatchCacheConfig.Name":"dispatch","DispatchCacheConfig.NumCounters":10000,"DispatchChunkSize":100,"DispatchClientMetricsEnabled":true,"DispatchClientMetricsPrefix":"(empty)","DispatchClusterMetricsEnabled":true,"DispatchClusterMetricsPrefix":"(empty)","DispatchConcurrencyLimits.Check":0,"DispatchConcurrencyLimits.LookupResources":0,"DispatchConcurrencyLimits.LookupSubjects":0,"DispatchConcurrencyLimits.ReachableResources":0,"DispatchHashringReplicationFactor":100,"DispatchHashringSpread":1,"DispatchMaxDepth":50,"DispatchSecondaryMaximumPrimaryHedgingDelays":"(map of size 0)","DispatchSecondaryUpstreamAddrs":"(map of size 0)","DispatchSecondaryUpstreamExprs":"(map of size 0)","DispatchServer.Address":":50053","DispatchServer.BufferSize":0,"DispatchServer.ClientCAPath":"(empty)","DispatchServer.Enabled":false,"DispatchServer.MaxConnAge":30000,"DispatchServer.MaxWorkers":0,"DispatchServer.Network":"tcp","DispatchServer.TLSCertPath":"(empty)","DispatchServer.TLSKeyPath":"(empty)","DispatchUpstreamAddr":"(empty)","DispatchUpstreamCAPath":"(empty)","DispatchUpstreamTimeout":60000,"Dispatcher":"nil","EnableExperimentalLookupResources":true,"EnableExperimentalRelationshipExpiration":false,"EnableExperimentalWatchableSchemaCache":false,"EnablePerformanceInsightMetrics":false,"EnableRequestLogs":false,"EnableResponseLogs":false,"EnableRevisionHeartbeat":true,"GRPCAuthFunc":"(value)","GRPCServer.Address":":50051","GRPCServer.BufferSize":1048576,"GRPCServer.ClientCAPath":"(empty)","GRPCServer.Enabled":true,"GRPCServer.MaxConnAge":30000,"GRPCServer.MaxWorkers":0,"GRPCServer.Network":"tcp","GRPCServer.TLSCertPath":"(empty)","GRPCServer.TLSKeyPath":"(empty)","GlobalDispatchConcurrencyLimit":50,"HTTPGateway.HTTPAddress":":8443","HTTPGateway.HTTPEnabled":false,"HTTPGateway.HTTPTLSCertPath":"(empty)","HTTPGateway.HTTPTLSKeyPath":"(empty)","HTTPGatewayCorsAllowedOrigins":"[*]","HTTPGatewayCorsEnabled":false,"HTTPGatewayUpstreamAddr":":50051","HTTPGatewayUpstreamTLSCertPath":"(empty)","MaxBulkExportRelationshipsLimit":10000,"MaxCaveatContextSize":4096,"MaxDatastoreReadPageSize":1000,"MaxDeleteRelationshipsLimit":1000,"MaxLookupResourcesLimit":1000,"MaxReadRelationshipsLimit":1000,"MaxRelationshipContextSize":25000,"MaximumPreconditionCount":1000,"MaximumUpdatesPerWrite":1000,"MetricsAPI.HTTPAddress":":9090","MetricsAPI.HTTPEnabled":true,"MetricsAPI.HTTPTLSCertPath":"(empty)","MetricsAPI.HTTPTLSKeyPath":"(empty)","NamespaceCacheConfig.CacheKindForTesting":"(empty)","NamespaceCacheConfig.Enabled":true,"NamespaceCacheConfig.MaxCost":"32MiB","NamespaceCacheConfig.Metrics":true,"NamespaceCacheConfig.Name":"namespace","NamespaceCacheConfig.NumCounters":1000,"PresharedSecureKey":"(sensitive)","SchemaPrefixesRequired":false,"SchemaWatchHeartbeat":1000,"ServerName":"(empty)","ShutdownGracePeriod":0,"SilentlyDisableTelemetry":false,"StreamingAPITimeout":30000,"TelemetryCAOverridePath":"(empty)","TelemetryEndpoint":"https://telemetry.authzed.com","TelemetryInterval":3600000,"V1SchemaAdditiveOnly":false,"WatchHeartbeat":1000,"time":"2025-08-25T01:44:08Z","message":"configuration"}
{"level":"info","datastore":"*schemacaching.definitionCachingProxy","time":"2025-08-25T01:44:08Z","message":"running server"}
{"level":"info","interval":"1h0m0s","endpoint":"https://telemetry.authzed.com","next":"4m25s","time":"2025-08-25T01:44:08Z","message":"telemetry reporter scheduled"}
{"level":"info","addr":":9090","service":"metrics","insecure":true,"time":"2025-08-25T01:44:08Z","message":"http server started serving"}
{"level":"info","details-url":"https://spicedb.dev/d/query-exec-mode","time":"2025-08-25T01:44:08Z","message":"found default_query_exec_mode in DB URI; leaving as-is"}
{"level":"info","interval":"5s","time":"2025-08-25T01:44:08Z","message":"got elected revision heartbeat leader, starting"}
```

## Other reading

- https://authzed.com/docs/best-practices
- https://medium.com/iaminsight/a-comprehensive-guide-to-setting-up-spicedb-with-postgresql-and-a-monitoring-stack-b250f31d7775

## Notes

- A principal we want to adhere to is that our database will contain all the information needed to rebuild the spicedb
  database, hence we keep WorkspaceUsers as it is, but it is not referenced in RLS.
  We will need to ensure we store such information as we go.

- With Rebac systems it’s important to note that after doing a write we need to wait for consistency before reading.
  Writes return tokens that you can use in subsequent reads to ensure this. You can also do a read “fully consistent” -
  this is used when a token is not given.

```kotlin
fun buildConsistencyToken(token: String?): Consistency {
    return if (token != null) {
        Consistency.newBuilder().setAtLeastAsFresh(ZedToken.newBuilder().setToken(token)).build()
    } else {
        Consistency.newBuilder().setFullyConsistent(true).build()
    }
}
```

- There are unit tests for our spiceddb schema in `./resources`

- Main classes:
    - SpiceDbRepository handles reading and writing to spicedb
        - Implements RebacRepository- if we were to use permify instead of spicedb we’d just reimplement this interface
        - SubjectResourcePermissionsRepository - repository for managing the subject_resource_permissions table
        - LocalRLSIndexSyncService - uses the RebacRepository and SubjectResourcePermissionsRepository to sync the
          resources a subject can view across to SubjectResourcePermissionsRepository
            - syncViewBySubject - replaces all the rows in SubjectResourcePermissionsRepository for a subject by
              querying the RebacRepository for everything the subject can view and inserts into
              SubjectResourcePermissionsRepository
            - syncViewByResource - replaces all the rows in SubjectResourcePermissionsRepository for a given resource
        - RelationshipRepository - a thin layer over RebacRepository that knows what relationships to manage for our
          application domain.
        - PermissionsService - the main entry point for application code - orchestrates
          SubjectResourcePermissionsRepository, RelationshipRepository and LocalRLSIndexSyncService
        - CreateResourcePermissionRequest contains command objects for each of the create operations
