plugins {
    id("project.conventions")
    id("java-test-fixtures")
}

group = "services.oneteam.ai.permissions"
version = "0.0.1"

dependencies {
    api("com.authzed.api:authzed:1.4.1")
    api("io.grpc:grpc-api:1.75.0")
    api("io.grpc:grpc-stub:1.75.0")
    api(libs.postgresql)
    api(libs.hikari)

    implementation(ktorLibs.serialization.kotlinx.json)

    api(libs.logback.classic)

    testImplementation(kotlin("test"))
    testImplementation(libs.bundles.kotest)
    testImplementation(libs.mockito)
    testImplementation(libs.mockk)
    testImplementation(libs.assertj.core)
    testImplementation(libs.junit.jupiter.params)
    testFixturesApi(libs.test.containers)
    testFixturesApi(libs.tcpostgresql)
    testFixturesApi(libs.assertj.core)

}