package services.oneteam.ai.permissions.spicedb

import io.grpc.ManagedChannelBuilder
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.permissions.rebac.spicedb.SpiceDbRepository
import java.util.concurrent.atomic.AtomicLong
import kotlin.system.measureTimeMillis

// Caused by: io.grpc.StatusRuntimeException: ALREADY_EXISTS: could not CREATE relationship `foundation:1#write@subject:1`, as it already existed. If this is persistent, please switch to TOUCH operations or specify a precondition

@Disabled
class SpiceDbRepositoryTest {
    companion object {
        const val target = "localhost:50051"
        const val token = "development-token"

        val channel = ManagedChannelBuilder
            .forTarget(target)
//                .useTransportSecurity() // if not using TLS, replace with .usePlaintext()
            .usePlaintext() // if not using TLS, replace with .usePlaintext()
            .build()
        val spiceDbRepository = SpiceDbRepository(
            token,
            channel
        )

    }

    @Disabled
    @Test
    fun `schema should init`() {
        spiceDbRepository.updateSchema()
        val schema = spiceDbRepository.readSchema()
        assertThat(schema).isNotEmpty
        assertThat(schema.trim()).isEqualToIgnoringWhitespace(spiceDbRepository.loadSchemaFromClasspath().trim())

    }

    @Disabled
    @Test
    fun testWriteRelationship() {
//        spiceDbRepository.deleteAllRelationships("foundation")
//        spiceDbRepository.writeSchema(File("./spicedb/resources/schema.zed").readText())
        val writeTime = measureTimeMillis {
            for (x in 1..100000) {
                spiceDbRepository.writeRelationship(
                    PermissionStructure.Foundation.NAME, x.toString(),
                    PermissionStructure.Foundation.Relationships.WRITE, PermissionStructure.Subject.NAME, "2"
                )
            }
        }
        val readTime = measureTimeMillis {
            val allRelationshipsForUser =
                spiceDbRepository.findAllRelationshipsForUser(PermissionStructure.Subject.NAME, "2")
            println("All relationships for user: ${allRelationshipsForUser.size}")
        }
//        println("Write time: $writeTime ms")
        println("Read time: $readTime ms")
    }

    @Disabled
    @Test
    fun testWriteNestedRelationship() {
//        spiceDbRepository.deleteAllRelationships("foundation")
//        spiceDbRepository.writeSchema(File("./spicedb/resources/schema.zed").readText())

        val tenant = "10"
        val workspace = "23"
        val subject = "40"
        val readSubject = "50"
        val foundationId = AtomicLong()
        /*
                val writeTime = measureTimeMillis {
                    for (x in 1..10) {
                        val xid = "x-${foundationId.incrementAndGet()}"
                        spiceDbRepository.writeRelationship("foundation", xid, "tenant", "tenant", tenant)
                        spiceDbRepository.writeRelationship("foundation", xid, "workspace", "workspace", workspace)
                        spiceDbRepository.writeRelationship("foundation", xid, "write", "subject", subject)
                        spiceDbRepository.writeRelationship("foundation", xid, "read", "subject", readSubject)

                        for (y in 1..2) {
                            val yid = "$xid-y-${foundationId.incrementAndGet()}"
                            spiceDbRepository.writeRelationship("foundation", yid, "tenant", "tenant", tenant)
                            spiceDbRepository.writeRelationship("foundation", yid, "workspace", "workspace", workspace)
                            spiceDbRepository.writeRelationship(
                                "foundation",
                                yid,
                                "parent",
                                "foundation",
                                xid
                            )
                            spiceDbRepository.writeRelationship(
                                "foundation",
                                yid,
                                "inherit_from",
                                "foundation",
                                xid
                            )

                            for (z in 1..20) {
                                val zid = "$yid-z-${foundationId.incrementAndGet()}"
                                spiceDbRepository.writeRelationship("foundation", zid, "tenant", "tenant", tenant)
                                spiceDbRepository.writeRelationship(
                                    "foundation",
                                    zid,
                                    "workspace",
                                    "workspace",
                                    workspace
                                )
                                spiceDbRepository.writeRelationship(
                                    "foundation",
                                    zid,
                                    "parent",
                                    "foundation",
                                    yid
                                )
                                spiceDbRepository.writeRelationship(
                                    "foundation",
                                    zid,
                                    "inherit_from",
                                    "foundation",
                                    yid
                                )
                            }
                        }
                    }
                }

                for (x in 1..5) {
                    val readTime = measureTimeMillis {
                        val allRelationshipsForUser =
                            spiceDbRepository.lookupResources("foundation", "view", "subject", subject)
                        println("All relationships for subject $subject: ${allRelationshipsForUser.size}")
                    }
                    val readTime2 = measureTimeMillis {
                        val allRelationshipsForUser =
                            //                    spiceDbRepository.lookupResources("foundation", "view", "subject", readSubject)
                            spiceDbRepository.getAllRelationshipsForUser("subject", readSubject)
                        println("All relationships for subject $readSubject: ${allRelationshipsForUser.size}")
                    }
                    val readTime3 = measureTimeMillis {
                        val allRelationshipsForUser =
                            //                    spiceDbRepository.lookupResources("foundation", "view", "subject", readSubject)
                            spiceDbRepository.getAllRelationshipsForUser("subject", readSubject, "1")
                        println("All relationships starting with 1 for subject $readSubject: ${allRelationshipsForUser.size}")
                    }
                    //        println("Write time: $writeTime ms")
                    println("Read time for subject $subject: $readTime ms")
                    println("Read time for subject $readSubject: $readTime2 ms")
                }

        */
        val fid = "x-1-y-2-z-3"

        spiceDbRepository.findAllSubjectsWithPermissionOnResource(
            PermissionStructure.Foundation.NAME,
            "20",
            PermissionStructure.Foundation.Permissions.VIEW,
        ).let {
            println("All subjects for foundation: $fid")
            it.forEach { relationship ->
                println(" - $relationship")
            }
        }

        spiceDbRepository.checkSubjectPermissionForResource(
            PermissionStructure.Foundation.NAME,
            "z-1",
            PermissionStructure.Foundation.Permissions.EDIT,
            PermissionStructure.Subject.NAME,
            readSubject,
        ).let {
            println("User $subject has permission: $it")
        }

    }
}

/*
All relationships for user: 1000
Write time: 3461 ms
Read time: 48 ms

All relationships for user: 8840
Write time: 27073 ms
Read time: 211 ms

All relationships for user: 19263
Write time: 58226 ms
Read time: 413 ms

All relationships for user: 38204
Write time: 116705 ms
Read time: 694 ms

All relationships for user: 59732
Write time: 168866 ms
Read time: 2838 ms

All relationships for user: 98743
Write time: 296331 ms
Read time: 1918 ms

 */


/*

nested

All relationships for user: 28
All relationships for user: 28
Write time: 12969 ms
Read time: 35 ms
Read time 2: 6 ms


All relationships for user: 120
All relationships for user: 120
Write time: 107705 ms
Read time: 62 ms
Read time 2: 18 ms


 */