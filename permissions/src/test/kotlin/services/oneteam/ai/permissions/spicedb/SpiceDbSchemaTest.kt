package services.oneteam.ai.permissions.spicedb

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import services.oneteam.ai.permissions.Docker
import java.io.File
import java.util.stream.Stream
import kotlin.io.path.Path

class SpiceDbSchemaTest {

    companion object {
        @JvmStatic
        fun yamlFilesProvider(): Stream<File> = Stream.of(
            Path("./spicedb/resources/collection-unittest.yaml").toFile(),
            Path("./spicedb/resources/workspace-unittest.yaml").toFile(),
            // Add more test files here as needed
        )
    }

    @ParameterizedTest
    @MethodSource("yamlFilesProvider")
    fun `schema should work`(yaml: File) {
        val container = Docker.Containers.SpiceDBZed.buildZedContainer(
            Path("./src/main/resources/services/oneteam/ai/permissions/spicedb/schema.zed").toFile(),
            yaml
        )
        container.start()
        while (container.isRunning) {
            Thread.sleep(500)
        }
        println(container.logs)
        assertThat(container.isRunning).isFalse()
        assertThat(container.logs).contains("Success! - ")
        assertThat(container.logs).doesNotContain("error: ")
    }
}