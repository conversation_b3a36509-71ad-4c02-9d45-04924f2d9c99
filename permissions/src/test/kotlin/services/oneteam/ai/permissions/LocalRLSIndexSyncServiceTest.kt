package services.oneteam.ai.permissions

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import services.oneteam.ai.permissions.rebac.Relationship
import services.oneteam.ai.permissions.rebac.Resource
import services.oneteam.ai.permissions.rebac.ResourceTypeIds

class LocalRLSIndexSyncServiceTest {

    /*
    sync the relationships to the local RLS index
    validate the local RLS index against the ReBAC system
    */
    @Test
    fun `sync relationships to local RLS index`() {
        DockerOrchestration.start()

        val dataSource = DockerOrchestration.buildHikariDataSource()
        val fixtures = Fixtures(dataSource)

        val resourceTypeId = ResourceTypeIds.FOUNDATION

        with(fixtures) {
            rebacRepository.updateSchema()

            // create relationships in SpiceDB using the SpiceDbRepository
            rebacRepository.writeRelationship(
                Resource("foundation"), "1", Relationship("read"), Resource("subject"), "1"
            )

            // sync the relationships to the local RLS index
            localRLSIndexSyncService.syncViewBySubject(
                tenantId = 1L,
                subjectId = 1L,
            )

            val localIndexForSubject = localIndexRepository.findAllForSubject(
                resourceTypeId,
                subjectId = 1L,
                tenantId = 1L
            )
            println(localIndexForSubject)
            assertThat(localIndexForSubject).isNotEmpty()

            // TODO finish this test off.
        }
    }
}