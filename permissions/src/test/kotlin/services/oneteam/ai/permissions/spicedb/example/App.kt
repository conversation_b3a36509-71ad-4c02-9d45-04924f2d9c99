package services.oneteam.ai.permissions.spicedb.example

import com.authzed.api.v1.*
import com.authzed.api.v1.PermissionsServiceGrpc.PermissionsServiceBlockingStub
import com.authzed.grpcutil.BearerToken
import io.grpc.Channel
import io.grpc.ManagedChannelBuilder
import java.util.concurrent.TimeUnit
import java.util.logging.Level
import java.util.logging.Logger


// Installation
// https://search.maven.org/artifact/com.authzed.api/authzed
class App(channel: Channel?) {
    private val schemaService: SchemaServiceGrpc.SchemaServiceBlockingStub
    private val permissionsService: PermissionsServiceBlockingStub

    init {
        val bearerToken = BearerToken(token)
        schemaService = SchemaServiceGrpc.newBlockingStub(channel)
            .withCallCredentials(bearerToken)
        permissionsService = PermissionsServiceGrpc.newBlockingStub(channel)
            .withCallCredentials(BearerToken(token))
    }

    fun writeSchema(): String {
        logger.info("Writing schema...")
        val schema = """
                definition thelargeapp/article {
                	relation author: thelargeapp/user
                	relation commenter: thelargeapp/user

                	permission can_comment = commenter + author
                }

                definition thelargeapp/user {}
                
                """.trimIndent()

        val request = WriteSchemaRequest
            .newBuilder()
            .setSchema(schema)
            .build()

        val response: WriteSchemaResponse
        try {
            response = schemaService.writeSchema(request)
        } catch (e: Exception) {
            logger.log(Level.WARNING, "RPC failed: {0}", e.message)
            return ""
        }
        logger.info("Response: " + response.toString())
        return response.toString()
    }

    fun readSchema(): String {
        logger.info("Reading schema...")
        val request = ReadSchemaRequest
            .newBuilder()
            .build()

        val response: ReadSchemaResponse
        try {
            response = schemaService.readSchema(request)
        } catch (e: Exception) {
            logger.log(Level.WARNING, "RPC failed: {0}", e.message)
            return ""
        }
        logger.info(response.toString())
        return response.toString()
    }

    fun writeRelationship(): String {
        logger.info("Write relationship...")

        val request = WriteRelationshipsRequest.newBuilder()
            .addUpdates(
                RelationshipUpdate.newBuilder()
                    .setOperation(RelationshipUpdate.Operation.OPERATION_CREATE)
                    .setRelationship(
                        Relationship.newBuilder()
                            .setResource(
                                ObjectReference.newBuilder()
                                    .setObjectType("thelargeapp/article")
                                    .setObjectId("java_test")
                                    .build()
                            )
                            .setRelation("author")
                            .setSubject(
                                SubjectReference.newBuilder()
                                    .setObject(
                                        ObjectReference.newBuilder()
                                            .setObjectType("thelargeapp/user")
                                            .setObjectId("george")
                                            .build()
                                    )
                                    .build()
                            )
                            .build()
                    )
                    .build()
            )
            .build()

        val response: WriteRelationshipsResponse
        try {
            response = permissionsService.writeRelationships(request)
        } catch (e: Exception) {
            logger.log(Level.WARNING, "RPC failed: {0}", e.message)
            return ""
        }
        logger.info("Response: " + response.toString())
        return response.getWrittenAt().getToken()
    }

    fun check(zedToken: ZedToken?): CheckPermissionResponse.Permissionship? {
        logger.info("Checking...")

        val request = CheckPermissionRequest.newBuilder()
            .setConsistency(
                Consistency.newBuilder()
                    .setAtLeastAsFresh(zedToken)
                    .build()
            )
            .setResource(
                ObjectReference.newBuilder()
                    .setObjectType("thelargeapp/article")
                    .setObjectId("java_test")
                    .build()
            )
            .setSubject(
                SubjectReference.newBuilder()
                    .setObject(
                        ObjectReference.newBuilder()
                            .setObjectType("thelargeapp/user")
                            .setObjectId("george")
                            .build()
                    )
                    .build()
            )
            .setPermission("can_comment")
            .build()

        val response: CheckPermissionResponse
        try {
            response = permissionsService.checkPermission(request)
        } catch (e: Exception) {
            logger.log(Level.WARNING, "RPC failed: {0}", e.message)
            return null
        }
        logger.info("Response: " + response.toString())
        return response.getPermissionship()
    }

    companion object {
        private val logger: Logger = Logger.getLogger(App::class.java.getName())
        private const val target = "localhost:50051"
        private const val token = "somerandomkeyhere"

        @JvmStatic
        fun main(args: Array<String>) {
            val channel = ManagedChannelBuilder
                .forTarget(target)
//                .useTransportSecurity() // if not using TLS, replace with .usePlaintext()
                .usePlaintext() // if not using TLS, replace with .usePlaintext()
                .build()
            try {
                val client = App(channel)

                client.writeSchema()

                client.readSchema()

                val tokenVal = client.writeRelationship()

                val result = client.check(
                    ZedToken.newBuilder()
                        .setToken(tokenVal)
                        .build()
                )
                logger.log(Level.INFO, "Check result: {0}", result)
            } finally {
                try {
                    channel.shutdownNow().awaitTermination(5, TimeUnit.SECONDS)
                } catch (e: InterruptedException) {
                    // Uh oh!
                }
            }
        }
    }
}