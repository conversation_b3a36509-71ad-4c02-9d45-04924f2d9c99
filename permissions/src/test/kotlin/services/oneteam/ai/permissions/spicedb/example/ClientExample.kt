package services.oneteam.ai.permissions.spicedb.example

import com.authzed.api.v1.*
import com.authzed.grpcutil.BearerToken
import io.grpc.ManagedChannelBuilder

object ClientExample {
    @JvmStatic
    fun main(args: Array<String>) {
        val channel = ManagedChannelBuilder
            .forTarget("localhost:50051")
            .usePlaintext()
            .build()

        val bearerToken = BearerToken("somerandomkeyhere")
        val permissionsService = PermissionsServiceGrpc
            .newBlockingStub(channel)
            .withCallCredentials(bearerToken)


        val request = CheckPermissionRequest.newBuilder()
            .setConsistency(
                Consistency.newBuilder()
                    .setMinimizeLatency(true)
                    .build()
            )
            .setResource(
                ObjectReference.newBuilder()
                    .setObjectType("blog/post")
                    .setObjectId("1")
                    .build()
            )
            .setSubject(
                SubjectReference.newBuilder()
                    .setObject(
                        ObjectReference.newBuilder()
                            .setObjectType("blog/user")
                            .setObjectId("emilia")
                            .build()
                    )
                    .build()
            )
            .setPermission("read")
            .build()

        // Is Emilia in the set of users that can read post #1?
        try {
            val response = permissionsService.checkPermission(request)
            println("result: " + response.getPermissionship().getValueDescriptor().getName())
        } catch (e: Exception) {
            println("Failed to check permission: " + e.message)
        }
    }
}