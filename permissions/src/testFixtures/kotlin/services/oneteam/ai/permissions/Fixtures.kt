package services.oneteam.ai.permissions

import io.grpc.ManagedChannelBuilder
import services.oneteam.ai.permissions.rebac.LocalRLSIndexSyncService
import services.oneteam.ai.permissions.rebac.Resource
import services.oneteam.ai.permissions.rebac.SubjectResourcePermissionsRepository
import services.oneteam.ai.permissions.rebac.spicedb.SpiceDbRepository
import java.sql.Connection
import javax.sql.DataSource

class Fixtures(val dataSource: DataSource) {

    // these resource keys need to match the keys in the SpiceDB schema
    // see
    val resourceKeys = mapOf(Resource("foundation") to 1)

    val localIndexRepository =
        SubjectResourcePermissionsRepository {
            dataSource.connection as Connection
        }

    val rebacRepository = SpiceDbRepository(
        Docker.Containers.SpiceDB.KEY, ManagedChannelBuilder
            .forTarget("${Docker.HostUtils.dockerHostIP}:${Docker.Containers.SpiceDB.PORT}")
            .usePlaintext() // if not using TLS, replace with .usePlaintext()
            .build()
    )

    val localRLSIndexSyncService = LocalRLSIndexSyncService(
        rebacRepository,
        localIndexRepository,
        resourceKeys
    )

}