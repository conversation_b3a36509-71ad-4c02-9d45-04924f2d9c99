package services.oneteam.ai.permissions

import com.github.dockerjava.api.model.ExposedPort
import com.github.dockerjava.api.model.HostConfig
import com.github.dockerjava.api.model.PortBinding
import com.github.dockerjava.api.model.Ports
import com.zaxxer.hikari.HikariDataSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.utility.DockerImageName
import services.oneteam.ai.permissions.rebac.spicedb.CloseableChannel
import org.testcontainers.utility.MountableFile
import services.oneteam.ai.permissions.rebac.spicedb.SpiceDbChannelBuilder
import java.io.File
import java.net.InetAddress
import java.net.NetworkInterface
import java.time.Duration

val logger: Logger = LoggerFactory.getLogger("DockerHostUtils")

/**
 * Utility object to manage Docker orchestration for SpiceDB and PostgreSQL containers.
 * It starts the PostgreSQL container, runs the SpiceDB migration, and then starts the SpiceDB server.
 */
object DockerOrchestration {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun start(): PostgreSQLContainer<Nothing> {
        // Start PostgreSQL container
        logger.info("Starting postgres container...")
        val postgres = Docker.Containers.Postgres.build()
        postgres.start()
        showLogs("Postgres: ", postgres.logs)

        Thread.sleep(10000)
        // Start SpiceDB migration container
        logger.info("Starting spicedb migration container...")
        val spicedbMigration = Docker.Containers.SpiceDB.buildMigrationContainer(postgres)
        spicedbMigration.start()

        // wait for migration container to stop
        var retries = 0
        val maxRetries = 60 // Maximum retries to wait for migration to complete
        while (spicedbMigration.isRunning && retries < maxRetries) {
            Thread.sleep(100) // Wait for migration to complete
            retries++
        }
        showLogs("SpiceDB Migration: ", spicedbMigration.logs)
//        assertThat(spicedbMigration.logs).withFailMessage { "ERRORS were present while trying to migrate spicedb - check the logs above for information" }
//            .doesNotContain("error", "failed", "panic", "fatal")

        // Start SpiceDB server container
        logger.info("Starting spicedb server container...")
        val spicedbServer = Docker.Containers.SpiceDB.buildServerContainer(postgres)
        spicedbServer.start()
        showLogs("SpiceDB Server: ", spicedbServer.logs)

        require(postgres.isRunning) { "Postgres server failed to start" }
        require(spicedbServer.isRunning) { "SpiceDB server failed to start" }
        require(!spicedbMigration.isRunning) { "SpiceDB migration container should have completed and stopped" }

        // Print network info for debugging
        Docker.HostUtils.printNetworkInfo()

        return postgres
    }

    fun buildHikariDataSource(): HikariDataSource {
        return HikariDataSource().apply {
            jdbcUrl = Docker.Containers.Postgres.jdbcUrl
            username = Docker.Containers.Postgres.USERNAME
            password = Docker.Containers.Postgres.PASSWORD
            driverClassName = "org.postgresql.Driver"
            maximumPoolSize = 10
            minimumIdle = 2
            idleTimeout = 30000 // 30 seconds
            connectionTimeout = 30000 // 30 seconds
            validationTimeout = 5000 // 5 seconds
        }
    }

    private fun showLogs(name: String, logs: String) {
        logger.trace("$name logs:\n$logs")
    }
}

/**
 * Utility object to manage Docker containers and networks for testing.
 * It provides methods to build and configure PostgreSQL and SpiceDB containers,
 * as well as utility functions to get the Docker host IP and print network information.
 */
object Docker {
    val internalNetwork: Network = Network.newNetwork()

    object Images {
        const val POSTGRES = "postgres:16-alpine"
        const val SPICEDB_ZED = "authzed/zed:latest"
        const val SPICEDB = "authzed/spicedb:latest"
    }

    object Containers {
        object Postgres {
            const val DATABASE_NAME = "otai-dev-database"
            const val USERNAME = "test-user"
            const val PASSWORD = "test-password"
            const val CONTAINER_PORT = 5432
            const val LOCAL_PORT = 54320

            val connectionUri =
                "postgres://$USERNAME:$PASSWORD@${HostUtils.dockerHostIP}:${LOCAL_PORT}/$DATABASE_NAME?sslmode=disable"
            val jdbcUrl = "jdbc:postgresql://${HostUtils.dockerHostIP}:${LOCAL_PORT}/$DATABASE_NAME"

            fun build(): PostgreSQLContainer<Nothing> {
                return PostgreSQLContainer<Nothing>(Images.POSTGRES).apply {

                    withDatabaseName(DATABASE_NAME)
                    withUsername(USERNAME)
                    withPassword(PASSWORD)
                    withNetwork(internalNetwork)
                    withNetworkAliases("postgres")

                    withInitScript("db-configure.sql")

                    /*
                     This configures a static (instead of random) port number,
                     allowing you to easily connect and inspect the db locally if needed
                     (set a breakpoint in your test and connect your db tool to ${localPort})
                    */

                    withExposedPorts(CONTAINER_PORT)
                    withCreateContainerCmdModifier { cmd ->
                        cmd.withHostConfig(
                            HostConfig().withPortBindings(
                                PortBinding(
                                    Ports.Binding.bindPort(LOCAL_PORT),
                                    ExposedPort(CONTAINER_PORT)
                                )
                            )
                        )
                    }
                    waitingFor(
                        Wait.forLogMessage(".*database system is ready to accept connections.*\\n", 2)
                    )

                }
            }
        }

        object SpiceDB {

            const val KEY = "test-key"
            const val PORT = 50052

            // use schema name "spicedb" to avoid conflicts with other databases
            val connectionUri = Postgres.connectionUri + "&search_path=spicedb"

            fun buildChannel(): CloseableChannel {
                return SpiceDbChannelBuilder(
                    KEY,
                    "${HostUtils.dockerHostIP}:$PORT",
                    usePlaintext = true
                ).build()
            }

            fun buildMigrationContainer(postgres: PostgreSQLContainer<Nothing>): GenericContainer<*> {
                logger.trace("jdbc url:{}", connectionUri)

                return GenericContainer(DockerImageName.parse(Images.SPICEDB))
                    .withNetwork(internalNetwork)
                    .withCommand(
                        "datastore",
                        "migrate",
                        "head",
                        "--datastore-engine", "postgres",
                        "--datastore-conn-uri", connectionUri,
                        "--log-level", "debug"
                    )
                    .dependsOn(postgres)
            }

            fun buildServerContainer(postgres: PostgreSQLContainer<Nothing>): GenericContainer<*> {

                logger.trace("jdbc url: $connectionUri")
                val containerPort = PORT
                val localPort = PORT

                return GenericContainer(DockerImageName.parse(Images.SPICEDB))
                    .withNetwork(internalNetwork)
                    .withExposedPorts(containerPort)
                    .withCreateContainerCmdModifier { cmd ->
                        cmd.withHostConfig(
                            HostConfig().withPortBindings(
                                PortBinding(
                                    Ports.Binding.bindPort(localPort),
                                    ExposedPort(containerPort)
                                )
                            )
                        )
                    }

                    .withCommand(
                        "serve",
                        "--grpc-preshared-key", KEY,
                        /*
                         * It is possible to enable the rest api but we don't need it
                         * since we use the gRPC API directly.
                         * "--http-enabled",
                         * "--http-addr", "0.0.0.0:9090",
                         */
                        "--grpc-addr", "0.0.0.0:$containerPort",
                        "--datastore-engine", "postgres",
                        "--datastore-conn-uri", connectionUri,
                        "--datastore-conn-max-lifetime", "1m",
                        "--log-level", "debug"
                    )
                    .waitingFor(
                        Wait.forListeningPorts(containerPort)
                            .withStartupTimeout(Duration.ofMinutes(2))
                    )
                    .dependsOn(postgres)

            }
        }

        object SpiceDBZed {

            fun buildZedContainer(schema: File, unittest: File): GenericContainer<*> {

                return GenericContainer(DockerImageName.parse(Images.SPICEDB_ZED))
                    .withCopyFileToContainer(MountableFile.forHostPath(schema.path), "/tmp/resources/schema.zed")
                    .withCopyFileToContainer(MountableFile.forHostPath(unittest.path), "/tmp/resources/unittest.yaml")
                    .withWorkingDirectory("/tmp/resources")
                    .withCommand(
                        "validate",
                        "unittest.yaml",
                        "--log-level", "debug"
                    )

            }

        }


    }

    object HostUtils {

        val dockerHostIP: String
            /**
             * Get the best available host IP for containers to reach the host
             */
            get() {
                // Method 1: Try host.docker.internal (Docker Desktop)
                val hostDockerInternal = tryHostDockerInternal()
                if (hostDockerInternal != null) {
                    println("Using host.docker.internal: " + hostDockerInternal)
                    return hostDockerInternal
                }


                // Method 2: Try Docker gateway IP (Linux Docker)
                val gatewayIP = dockerGatewayIP
                if (gatewayIP != null) {
                    println("Using Docker gateway IP: " + gatewayIP)
                    return gatewayIP
                }


                // Method 3: Try first non-localhost IP
                val localIP = firstNonLocalhostIP
                if (localIP != null) {
                    println("Using local IP: " + localIP)
                    return localIP
                }


                // Method 4: Check common Docker gateway IPs
                val commonGateway = tryCommonGatewayIPs()
                if (commonGateway != null) {
                    println("Using common gateway IP: " + commonGateway)
                    return commonGateway
                }


                // Fallback
                println("Falling back to localhost (may not work from containers)")
                return "localhost"
            }

        /**
         * Try to use host.docker.internal (works on Docker Desktop)
         */
        private fun tryHostDockerInternal(): String? {
            try {
                InetAddress.getByName("host.docker.internal")
                return "host.docker.internal"
            } catch (e: Exception) {
                return null
            }
        }

        private val dockerGatewayIP: String?
            /**
             * Try to find Docker gateway IP (typically **********)
             */
            get() {
                try {
                    val interfaces = NetworkInterface.getNetworkInterfaces()
                    while (interfaces.hasMoreElements()) {
                        val networkInterface = interfaces.nextElement()

                        if (networkInterface.getName().contains("docker") ||
                            networkInterface.getName().contains("br-")
                        ) {
                            val addresses = networkInterface.getInetAddresses()
                            while (addresses.hasMoreElements()) {
                                val address = addresses.nextElement()
                                val hostAddress = address.getHostAddress()

                                if (!address.isLoopbackAddress() &&
                                    (hostAddress.startsWith("172.") || hostAddress.startsWith("192.168."))
                                ) {
                                    return hostAddress
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    System.err.println("Error detecting Docker gateway IP: " + e.message)
                }
                return null
            }

        private val firstNonLocalhostIP: String?
            /**
             * Get first non-localhost, non-link-local IP
             */
            get() {
                try {
                    val interfaces = NetworkInterface.getNetworkInterfaces()
                    while (interfaces.hasMoreElements()) {
                        val networkInterface = interfaces.nextElement()

                        if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                            continue
                        }

                        val addresses = networkInterface.getInetAddresses()
                        while (addresses.hasMoreElements()) {
                            val address = addresses.nextElement()

                            if (!address.isLoopbackAddress() && !address.isLinkLocalAddress()) {
                                val hostAddress = address.getHostAddress()
                                // Prefer IPv4
                                if (hostAddress.contains(".") && !hostAddress.startsWith("169.254.")) {
                                    return hostAddress
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    System.err.println("Error getting local IP: " + e.message)
                }
                return null
            }

        /**
         * Try common Docker gateway IPs
         */
        private fun tryCommonGatewayIPs(): String? {
            val commonIPs = arrayOf<String?>(
                "**********",  // Default Docker bridge
                "**********",  // Common alternative
                "************",  // Docker Desktop (Mac)
                "************" // Docker Toolbox
            )

            for (ip in commonIPs) {
                try {
                    val address = InetAddress.getByName(ip)
                    if (address.isReachable(1000)) { // 1 second timeout
                        return ip
                    }
                } catch (e: Exception) {
                    // Continue to next IP
                }
            }
            return null
        }

        /**
         * Build a PostgreSQL connection URI for SpiceDB
         */
        fun buildPostgresConnectionUri(
            host: String?,
            port: Int,
            database: String?,
            username: String?,
            password: String?
        ): String {
            return String.format(
                "postgresql://%s:%s@%s:%d/%s?sslmode=disable&connect_timeout=10",
                username, password, host, port, database
            )
        }

        /**
         * Print network debugging information
         */
        fun printNetworkInfo() {
            println("=== Network Information ===")

            try {
                val interfaces = NetworkInterface.getNetworkInterfaces()
                while (interfaces.hasMoreElements()) {
                    val ni = interfaces.nextElement()
                    println("Interface: " + ni.getName() + " (" + ni.getDisplayName() + ")")
                    println("  Up: " + ni.isUp() + ", Loopback: " + ni.isLoopback())

                    val addresses = ni.getInetAddresses()
                    while (addresses.hasMoreElements()) {
                        val addr = addresses.nextElement()
                        println(
                            "  Address: " + addr.getHostAddress() +
                                    " (Loopback: " + addr.isLoopbackAddress() +
                                    ", LinkLocal: " + addr.isLinkLocalAddress() + ")"
                        )
                    }
                    println()
                }
            } catch (e: Exception) {
                System.err.println("Error getting network info: " + e.message)
            }

            println("Detected Docker host IP: " + dockerHostIP)
            println("========================")
        }
    }
}

