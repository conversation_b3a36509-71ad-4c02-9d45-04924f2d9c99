package services.oneteam.ai.permissions.rebac

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.sql.Connection

typealias ResourceTypeId = Int

object ResourceTypeIds {
    const val FOUNDATION: ResourceTypeId = 1
    const val FORM: ResourceTypeId = 2
    const val WORKSPACE: ResourceTypeId = 3
    const val TENANT: ResourceTypeId = 4
}

/**
 * SubjectResourcePermissions is a local table which contains rows representing which subjects can VIEW which resources.
 * It is used for Row Level Security. Every table that is subject to visibility permissions has a POLICY which references
 * this table.
 *
 * This table is only concerned with RLS VIEW so that we can query while filtering and sorting - something that cannot
 * be done with a ReBAC system - for other permissions such as what actions a user can perform on a resource,
 * we use the ReBAC repository.
 *
 * A resource is defined by its type and ID, for example:
 * - A user can VIEW a foundation (resource type = foundation, resource ID = 123)
 * - A user can VIEW a form (resource type = form, resource ID = 456)
 * - A user can VIEW a workspace (resource type = workspace, resource ID = 789)
 *
 * Remember that what a user can VIEW is calculated by the ReBAC system
 * (which is dependent on the ReBAC schema which can include inheritance),
 * so this table is only used to store the results of those calculations.
 */
class SubjectResourcePermissionsRepository(private val connectionProvider: () -> Connection) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    val deleteSql = """
            DELETE FROM subject_resource_permissions 
            WHERE resource_type = ? AND subject_id = ? AND tenant_id = ?
        """


    val insertSql = """
            INSERT INTO subject_resource_permissions (tenant_id, subject_id, resource_id, resource_type)
            VALUES (?, ?, ?, ?)
        """

    /**
     * Deletes all rows for a given resource and subject.
     * EG delete all permissions for USER <X> on FOUNDATION <Y>.
     */
    fun deleteByResourceAndSubject(resourceTypeId: ResourceTypeId, resourceId: Long, subjectId: Long, tenantId: Long) {
        val deleteSql = """
            DELETE FROM subject_resource_permissions 
            WHERE resource_type = ? AND resource_id= ? AND subject_id = ? AND tenant_id = ?
        """

        connectionProvider().prepareStatement(deleteSql).use { preparedStatement ->
            preparedStatement.setInt(1, resourceTypeId)
            preparedStatement.setLong(2, resourceId)
            preparedStatement.setLong(3, subjectId)
            preparedStatement.setLong(4, tenantId)
            preparedStatement.executeUpdate()
        }
        logger.trace(
            "Successfully deleted permissions for resource type {} with ID {} for subject {}",
            resourceTypeId,
            resourceId,
            subjectId
        )
    }

    /**
     * Deletes all rows for a given resource type and subject.
     * EG delete all FOUNDATIONS for USER <X>.
     */
    fun deleteByResourceTypeAndSubject(resourceTypeId: ResourceTypeId, subjectId: Long, tenantId: Long) {
        connectionProvider().prepareStatement(deleteSql).use { preparedStatement ->
            preparedStatement.setInt(1, resourceTypeId)
            preparedStatement.setLong(2, subjectId)
            preparedStatement.setLong(3, tenantId)
            preparedStatement.executeUpdate()
        }
    }

    /**
     * Deletes all rows for a given resource type and resource ID.
     * Used when we delete a resource, for example when a foundation is deleted.
     * EG delete all permissions for FOUNDATION <Y>.
     */
    fun deleteByResource(resourceTypeId: ResourceTypeId, resourceId: Long, tenantId: Long) {
        connectionProvider().prepareStatement(deleteSql).use { preparedStatement ->
            preparedStatement.setInt(1, resourceTypeId)
            preparedStatement.setLong(2, resourceId)
            preparedStatement.setLong(3, tenantId)
            preparedStatement.executeUpdate()
        }
    }

    /**
     * Replaces ALL rows for a subject and the given resource type.
     * This is used when we want to reset the permissions for a subject on a set of resources.
     *
     * For example, when we want to synchronise permissions for a user for all foundations.
     */
    fun updateForSubjectAndResources(
        resourceIds: List<Long>,
        resourceTypeId: ResourceTypeId,
        subjectId: Long,
        tenantId: Long
    ) {
        deleteByResourceTypeAndSubject(resourceTypeId, subjectId, tenantId)
        insertForSubjectAndResources(resourceIds, resourceTypeId, subjectId, tenantId)
        logger.trace("Successfully updated {} {} resources for user {}", resourceIds.size, resourceTypeId, subjectId)
    }

    // OPTIMISATION are there faster ways to insert permissions in bulk? For big datasets, would loading a CSV file be faster?
    fun insertForSubjectAndResources(
        resourceIds: List<Long>,
        resourceTypeId: ResourceTypeId,
        subjectId: Long,
        tenantId: Long
    ) {
        connectionProvider().prepareStatement(insertSql).use { preparedStatement ->
            for (resourceId in resourceIds) {
                preparedStatement.setLong(1, tenantId)
                preparedStatement.setLong(2, subjectId)
                preparedStatement.setLong(3, resourceId)
                preparedStatement.setInt(4, resourceTypeId)
                preparedStatement.addBatch()
            }
            preparedStatement.executeBatch()
        }
        logger.trace("Successfully inserted permissions for {} {} for user {}", resourceTypeId, resourceIds, subjectId)
    }

    /**
     * Replaces all rows for a resource.
     * This is used when we want to update the view permissions for a resource.
     * For example, when we want to synchronise view permissions for a foundation.
     */
    fun updateForResourceAndSubjects(
        resourceId: Long,
        resourceTypeId: ResourceTypeId,
        subjectIds: List<Long>,
        tenantId: Long
    ) {
        deleteByResource(resourceTypeId, resourceId, tenantId)
        insertForResourceAndSubjects(resourceId, resourceTypeId, subjectIds, tenantId)
        logger.trace("Successfully updated {} {} resources for users {}", resourceTypeId, resourceId, subjectIds)
    }

    // OPTIMISATION are there faster ways to insert permissions in bulk? For big datasets, would loading a CSV file be faster?
    fun insertForResourceAndSubjects(
        resourceId: Long,
        resourceTypeId: ResourceTypeId,
        subjectIds: List<Long>,
        tenantId: Long
    ) {
        connectionProvider().prepareStatement(insertSql).use { preparedStatement ->
            for (subjectId in subjectIds) {
                preparedStatement.setLong(1, tenantId)
                preparedStatement.setLong(2, subjectId)
                preparedStatement.setLong(3, resourceId)
                preparedStatement.setInt(4, resourceTypeId)
                preparedStatement.addBatch()
            }
            preparedStatement.executeBatch()
        }
        logger.trace("Successfully insert permissions for {} {}  for users {}", resourceTypeId, resourceId, subjectIds)
    }

    fun findAllForSubject(
        resourceTypeId: ResourceTypeId,
        subjectId: Long,
        tenantId: Long
    ): List<Long> {
        val sql = """
            SELECT resource_id, resource_type FROM subject_resource_permissions
            WHERE subject_id = ? AND tenant_id = ? AND resource_type = ?
        """
        return connectionProvider().prepareStatement(sql).use { preparedStatement ->
            preparedStatement.setLong(1, subjectId)
            preparedStatement.setLong(2, tenantId)
            preparedStatement.setInt(3, resourceTypeId)
            preparedStatement.executeQuery().use { resultSet ->
                generateSequence {
                    if (resultSet.next()) {
                        resultSet.getLong("resource_id")
                    } else null
                }.toList()
            }
        }
    }
}