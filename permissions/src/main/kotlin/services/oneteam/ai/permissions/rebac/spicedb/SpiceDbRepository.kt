package services.oneteam.ai.permissions.rebac.spicedb

import com.authzed.api.v1.*
import com.authzed.api.v1.PermissionsServiceGrpc.PermissionsServiceBlockingStub
import com.authzed.grpcutil.BearerToken
import io.grpc.Channel
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.permissions.PermissionsException
import services.oneteam.ai.permissions.rebac.Permission
import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.permissions.rebac.RebacRepository
import services.oneteam.ai.permissions.rebac.Resource

class SpiceDbRepository(token: String, channel: Channel) : RebacRepository {

    private val logger: Logger = LoggerFactory.getLogger(javaClass)
    private val schemaService: SchemaServiceGrpc.SchemaServiceBlockingStub
    private val permissionsService: PermissionsServiceBlockingStub

    init {
        val bearerToken = BearerToken(token)
        schemaService = SchemaServiceGrpc.newBlockingStub(channel).withCallCredentials(bearerToken)
        permissionsService = PermissionsServiceGrpc.newBlockingStub(channel).withCallCredentials(BearerToken(token))
    }

    /**
     * Force fully consistent reads if no token is provided.
     * If a token is provided, use at-least-as-fresh semantics.
     */
    fun buildConsistencyToken(token: String?): Consistency {
        return if (token != null) {
            Consistency.newBuilder().setAtLeastAsFresh(ZedToken.newBuilder().setToken(token)).build()
        } else {
            Consistency.newBuilder().setFullyConsistent(true).build()
        }
    }

    override fun readRelationships(
        resourceType: Resource,
        resourceId: String,
        relationship: services.oneteam.ai.permissions.rebac.Relationship,
        subjectType: Resource?,
        subjectId: String?,
        token: String?
    ): List<String> {
        val relationshipFilterBuilder =
            RelationshipFilter.newBuilder().setResourceType(resourceType.key).setOptionalResourceId(resourceId)
                .setOptionalRelation(relationship.key)

        if (subjectType != null && subjectId != null) {
            relationshipFilterBuilder.setOptionalSubjectFilter(
                SubjectFilter.newBuilder().setSubjectType(subjectType.key).setOptionalSubjectId(subjectId).build()
            )
        }

        val request =
            ReadRelationshipsRequest.newBuilder()
                .setRelationshipFilter(relationshipFilterBuilder.build())
                .setConsistency(buildConsistencyToken(token))
                .build()

        try {
            val relationships = permissionsService.readRelationships(request)
            return relationships.asSequence().map { it.toString() }.toList()

            /*
                read_at {
                  token: "GgoKCENNWE1GQT09"
                }
                relationship {
                  resource {
                    object_type: "foundation"
                    object_id: "1"
                  }
                  relation: "write"
                  subject {
                    object {
                      object_type: "subject"
                      object_id: "1"
                    }
                  }
                }
                after_result_cursor {
                  token: "CjwKCENNWE1GQT09Ehxmb3VuZGF0aW9uOjEjd3JpdGVAc3ViamVjdDoxGhAwMjllNTE0YjVlYTM1NDRhIAE="
                }

            */

        } catch (e: Exception) {
            throw PermissionsException("Failed to read relationships", e)
        }
    }

    override fun writeRelationship(
        resourceType: Resource,
        resourceId: String,
        relationship: services.oneteam.ai.permissions.rebac.Relationship,
        subjectType: Resource,
        subjectId: String
    ): String {
        val request = WriteRelationshipsRequest.newBuilder().addUpdates(
            RelationshipUpdate.newBuilder().setOperation(RelationshipUpdate.Operation.OPERATION_TOUCH)
                .setRelationship(
                    Relationship.newBuilder().setResource(
                        ObjectReference.newBuilder().setObjectType(resourceType.key).setObjectId(resourceId).build()
                    ).setRelation(relationship.key).setSubject(
                        SubjectReference.newBuilder().setObject(
                            ObjectReference.newBuilder().setObjectType(subjectType.key).setObjectId(subjectId)
                                .build()
                        ).build()
                    ).build()
                ).build()
        )

        try {
            val response = permissionsService.writeRelationships(request.build())
            logger.trace(
                "Successfully wrote relationship: {}/{} with relation {} for subject {}/{}",
                resourceType,
                resourceId,
                relationship,
                subjectType,
                subjectId
            )
            return response.writtenAt.getToken()
        } catch (e: Exception) {
            throw PermissionsException("Failed to write relationship", e)
        }
    }

    override fun findAllResourcesWithPermissionForSubject(
        resourceType: Resource,
        permission: Permission,
        subjectType: Resource,
        subjectId: String,
        token: String?
    ): List<String> {
        val request = LookupResourcesRequest.newBuilder()
            .setResourceObjectType(resourceType.key)
            .setPermission(permission.key)
            .setSubject(
                SubjectReference.newBuilder()
                    .setObject(
                        ObjectReference.newBuilder()
                            .setObjectType(subjectType.key)
                            .setObjectId(subjectId)
                            .build()
                    )
                    .build()
            )
            .setConsistency(buildConsistencyToken(token))
            .build()

        try {
            val response = permissionsService.lookupResources(request)
            return response.asSequence().map { it.resourceObjectId }.toList()
        } catch (e: Exception) {
            throw PermissionsException("Failed to lookup resources", e)
        }

    }

    override fun findAllSubjectsWithPermissionOnResource(
        resourceType: Resource,
        resourceId: String,
        permission: Permission,
        token: String?
    ): List<String> {
        val request = LookupSubjectsRequest.newBuilder()
            .setResource(
                ObjectReference.newBuilder()
                    .setObjectType(resourceType.key)
                    .setObjectId(resourceId)
                    .build()
            )
            .setSubjectObjectType(PermissionStructure.Subject.NAME.key)
            .setPermission(permission.key)
            .setConsistency(buildConsistencyToken(token))
            .build()

        try {
            val response = permissionsService.lookupSubjects(request)
            return response.asSequence().map { it.subject.subjectObjectId }.toList()
        } catch (e: Exception) {
            throw PermissionsException("Failed to lookup subjects", e)
        }
    }


    override fun checkSubjectPermissionForResource(
        resourceType: Resource,
        resourceId: String,
        permission: Permission,
        subjectType: Resource,
        subjectId: String,
        token: String?
    ): Boolean {
        val request = CheckPermissionRequest.newBuilder()
            .setResource(
                ObjectReference.newBuilder()
                    .setObjectType(resourceType.key)
                    .setObjectId(resourceId)
                    .build()
            )
            .setPermission(permission.key)
            .setSubject(
                SubjectReference.newBuilder()
                    .setObject(
                        ObjectReference.newBuilder()
                            .setObjectType(subjectType.key)
                            .setObjectId(subjectId)
                            .build()
                    )
                    .build()
            )
            .setConsistency(buildConsistencyToken(token))
            .build()

        return try {
            val response = permissionsService.checkPermission(request)
            response.permissionship == CheckPermissionResponse.Permissionship.PERMISSIONSHIP_HAS_PERMISSION
        } catch (e: Exception) {
            throw PermissionsException("Failed to check permission", e)
        }
    }

    /**
     * Finds all relationships for a user, optionally filtered by prefix.
     * Returns a list of resource IDs that the user has view permission on.
     */
    override fun findAllRelationshipsForUser(
        subjectType: Resource,
        subjectId: String,
        prefix: String?,
        token: String?
    ): List<String> {

        val relationshipFilter = RelationshipFilter.newBuilder()
            .setOptionalSubjectFilter(
                SubjectFilter.newBuilder()
                    .setSubjectType(subjectType.key)
                    .setOptionalSubjectId(subjectId)
                    .build()
            )
            .setOptionalRelation(PermissionStructure.Common.Permissions.VIEW.key)

        if (!prefix.isNullOrEmpty()) {
            relationshipFilter.setOptionalResourceIdPrefix(prefix)
        }

        val request = ReadRelationshipsRequest.newBuilder()
            .setRelationshipFilter(relationshipFilter.build())
            .setConsistency(buildConsistencyToken(token))
            .build()

        try {
            val relationships = permissionsService.readRelationships(request)
            return relationships.asSequence().map { it.relationship.resource.objectId }.toList()
        } catch (e: Exception) {
            throw PermissionsException("Failed to get relationships", e)
        }
    }

    override fun deleteRelationshipsForResourceAndSubject(
        resourceType: Resource,
        resourceId: String,
        subjectId: String
    ): String {
        val relationshipFilter = RelationshipFilter.newBuilder()
            .setResourceType(resourceType.key)
            .setOptionalResourceId(resourceId)
            .setOptionalSubjectFilter(
                SubjectFilter.newBuilder().setSubjectType(PermissionStructure.Subject.NAME.key)
                    .setOptionalSubjectId(subjectId).build()
            )

        val request = DeleteRelationshipsRequest.newBuilder()
            .setRelationshipFilter(relationshipFilter.build())
            .build()

        try {
            val response = permissionsService.deleteRelationships(request)
            logger.trace(
                "Successfully deleted relationships for {}/{} and subject {}",
                resourceType,
                resourceId,
                subjectId
            )
            return response.deletedAt.token
        } catch (e: Exception) {
            throw PermissionsException("Failed to delete relationships", e)
        }
    }

    override fun deleteAllRelationshipsForResource(resourceType: Resource, resourceId: String): String {
        val relationshipFilter = RelationshipFilter.newBuilder()
            .setResourceType(resourceType.key)
            .setOptionalResourceId(resourceId)

        val request = DeleteRelationshipsRequest.newBuilder()
            .setRelationshipFilter(relationshipFilter.build())
            .build()

        try {
            val response = permissionsService.deleteRelationships(request)
            logger.trace("Successfully deleted all relationships for resource type: {}", resourceType)
            return response.deletedAt.getToken()
        } catch (e: Exception) {
            throw PermissionsException("Failed to delete relationships", e)
        }
    }

    override fun deleteAllRelationshipsForType(
        resourceType: Resource,
        relationship: services.oneteam.ai.permissions.rebac.Relationship
    ): String {
        val relationshipFilter = RelationshipFilter.newBuilder()
            .setResourceType(resourceType.key)
            .setOptionalRelation(relationship.key)

        val request = DeleteRelationshipsRequest.newBuilder()
            .setRelationshipFilter(relationshipFilter.build())
            .build()

        try {
            val response = permissionsService.deleteRelationships(request)
            logger.trace("Successfully deleted all relationships for resource type: {}", resourceType)
            return response.deletedAt.getToken()
        } catch (e: Exception) {
            throw PermissionsException("Failed to delete relationships", e)
        }
    }

    override fun writeSchema(schema: String): String {
        val request = WriteSchemaRequest
            .newBuilder()
            .setSchema(schema)
            .build()

        val response: WriteSchemaResponse
        try {
            response = schemaService.writeSchema(request)
        } catch (e: Exception) {
            throw PermissionsException("Failed to write schema", e)

        }
        return response.toString()
    }

    fun updateSchema() {
        // read schema.zed from classpath
        val schemaContent = loadSchemaFromClasspath()
        // write schema to SpiceDB
        writeSchema(schemaContent)
    }

    fun loadSchemaFromClasspath(): String {
        return this::class.java.classLoader.getResourceAsStream("services/oneteam/ai/permissions/spicedb/schema.zed")
            ?.use { inputStream ->
                inputStream.bufferedReader().readText()
            } ?: throw IllegalArgumentException("schema.zed not found in classpath")
    }

    fun readSchema(): String {
        val request = ReadSchemaRequest.newBuilder().build()
        try {
            return schemaService.readSchema(request).schemaText.also { schema ->
                logger.info("Current schema: \n${schema}")
            }
        } catch (e: Exception) {
            throw PermissionsException("Failed to read schema", e)
        }
    }

    fun healthCheck(): Boolean {
        return try {
            // Perform a simple operation to check if SpiceDB is reachable
            val request = ReadSchemaRequest.newBuilder().build()
            schemaService.readSchema(request)
            true
        } catch (e: Exception) {
            logger.error("SpiceDB health check failed", e)
            false
        }
    }
}