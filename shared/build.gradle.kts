plugins {
    id("project.conventions")
    id("java-test-fixtures")
}

group = "services.oneteam.ai.shared"
version = "0.0.1"

sourceSets {
    test {
        resources {
            srcDir("../scripts/db") // so that postgres can find the db scripts and set up the database for migrations to succeed
        }
    }
}

dependencies {
    api(project(":permissions"))

    api("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j")
    api("com.networknt:json-schema-validator")
    api(libs.json.schema.validator)
    api(libs.json.path)

    api(libs.postgresql)
    api(libs.logback.classic)
    api(libs.kotlinx.datetime)
    api(libs.bundles.exposed)
    api(libs.hikari)
    api(libs.bundles.flyway) // https://documentation.red-gate.com/fd/migrations-184127470.html
    api(libs.automerge)

    api(ktorLibs.client.core)
    api(ktorLibs.client.cio)
    api(ktorLibs.client.contentNegotiation)
    api(ktorLibs.client.serialization)
    api(ktorLibs.serialization.kotlinx.json)

    api(platform(libs.azure.sdk.bom))
    api(libs.azure.storage.blob)
    api(libs.azure.security.keyvault.secrets)
    api(libs.azure.identity)
    api(libs.azure.web.pubsub)
    api(libs.bouncycastle)
    implementation(libs.nanoid)
    implementation(libs.dataframe)

    testImplementation(kotlin("test"))
    testImplementation(libs.junit.jupiter.params)
    testImplementation(libs.bundles.kotest)
    testImplementation(libs.assertj.core)
    testImplementation(libs.test.containers)

    testFixturesApi(testFixtures(project(":permissions")))

    testFixturesApi(ktorLibs.client.mock)
    testFixturesApi(libs.mockk)
    testFixturesApi(libs.mockito)

}

// https://docs.gradle.org/current/userguide/jvm_test_suite_plugin.html
// https://tomgregory.com/gradle/gradle-integration-tests/#jvm-test-suite-plugin-gradle-version-73
// https://www.baeldung.com/gradle-test-vs-check
// https://stackoverflow.com/a/76530083
testing {
    suites {
        withType<JvmTestSuite> {
            useJUnitJupiter()
        }

        val test by getting(JvmTestSuite::class) {}

        val postgresTest by registering(JvmTestSuite::class) {
            dependencies {
                implementation(sourceSets.main.get().runtimeClasspath)
                implementation(sourceSets.test.get().runtimeClasspath)
                implementation(sourceSets.testFixtures.get().runtimeClasspath)
                implementation(libs.tcpostgresql)
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }

}

tasks.named("check") {
    dependsOn(testing.suites.named("postgresTest"))
}