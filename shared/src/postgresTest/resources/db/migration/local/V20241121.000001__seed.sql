INSERT INTO tenants (name, origin_url, created_at, updated_at)
VALUES ('tenant1', 'http://tenant1', NOW(), NOW());

INSERT INTO tenants (name, origin_url, created_at, updated_at)
VALUES ('tenant1', 'http://tenant2', NOW(), NOW());

INSERT INTO users (email, tenant_id, created_at, updated_at)
VALUES ('<EMAIL>', (SELECT id FROM tenants WHERE origin_url = 'http://tenant1'), NOW(), NOW());

INSERT INTO users (email, tenant_id, created_at, updated_at)
VALUES ('<EMAIL>', (SELECT id FROM tenants WHERE origin_url = 'http://tenant1'), NOW(), NOW());

INSERT INTO users (email, tenant_id, created_at, updated_at)
VALUES ('<EMAIL>', (SELECT id FROM tenants WHERE origin_url = 'http://tenant1'), NOW(), NOW());

INSERT INTO users (email, tenant_id, created_at, updated_at)
VALUES ('<EMAIL>', (SELECT id FROM tenants WHERE origin_url = 'http://tenant2'), NOW(), NOW());