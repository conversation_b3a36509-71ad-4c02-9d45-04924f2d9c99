package services.oneteam.ai.shared

import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.testing.TestPostgresDatabase

/**
 * Tests can be turned off for some situations by using annotations like:
 *   @EnabledOnOs(value = [OS.MAC], disabledReason = "Disabled on CI for now")
 */
class MigrationsPostgresTest {

    @Test()
    fun `should apply migrations successfully`() {
        // prepare
        val database = TestPostgresDatabase
        val fixtures = Fixtures(database)

        // perform
        database.migrate(*fixtures.javaMigrations().toTypedArray())

        // verify
        val connection = database.database.privileged.dataSource.connection
        connection.use { connection ->
            connection.prepareStatement("SELECT COUNT(*) FROM flyway_schema_history").executeQuery().use { rs ->
                rs.next()
                val count = rs.getInt(1)
                assert(count > 0) { "No migrations found" }
            }
        }
    }
}