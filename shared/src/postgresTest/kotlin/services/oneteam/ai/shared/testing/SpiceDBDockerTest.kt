package services.oneteam.ai.shared.testing


import com.authzed.api.v1.SchemaServiceGrpc
import com.authzed.api.v1.WriteSchemaRequest
import com.authzed.grpcutil.BearerToken
import com.github.dockerjava.api.model.ExposedPort
import com.github.dockerjava.api.model.HostConfig
import com.github.dockerjava.api.model.PortBinding
import com.github.dockerjava.api.model.Ports
import io.grpc.ManagedChannelBuilder
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.utility.DockerImageName
import services.oneteam.ai.permissions.Docker
import java.io.IOException
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.time.Duration

@Disabled
class SpiceDBIntegrationTest {

    @Test
    fun testSpiceDBConnection() {
        // Get connection details
        val spicedbGrpcHost = spicedb!!.getHost()
        val spicedbGrpcPort = spicedb!!.getMappedPort(50051)
        val spicedbHttpHost = spicedb!!.getHost()
        val spicedbHttpPort = spicedb!!.getMappedPort(9100)

        println("SpiceDB gRPC endpoint: " + spicedbGrpcHost + ":" + spicedbGrpcPort)
        println("SpiceDB HTTP endpoint: " + spicedbHttpHost + ":" + spicedbHttpPort)


        // Your SpiceDB client integration tests go here
        // Example using HTTP client or gRPC client

        // Verify containers are running
        assert(postgres!!.isRunning())
        assert(spicedb!!.isRunning())
    }

    @Test
    fun writeSchema() {
        val schema = """
                definition thelargeapp/article {
                	relation author: thelargeapp/user
                	relation commenter: thelargeapp/user

                	permission can_comment = commenter + author
                }

                definition thelargeapp/user {}
                
                """.trimIndent()

        val request = WriteSchemaRequest
            .newBuilder()
            .setSchema(schema)
            .build()

        val channel = ManagedChannelBuilder
            .forTarget("${Docker.HostUtils.dockerHostIP}:50051")
//                .useTransportSecurity() // if not using TLS, replace with .usePlaintext()
            .usePlaintext() // if not using TLS, replace with .usePlaintext()
            .build()
        val bearerToken = BearerToken("test-key")
        val schemaService = SchemaServiceGrpc.newBlockingStub(channel)
            .withCallCredentials(bearerToken)

        val response = schemaService.writeSchema(request)
        println("Response: $response")
        return
    }

    @Test
    @Throws(IOException::class, InterruptedException::class)
    fun testHttpEndpoint() {
        val endpoint = "http://" + spicedb!!.getHost() + ":" + spicedb!!.getMappedPort(9100) + "/v1/schema/read"

        println("Testing SpiceDB HTTP endpoint: $endpoint")

        val client = HttpClient.newHttpClient()
        val request = HttpRequest.newBuilder().method("POST", HttpRequest.BodyPublishers.noBody())
            .uri(URI.create(endpoint))
            .header("Authorization", "Bearer test-key")
            .header("Accept", "application/json")
            .header("Content-Type", "application/json")
            .build()

        val response = client.send(request, HttpResponse.BodyHandlers.ofString())

        println("Response status: " + response.statusCode())
        println("Response body: " + response.body())


        // Should return 200 even with empty schema
        assert(response.statusCode() == 200)
    }

    companion object {
        private var network: Network? = null
        private var postgres: PostgreSQLContainer<*>? = null
        private var spicedb: GenericContainer<*>? = null

        @JvmStatic
        @BeforeAll
        fun setUp() {
            // Create a shared network for containers to communicate
            network = Network.newNetwork()

            // Set up PostgreSQL container
            postgres = PostgreSQLContainer(DockerImageName.parse("postgres:16-alpine"))
                .withDatabaseName("spicedb")
                .withUsername("spicedb")
                .withPassword("spicedb")
                .withNetwork(network)
                .withNetworkAliases("postgres")
                .withExposedPorts(5432)
                .withCreateContainerCmdModifier { cmd ->
                    cmd.withHostConfig(
                        HostConfig().withPortBindings(
                            PortBinding(
                                Ports.Binding.bindPort(5432),
                                ExposedPort(5432)
                            )
                        )
                    )
                }
                .waitingFor(
                    Wait.forLogMessage(".*database system is ready to accept connections.*", 2)
                        .withStartupTimeout(Duration.ofMinutes(2))
                );

            postgres!!.start()
            System.out.println("PostgreSQL started successfully");
            println("PostgreSQL JDBC URL: " + postgres!!.getJdbcUrl());
            println("host: ${postgres!!.host}")
            testPostgreSQLConnectivity();


            // Set up SpiceDB container
            val spicedbMigrate = GenericContainer(DockerImageName.parse("authzed/spicedb:latest"))
                .withNetwork(network)
                .withCommand(
                    "migrate",
                    "head",
                    "--datastore-engine",
                    "postgres",
                    "--datastore-conn-uri",
                    "postgres://spicedb:spicedb@${Docker.HostUtils.dockerHostIP}:5432/spicedb?sslmode=disable",
                    "--datastore-conn-max-lifetime",
                    "1m",
                    "--log-level",
                    "debug"
                )
                .dependsOn(postgres)
            spicedbMigrate.start()

            // Set up SpiceDB container
            spicedb = GenericContainer(DockerImageName.parse("authzed/spicedb:latest"))
                .withNetwork(network)
                .withNetworkAliases("spicedb")
                .withExposedPorts(50051, 9100) // gRPC and HTTP ports
                .withCreateContainerCmdModifier { cmd ->
                    cmd.withHostConfig(
                        HostConfig().withPortBindings(
                            PortBinding(
                                Ports.Binding.bindPort(9100),
                                ExposedPort(9100)
                            ),
                            PortBinding(
                                Ports.Binding.bindPort(50051),
                                ExposedPort(50051)
                            )
                        )
                    )
                }
                .withCommand(
                    "serve",
                    "--grpc-preshared-key",
                    "test-key",
                    "--http-enabled",
                    "--http-addr",
                    ":9100",  // Specify HTTP port
                    "--datastore-engine",
                    "postgres",
                    "--datastore-conn-uri",
                    "postgres://spicedb:spicedb@${Docker.HostUtils.dockerHostIP}:5432/spicedb?sslmode=disable",
                    "--datastore-conn-max-lifetime",
                    "1m",
                    "--log-level",
                    "debug"
                )
//                .waitingFor(
//                    Wait.forListeningPort()
//                )
//                .waitingFor(
//                    Wait.forLogMessage(".*grpc server started.*", 1)
//                        .withStartupTimeout(Duration.ofMinutes(1))
//                )
                .waitingFor(Wait.forListeningPorts(50051, 9100))
                .dependsOn(spicedbMigrate)

            spicedb!!.start()

            println("SpiceDB is ready!");

            val logs = spicedb!!.getLogs()
            println("SpiceDB logs:")
            println(logs)
        }

        private fun testPostgreSQLConnectivity() {
            // Test PostgreSQL connectivity using a simple postgres client container
            try {
                GenericContainer(DockerImageName.parse("postgres:16-alpine"))
                    .withNetwork(network)
                    .withCommand("pg_isready", "-h", "postgres", "-p", "5432", "-U", "spicedb").use { pgTest ->
                        pgTest.start()
                        val result: String = pgTest.getLogs()
                        println("PostgreSQL connectivity test result: " + result)
                        if (!result.contains("accepting connections")) {
                            throw java.lang.RuntimeException("PostgreSQL is not accessible from within the network")
                        }
                    }
            } catch (e: java.lang.Exception) {
                System.err.println("Failed to test PostgreSQL connectivity: " + e.message)
                // Continue anyway, but log the issue
            }
        }

        @Throws(Exception::class)
        private fun waitForSpiceDBHealthy() {
            val healthEndpoint =
                "http://" + Docker.HostUtils.dockerHostIP + ":" + spicedb!!.getMappedPort(9100) + "/v1/schema/read"
            val client: HttpClient = HttpClient.newHttpClient()

            val maxRetries = 30 // 30 retries with 2 second intervals = 1 minute
            var retryCount = 0

            while (retryCount < maxRetries) {
                try {
                    val request: HttpRequest? = HttpRequest.newBuilder()
                        .method("POST", HttpRequest.BodyPublishers.noBody())
                        .header("Accept", "application/json")
                        .header("Content-Type", "application/json")
                        .uri(URI.create(healthEndpoint))
                        .header("Authorization", "Bearer test-key")
                        .timeout(Duration.ofSeconds(5))
                        .build()

                    val response: HttpResponse<String?> = client.send(request, HttpResponse.BodyHandlers.ofString())

                    if (response.statusCode() == 200) {
                        println("SpiceDB health check passed on attempt " + (retryCount + 1))
                        return  // Success!
                    } else {
                        println(
                            ("SpiceDB health check failed with status: " + response.statusCode()
                                    + ", body: " + response.body())
                        )
                    }
                } catch (e: Exception) {
                    println("SpiceDB health check attempt " + (retryCount + 1) + " failed: " + e.message)
                }

                retryCount++
                if (retryCount < maxRetries) {
                    Thread.sleep(2000) // Wait 2 seconds before retry
                }
            }

            throw RuntimeException("SpiceDB failed to become healthy after " + maxRetries + " attempts")
        }


        @JvmStatic
        @AfterAll
        fun tearDown() {
            if (spicedb != null) {
                spicedb!!.stop()
            }
            if (postgres != null) {
                postgres!!.stop()
            }
            if (network != null) {
                network!!.close()
            }
        }

        val spiceDBGrpcEndpoint: String
            // Helper method to get SpiceDB gRPC connection string
            get() = spicedb!!.getHost() + ":" + spicedb!!.getMappedPort(50051)

        val spiceDBHttpEndpoint: String
            // Helper method to get SpiceDB HTTP endpoint
            get() = "http://" + spicedb!!.getHost() + ":" + spicedb!!.getMappedPort(9100)

        val spiceDBPresharedKey: String
            // Helper method to get the preshared key
            get() = "test-key"
    }


}