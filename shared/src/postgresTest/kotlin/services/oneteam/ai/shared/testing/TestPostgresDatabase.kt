package services.oneteam.ai.shared.testing

import org.flywaydb.core.api.migration.JavaMigration
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.permissions.DockerOrchestration
import services.oneteam.ai.shared.*
import services.oneteam.ai.shared.database.DatabaseLive
import services.oneteam.ai.shared.database.DatabaseUtils
import java.util.concurrent.atomic.AtomicLong

/**
 * Provides a dockerized test postgres container for use in tests.
 * It will start the container and run the migrations on start up.
 */

// https://vuongdang.dev/articles/database-testing-with-testcontainers-and-kotlin-exposed
object TestPostgresDatabase : TestDb {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val counter = AtomicLong(1)
    val useStaticPort = System.getenv("TEST_DB_USE_STATIC_PORT")?.toBoolean() ?: true

    val container = DockerOrchestration.start()

    override fun migrate(vararg javaMigrations: JavaMigration) {
        logger.info("Migrating database")
        FlywayMigration().migrate(
            FlywayConfig(
                true,
                listOf(
                    "classpath:db/migration/common",
                    "classpath:db/migration/local",
                    "classpath:db/migration/postgres-only"
                ),
                mapOf("DB_USER" to "otai_admin", "DB_SUPER_USER" to "otai_superuser"),
            ), database.privileged.dataSource, *javaMigrations
        )

        DatabaseVerification("public", database).ensureTenantIsolation()
    }

    override fun drop() {
        logger.info("Dropping all tables")
        transaction(database.connectSuperUser()) {
            val conn = TransactionManager.current().connection

            DatabaseUtils.listTables("public").forEach {
                val statement = conn.prepareStatement("DROP TABLE IF EXISTS $it CASCADE", false)
                statement.executeUpdate()
            }
        }
    }

    override val database = DatabaseLive(
        DatabaseConfig(
            true,
            DBConnectionConfig(
                20,
                container.jdbcUrl,
                "org.postgresql.Driver",
                "otai_admin",
                "otai_admin",
                poolName = "test-db",
                rls = true
            ),
            // TODO should this use another user, different from the other connection?
            DBConnectionConfig(
                10,
                container.jdbcUrl,
                "org.postgresql.Driver",
                "otai_superuser",
                "otai_superuser",
                poolName = "test-db",
                rls = true
            ),
        )
    )

    init {
        start()
    }

    fun start() {
        println("Starting test database ${container.jdbcUrl}")
        drop()
        ExposedInitializer().init(true, database)
        DatabaseVerification("public", database).ensureTenantIsolation()
    }

    fun stop() {
        println("Stopping test database")
        container.stop()
    }

}