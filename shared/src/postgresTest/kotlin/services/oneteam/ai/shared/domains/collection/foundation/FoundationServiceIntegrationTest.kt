package services.oneteam.ai.shared.domains.collection.foundation

import io.kotest.common.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.TestDatabaseInitializer
import services.oneteam.ai.shared.UserSession
import services.oneteam.ai.shared.domains.NotFoundException
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.collection.Visibility
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.helpers.CustomNanoId
import services.oneteam.ai.shared.permissions.PermissionDeniedException
import services.oneteam.ai.shared.withTenantTransactionScope

class FoundationServiceIntegrationTest {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val fixtures = TestDatabaseInitializer.fixtures
    val foundationService = fixtures.foundationService
    val foundationMembersService = fixtures.foundationMembersService
    val workspaceService = fixtures.workspaceService
    val localRLSIndexSyncService = fixtures.localRLSIndexSyncService
    val workspaceUserService = fixtures.workspaceUserService

    @Test
    fun `creating a foundation adds the creator as a manager`() {
        runBlocking {

            val user1 = fixtures.tenant1User1Session
            val user2 = fixtures.tenant1User2Session
            val user3 = fixtures.tenant2User1Session

            val foundation = fixtures.withTestUser(user1) { user ->
                // prepare
                val workspace = workspaceService.get(Fixtures.Workspaces.Minimal.KEY)
                val rootFoundation = foundationService.root(workspace.id)

                // perform
                val foundation = createFoundation(
                    workspace.id,
                    rootFoundation,
                    Fixtures.Workspaces.Minimal.FoundationConfigurations.Company,
                    user1
                )

                logger.info("Working with test foundation {}", foundation)

                /*
                 * verify
                 */

                assertThat(foundation).isNotNull()

                withTenantTransactionScope {

                    val members = foundationMembersService.search(
                        FoundationMembersSearchCriteria.byFoundation(foundation.id), PageRequest.forMax()
                    )

                    // there should be one member - the user who created the foundation
                    assertThat(members.items).hasSize(1)
                    assertThat(members.items[0].userId).isEqualTo(user1.userId)
                    // ensure the user creating the foundation is a manager
                    assertThat(members.items[0].accessLevel).isEqualTo(FoundationMembers.AccessLevel.MANAGER)
                    // ensure the foundation can be retrieved by this user
                    assertThat(foundationService.get(foundation.id)).isNotNull
                    // ensure the foundation defaults to INHERIT visibility
                    assertThat(foundationService.get(foundation.id).visibility).isEqualTo(Visibility.INHERIT)
                }

                withTenantTransactionScope { tenant ->
                    fixtures.syncTenant.sync(tenant)
                    // after syncing the user should still have visibility
                    assertThat(foundationService.get(foundation.id)).isNotNull
                }

                foundation
            }

            userShouldSeeFoundation(user1, foundation)
            userShouldNotSeeFoundation(user2, foundation)
            userShouldNotSeeFoundation(user3, foundation)
        }
    }

    @Test
    fun `adding a member grants visibility`() {
        runBlocking {

            val user1 = fixtures.tenant1User1Session
            val user2 = fixtures.tenant1User2Session
            val user3 = fixtures.tenant1User3Session
            val user4 = fixtures.tenant2User1Session

            /**
             * Step 1: Create a foundation with one user
             */
            val foundation = fixtures.withTestUser(user1) { user ->
                // prepare
                val workspace = workspaceService.get(Fixtures.Workspaces.Minimal.KEY)
                val rootFoundation = foundationService.root(workspace.id)
                val foundationConfigurationId = Fixtures.Workspaces.Minimal.FoundationConfigurations.Company

                val foundation = createFoundation(workspace.id, rootFoundation, foundationConfigurationId, user1)

                logger.info("Working with test foundation $foundation")

                // perform

                /**
                 * Step 2: Add a second user as a member of the foundation
                 */

                addMember(foundation, user2, FoundationMembers.AccessLevel.WRITE)

                withTenantTransactionScope {
                    logPermissionState(workspace)
                    logPermissionState(workspace)
                }

                /*
                 * verify
                 */

                withTenantTransactionScope {

                    /**
                     * Step 3: Verify the second user has been added as a member
                     */
                    val members = foundationMembersService.search(
                        FoundationMembersSearchCriteria.byFoundationAndUser(
                            foundation.id,
                            user2.userId
                        ), PageRequest.forMax()
                    )

                    assertThat(members.items.size).isEqualTo(1)
                    assertThat(members.items[0].userId).isEqualTo(user2.userId)
                    // ensure the user has the right access level
                    assertThat(members.items[0].accessLevel).isEqualTo(FoundationMembers.AccessLevel.WRITE)

                }

                // return the foundation for the next step
                foundation
            }

            /**
             * Step 4: Verify the second user can see the foundation
             */


            userShouldSeeFoundation(user1, foundation)
            userShouldSeeFoundation(user2, foundation)
            userShouldNotSeeFoundation(user3, foundation)
            userShouldNotSeeFoundation(user4, foundation)
        }
    }

    @Test
    fun `a user without permission to the parent cannot create a foundation`() {
        runBlocking {

            val user1 = fixtures.tenant1User1Session
            val user2 = fixtures.tenant1User2Session

            val foundationConfigurationId = Fixtures.Workspaces.Minimal.FoundationConfigurations.Company

            /*
             * prepare
             */
            // GIVEN "user1" is logged in
            val foundation = fixtures.withTestUser(user1) { user ->

                // create workspace and root foundation as user1
                // AND creates the "Minimal" workspace
                val workspace = workspaceService.get(Fixtures.Workspaces.Minimal.KEY)
                val rootFoundation = foundationService.root(workspace.id)

                // create a foundation under root
                // user1 can do this because they have manage permissions on root
                // AND creates a foundation under root
                val foundation = createFoundation(workspace.id, rootFoundation, foundationConfigurationId, user)

                // add user2 to foundation with READ access
                // AND adds "user2" as a foundation member with "READ" access
                addMember(foundation, user2, FoundationMembers.AccessLevel.READ)

                return@withTestUser foundation
            }

            fixtures.withTestUser(user2) { user ->
                // perform + verify
                try {
                    val foundation =
                        createFoundation(foundation.workspaceId, foundation, foundationConfigurationId, user)
                    fail { "User ${user.userId} should not have been able to create a foundation under ${foundation.id}" }
                } catch (e: PermissionDeniedException) {
                    // expected
                    logger.info("Expected exception: ${e.message}")
                }
            }
        }
    }

    @Test
    fun `deactivating a member should remove permissions`() {
        runBlocking {

            val user1 = fixtures.tenant1User1Session
            val user2 = fixtures.tenant1User2Session

            val foundationConfigurationId = Fixtures.Workspaces.Minimal.FoundationConfigurations.Company

            /*
             * prepare
             */
            // GIVEN "user1" is logged in
            val foundation = fixtures.withTestUser(user1) { user ->

                // create workspace and root foundation as user1
                // AND creates the "Minimal" workspace
                val workspace = workspaceService.get(Fixtures.Workspaces.Minimal.KEY)
                val rootFoundation = foundationService.root(workspace.id)

                // create a foundation under root
                // user1 can do this because they have manage permissions on root
                // AND creates a foundation under root
                val foundation = createFoundation(workspace.id, rootFoundation, foundationConfigurationId, user)

                // add user2 to foundation with READ access
                // AND adds "user2" as a foundation member with "READ" access
                addMember(foundation, user2, FoundationMembers.AccessLevel.READ)

                return@withTestUser foundation
            }

            // user2 should be able to see the foundation before we deactivate them
            fixtures.withTestUser(user2) { user ->
                foundationService.get(foundation.id)
            }

            // deactivate user2
            fixtures.withTestUser(user1) { user ->
                foundationMembersService.deactivateMembers(
                    foundation, listOf(
                        FoundationMembers.ForDeactivate(
                            userId = user2.userId,
                        )
                    )
                )
            }

            // user2 should no longer be able to see the foundation
            fixtures.withTestUser(user2) { user ->
                // perform + verify
                try {
                    foundationService.get(foundation.id)
                    fail { "User ${user.userId} should not have been able to retrieve foundation ${foundation.id}" }
                } catch (e: NotFoundException) {
                    // expected
                    logger.info("Expected exception: ${e.message}")
                }
            }
        }
    }

    private fun logPermissionState(workspace: Workspace.ForApi) {
        logger.info(
            localRLSIndexSyncService.permissionStateForResource(
                workspace.id.value,
                PermissionStructure.Workspace.NAME
            ).toString()
        )
    }

    private suspend fun createFoundation(
        workspaceId: Workspace.Id,
        parentFoundation: Foundation.ForApi,
        foundationConfigurationId: FoundationConfiguration.Id,
        user: UserSession
    ): Foundation.ForApi {
        val foundation = foundationService.create(
            fixtures.anyFoundationForCreate(
                workspaceId = workspaceId,
                foundationConfigurationId = foundationConfigurationId,
                parentId = parentFoundation.id,
            ),
            user.userId
        )
        return foundation
    }

    private suspend fun addMember(
        foundation: Foundation.ForApi,
        user: UserSession,
        accessLevel: FoundationMembers.AccessLevel
    ) {
        foundationMembersService.createMembers(
            foundation, listOf(
                FoundationMembers.ForCreate(
                    userId = user.userId,
                    accessLevel = accessLevel
                )
            )
        )
    }

    private suspend fun userShouldNotSeeFoundation(user: UserSession, foundation: Foundation.ForApi) =
        fixtures.withTestUser(user) {
            withTenantTransactionScope {
                try {
                    foundationService.get(foundation.id)
                    fail { "Foundation ${foundation.id} should not be visible to user ${user.userId}" }
                } catch (_: NotFoundException) {
                    // expected
                    return@withTenantTransactionScope
                }
            }
        }

    private suspend fun userShouldSeeFoundation(user: UserSession, foundation: Foundation.ForApi) =
        fixtures.withTestUser(user) {
            withTenantTransactionScope {
                withTenantTransactionScope {
                    logger.info(
                        localRLSIndexSyncService.permissionStateForResource(
                            foundation.id.value,
                            PermissionStructure.Foundation.NAME
                        ).toString()
                    )
                }

                assertThat(foundationService.get(foundation.id)).isNotNull()
            }
        }
}
