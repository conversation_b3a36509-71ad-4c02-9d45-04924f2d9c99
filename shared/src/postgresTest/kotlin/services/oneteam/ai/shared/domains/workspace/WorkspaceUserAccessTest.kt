package services.oneteam.ai.shared.domains.workspace

import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import services.oneteam.ai.shared.withTenantTransactionScope
import kotlin.test.assertNotNull

class WorkspaceUserAccessTest {

    private val database = TestPostgresDatabase
    private val checks = Checks()

    @Test
    fun `user should only see workspaces they have access to`() = runTest {
        val fixtures = Fixtures(database).initialise()
        val workspaceRepository = fixtures.workspaceRepository

        // create workspace 1 for user 1
        val key1 = Workspace.Key("T1")
        val key2 = Workspace.Key("T2")

        withContext(RequestContext(tenant = fixtures.tenant1, principal = fixtures.tenant1User1Session)) {
            withTenantTransactionScope { tenant ->

                // prepare
                val workspace = workspaceRepository.create(
                    tenant,
                    Workspace.ForCreate(
                        Workspace.Name("testWorkspaceAccess1"),
                        key1,
                        Workspace.Description("test description"),
                        Workspace.DocumentId("workspaceDocumentId1")
                    ),
                    fixtures.tenant1User1!!
                )
                assertNotNull(workspace)
            }
        }


        // create workspace 2 for user 2

        withContext(RequestContext(tenant = fixtures.tenant1, principal = fixtures.tenant1User2Session)) {
            withTenantTransactionScope { tenant ->
                // prepare
                val workspace = workspaceRepository.create(
                    tenant,
                    Workspace.ForCreate(
                        Workspace.Name("testWorkspaceAccess2"), key2,
                        Workspace.Description("test description"),
                        Workspace.DocumentId("workspaceDocumentId2")
                    ),
                    fixtures.tenant1User2!!
                )
                assertNotNull(workspace)
            }

        }

        // verify user 1 can only see workspace 1
        withContext(RequestContext(tenant = fixtures.tenant1, principal = fixtures.tenant1User1Session)) {
            withTenantTransactionScope {
                assertThat(workspaceRepository.getByKey(key1)).isNotNull()
                assertThat(workspaceRepository.getByKey(key2)).isNull()
            }
        }
        // verify user 2 can only see workspace 2
        withContext(RequestContext(tenant = fixtures.tenant1, principal = fixtures.tenant1User2Session)) {
            withTenantTransactionScope {
                assertThat(workspaceRepository.getByKey(key1)).isNull()
                assertThat(workspaceRepository.getByKey(key2)).isNotNull()
            }
        }
    }
}