package services.oneteam.ai.shared.domains.workspace

import io.kotest.matchers.comparables.shouldBeGreaterThan
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withContext
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.UserSession
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import services.oneteam.ai.shared.withTenantTransactionScope
import kotlin.test.assertNotNull

class WorkspaceRepositoryPostgresTest {
    val database = TestPostgresDatabase
    val checks = Checks()

    lateinit var fixtures: Fixtures
    lateinit var workspaceRepository: WorkspaceRepository

    @BeforeEach
    fun setUp() {
        // Initialize fixtures and repositories
        fixtures = Fixtures(database).initialise()
        workspaceRepository = fixtures.workspaceRepository
    }

    @AfterEach
    fun tearDown() {
//        println("Container logs:\n${spiceDBContainer.logConsumer.toUtf8String()}")
    }

    @Test
    fun `should create workspace`() = runTest {

        withContext(RequestContext(tenant = fixtures.tenant2, principal = fixtures.tenant2User1Session)) {
            withTenantTransactionScope {
                // prepare
                // perform
                val workspace = workspaceRepository.create(
                    fixtures.tenant2,
                    Workspace.ForCreate(
                        Workspace.Name("testWorkspace"), Workspace.Key("T1"),
                        Workspace.Description("test description")
                    ),
                    fixtures.tenant2User1!!
                )
                // verify
                assertNotNull(workspace)
                workspace.id.value shouldBeGreaterThan (0L)
            }
        }
    }

    @Test
    fun `should get all workspaces for current tenant`() = runTest {
        // prepare

        createWorkspace(fixtures.tenant1, fixtures.tenant1User1Session)
        createWorkspace(fixtures.tenant2, fixtures.tenant2User1Session)

        // tenant 1 should only see tenant 1 workspaces
        withContext(RequestContext(tenant = fixtures.tenant1, principal = fixtures.tenant1User1Session)) {
            withTenantTransactionScope {
                // perform
                val workspaces = workspaceRepository.getAll()
                // verify
                assertNotNull(workspaces)
                workspaces.forEach { w ->
                    //todo: fix this
//                w.tenantId shouldBe fixtures.tenant1.id
                }
            }
        }

        // tenant 2 should only see tenant 2 workspaces
        withContext(RequestContext(tenant = fixtures.tenant2, principal = fixtures.tenant2User1Session)) {
            withTenantTransactionScope {
                // perform
                val workspaces = workspaceRepository.getAll()
                // verify
                assertNotNull(workspaces)
                workspaces.forEach { w ->
                    //todo: fix this
//                w.tenantId shouldBe fixtures.tenant2.id
                }
            }
        }

    }

    suspend fun createWorkspace(tenant: Tenant, userId: UserSession): Workspace.ForApi {
        return withContext(RequestContext(tenant = tenant, principal = userId)) {
            return@withContext withTenantTransactionScope {
                // prepare
                val count = database.counter.incrementAndGet()
                // perform
                return@withTenantTransactionScope workspaceRepository.create(
                    tenant,
                    Workspace.ForCreate(
                        Workspace.Name("N${tenant.id}-$count"),
                        Workspace.Key("K${tenant.id}-$count"),
                        Workspace.Description("D${tenant.id}-$count")
                    ),
                    User.Id(userId.user.id)
                )

            }
        }
    }
}