package services.oneteam.ai.shared

import db.migration.common.V20250727_1__spicedb_schema
import io.grpc.ManagedChannelBuilder
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.decodeFromStream
import org.flywaydb.core.api.migration.JavaMigration
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.permissions.Docker
import services.oneteam.ai.permissions.rebac.LocalRLSIndexSyncService
import services.oneteam.ai.permissions.rebac.SubjectResourcePermissionsRepository
import services.oneteam.ai.permissions.rebac.spicedb.SpiceDbRepository
import services.oneteam.ai.shared.domains.collection.form.BlobService
import services.oneteam.ai.shared.domains.collection.form.FormMembersRepository
import services.oneteam.ai.shared.domains.collection.form.FormRepository
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.FoundationMembersRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationMembersService
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationRepository
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.passvault.InMemoryKeyVaultService
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.tenant.TenantRepository
import services.oneteam.ai.shared.domains.tenant.TenantService
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.domains.user.UserService
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariableRepository
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariableService
import services.oneteam.ai.shared.domains.workspace.variable.secured.WorkspaceSecuredValueRepository
import services.oneteam.ai.shared.domains.workspace.variable.secured.WorkspaceSecuredValueService
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.permissions.*
import java.nio.file.Paths
import java.sql.Connection
import java.util.*

class Fixtures(val testDb: TestDb) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    val checks = Checks()
    val d: ResourceBundle = ResourceBundle.getBundle("dictionary")

    val proxyService = ExternalProxyService("token", MockHttpClient().client)
    val dictionary: ResourceBundle = ResourceBundle.getBundle("dictionary")
    val tenantRepository = TenantRepository()
    val tenantService = TenantService(tenantRepository)
    val userRepository = UserRepository(checks)
    val userService = UserService(userRepository)
    val foundationMembersRepository = FoundationMembersRepository()

    val formMembersRepository = FormMembersRepository()
    val rebacRepository = SpiceDbRepository(
        Docker.Containers.SpiceDB.KEY, ManagedChannelBuilder
            .forTarget("${Docker.HostUtils.dockerHostIP}:${Docker.Containers.SpiceDB.PORT}")
            .usePlaintext() // if not using TLS, replace with .usePlaintext()
            .build()
    )

    val localIndexRepository =
        SubjectResourcePermissionsRepository { TransactionManager.current().connection.connection as Connection }

    val relationshipRepository = RelationshipRepository(
        rebacRepository
    )
    val localRLSIndexSyncService = LocalRLSIndexSyncService(
        rebacRepository,
        localIndexRepository,
        ResourceType.entries.filter { it == ResourceType.FOUNDATION || it == ResourceType.FORM || it == ResourceType.WORKSPACE }
            .associate { it.key to it.id }
    )
    val permissionsService: PermissionsService = PermissionsService(
        localIndexRepository,
        relationshipRepository,
        localRLSIndexSyncService,
        rebacRepository
    )
    val workspacePermissionService = WorkspacePermissionService(permissionsService)
    val foundationPermissionService = FoundationPermissionService(permissionsService, workspacePermissionService)
    val formPermissionService = FormPermissionService(permissionsService, foundationPermissionService)
    val workspaceRepository = WorkspaceRepository(checks, permissionsService)
    val foundationRepository = FoundationRepository(permissionsService)

    val foundationConfigurationService = FoundationConfigurationService(checks, dictionary, foundationRepository)
    val flowStepTypeConfigurationRepository = FlowStepTypeConfigurationRepository()
    val flowStepTypeConfigurationService = FlowStepTypeConfigurationService(flowStepTypeConfigurationRepository)
    val formRepository = FormRepository(checks, permissionsService)
    val resourceRepository = ResourceRepository(foundationRepository, formRepository)

    val documentService = ApiDocumentService(
        proxyService, flowStepTypeConfigurationService
    )
    val keyVaultService = InMemoryKeyVaultService(
        InMemoryKeyVaultService.InMemoryKeyVaultServiceConfig(
            loadSeedData = true, keyVaultName = "testVault", secretNamePrefix = "test"
        )
    )
    val workspaceSecuredValueRepository = WorkspaceSecuredValueRepository()
    val workspaceSecuredValueService = WorkspaceSecuredValueService(
        keyVaultService, workspaceSecuredValueRepository
    )
    val workspaceVariableRepository = WorkspaceVariableRepository();
    val workspaceVariableService = WorkspaceVariableService(workspaceSecuredValueService, workspaceVariableRepository)
    val workspaceVersionRepository = WorkspaceVersionRepository()
    val workspaceVersionService = WorkspaceVersionService(
        workspaceRepository, documentService, workspaceVersionRepository, workspaceVariableService, checks
    )

    val workspaceUserRepository =
        WorkspaceUserRepository(checks, workspaceRepository, userRepository)
    val workspaceUserService = WorkspaceUserService(
        workspaceUserRepository,
        workspacePermissionService,
        permissionsService,
    )
    val foundationMembersService = FoundationMembersService(
        foundationPermissionService,
        permissionsService,
        foundationMembersRepository,
        resourceRepository,
        workspaceUserService,
        userService
    )
    val foundationService = FoundationService(
        foundationRepository,
        workspaceVersionService,
        resourceRepository,
        permissionsService,
        foundationPermissionService,
        foundationMembersService,
        checks
    )

    val uploadService = BlobService("", "", "")
    val formService = FormService(
        formRepository,
        workspaceRepository,
        documentService,
        workspaceVersionRepository,
        uploadService,
        foundationService,
        workspaceVersionService,
        formPermissionService,
        formMembersRepository,
        checks
    )
    val workspaceService = WorkspaceService(
        workspaceRepository,
        foundationService,
        documentService,
        foundationConfigurationService,
        workspaceVersionService,
        formService,
        workspaceUserService,
        checks
    )

    val syncWorkspace = SyncWorkspace(
        formRepository,
        workspaceUserRepository,
        foundationRepository,
        permissionsService,
        formMembersRepository,
        foundationMembersRepository
    )
    val syncUser = SyncUser(
        localRLSIndexSyncService = localRLSIndexSyncService,
        userRepository = userRepository
    )

    val syncTenant = SyncTenant(
        workspaceRepository = workspaceRepository,
        foundationRepository = foundationRepository,
        syncWorkspace = syncWorkspace,
        syncUser = syncUser,
        tenantRepository = tenantRepository,
    )

    val v202507271SpicedbSchema = V20250727_1__spicedb_schema(rebacRepository)

    lateinit var tenant1: Tenant
    lateinit var tenant2: Tenant

    var tenant1User1: User.Id? = null
    var tenant1User2: User.Id? = null
    var tenant2User1: User.Id? = null

    lateinit var tenant1User1Session: UserSession
    lateinit var tenant1User2Session: UserSession
    lateinit var tenant2User1Session: UserSession

    val Workspace1_Key = Workspace.Key("WS1")

    fun javaMigrations(): List<JavaMigration> {
        return listOf(
            v202507271SpicedbSchema,
        )
    }

    /**
     * We need to:
     * 1. Start postgres container
     * 2. Drop the tables to ensure a clean state
     * 3. Apply spiceDB migrations
     * 4. Start spiceDB container
     * 5. Apply application migrations
     */
    fun initialise(): Fixtures {
        logger.info("Initialising fixtures")

        testDb.drop()

        testDb.migrate(v202507271SpicedbSchema)

        runBlocking {
            // these represent the tenants inserted via migrations in src/postgresTest/resources/db/migration/local/V20241121.000001__seed.sql and src/h2Test/resources/db/migration/local/V20241121.000001__seed.sql
            tenant1 = tenantService.getByInternalUrlOrOriginUrl("http://tenant1")!!
            tenant2 = tenantService.getByInternalUrlOrOriginUrl("http://tenant2")!!

            withContext(RequestContext(tenant = tenant1)) {
                withTenantTransactionScope {
                    tenant1User1 =
                        User.Id(userRepository.getAll().first { it.email == "<EMAIL>" }.id.value)
                    tenant1User2 =
                        User.Id(userRepository.getAll().first { it.email == "<EMAIL>" }.id.value)
                }
            }
            withContext(RequestContext(tenant = tenant2)) {
                withTenantTransactionScope {
                    tenant2User1 =
                        User.Id(userRepository.getAll().first { it.email == "<EMAIL>" }.id.value)
                }
            }
            tenant1User1Session = UserSession(
                AuthenticatedUser(
                    tenant1User1!!.value,
                    "tokenUser.firstname",
                    "tokenUser.lastname",
                    "tokenUser.email",
                    tenant1.id,
                    1,
                    AutoLogout(true, 60) // Assuming a default auto logout setting
                ), Long.MAX_VALUE
            )

            tenant1User2Session = UserSession(
                AuthenticatedUser(
                    tenant1User2!!.value,
                    "tokenUser.firstname",
                    "tokenUser.lastname",
                    "tokenUser.email",
                    tenant1.id,
                    1,
                    AutoLogout(true, 60) // Assuming a default auto logout setting
                ), Long.MAX_VALUE
            )

            tenant2User1Session = UserSession(
                AuthenticatedUser(
                    tenant2User1!!.value,
                    "tokenUser.firstname",
                    "tokenUser.lastname",
                    "tokenUser.email",
                    tenant2.id,
                    2,
                    AutoLogout(true, 60) // Assuming a default auto logout setting
                ), Long.MAX_VALUE
            )

            runBlocking {
                withContext(RequestContext(tenant = tenant1, principal = tenant1User1Session)) {
                    workspaceService.create(
                        Workspace.ForCreate(
                            Workspace.Name("testWorkspace1"), Workspace1_Key, Workspace.Description("test description")
                        ), user = tenant1User1!!
                    )
                }
            }
        }
        return this
    }

    companion object {

        @OptIn(ExperimentalSerializationApi::class)
        fun stepTypeConfigurations(): Map<String, FlowStepTypeConfiguration> {
            return otSerializer.decodeFromStream<List<FlowStepType>>(this::class.java.getResourceAsStream("/StepTypeSeed.json")!!)
                .map {
                    FlowStepTypeConfiguration(
                        id = 0,
                        primaryIdentifier = it.primaryIdentifier,
                        type = it.type,
                        name = it.name,
                        description = it.description,
                        properties = it.properties
                    )
                }.associateBy { it.primaryIdentifier }

        }

        fun helperFlows(): Map<FlowConfiguration.Id, FlowConfiguration.ForJson> {
            val file = Paths.get("src/test/resources/validation/flowconfiguration/valid/helper-flow-set-variable.json")
                .toFile()
            val input = file.readText()
            val flowConfiguration = otSerializer.decodeFromString<FlowConfiguration.ForJson>(input)
            return mapOf(
                flowConfiguration.id to flowConfiguration
            )
        }
    }
}