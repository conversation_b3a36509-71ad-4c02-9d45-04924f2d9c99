package db.migration.common

import org.flywaydb.core.api.migration.BaseJavaMigration
import org.flywaydb.core.api.migration.Context
import services.oneteam.ai.shared.permissions.SyncTenant

/**
 * Java migration to add permissions for workspaces
 *  - uses audit logs to find out who created the workspace
 *
 *  This migration will:
 *  - iterate through all workspaces
 *  - for each workspace, it will:
 *    - iterate through all foundations and forms in the workspace
 *      - create relationships for each foundation and form
 *    - set permissions for users in the workspace based on their access level
 *  - sync the local RLS index for each user in the workspace
 */
class V20250728_1__permissions_workspace(val syncTenant: SyncTenant) : BaseJavaMigration() {

    override fun migrate(context: Context) {
        syncTenant.sync()
    }

}