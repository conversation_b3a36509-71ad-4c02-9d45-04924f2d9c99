package services.oneteam.ai.shared.permissions

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.auth.ApiKeyPrincipal
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.tenant.TenantRepository
import services.oneteam.ai.shared.domains.workspace.WorkspaceRepository
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.withTenantTransactionScope

class SyncTenant(
    val workspaceRepository: WorkspaceRepository,
    val foundationRepository: FoundationRepository,
    val syncWorkspace: SyncWorkspace,
    val syncUser: SyncUser,
    val tenantRepository: TenantRepository,
) {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun sync() {
        runBlocking {
            newSuspendedTransaction {
                tenantRepository.getAll()
            }
        }.forEach { tenant ->
            sync(tenant)
        }
    }

    fun sync(tenant: Tenant) {
        processWorkspaces(tenant)
    }

    private fun processWorkspaces(tenant: Tenant) {
        runBlocking {
            // run as a system user (ApiKeyPrincipal) to avoid RLS
            launch(Dispatchers.IO + RequestContext(tenant = tenant, principal = ApiKeyPrincipal("syncSpiceDb"))) {

                val workspaces = withTenantTransactionScope { workspaceRepository.getAll() }

                var hasError = false

                workspaces.forEach { workspace ->

                    try {
                        syncWorkspace.syncWorkspace(workspace.id, tenant.id)
                    } catch (ex: Exception) {
                        hasError = true
                        logger.error(
                            "Error processing workspace with ID {} in tenant {}: {}",
                            workspace.id,
                            tenant.id,
                            ex.message,
                            ex
                        )
                        // continue processing other workspaces even if one fails so we don't prevent the server starting
                    } finally {
                        logger.info("Finished processing workspace with ID {} in tenant {}", workspace.id, tenant.id)
                    }
                }

                if (hasError) {
                    logger.error("There were errors processing workspaces in tenant ${tenant.id} - ${tenant.name}")
                }

                syncUser.syncUsers()
            }
        }
    }
}