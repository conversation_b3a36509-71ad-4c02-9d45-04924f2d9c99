package services.oneteam.ai.shared.permissions

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.collection.form.FormRepository
import services.oneteam.ai.shared.domains.collection.form.FormsSchema
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationsSchema
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceUserRepository
import services.oneteam.ai.shared.domains.workspace.WorkspaceUserSearchCriteria
import services.oneteam.ai.shared.withTenantTransactionScope

class SyncWorkspace(
    val relationshipRepository: RelationshipRepository,
    val formRepository: FormRepository,
    val workspaceUserRepository: WorkspaceUserRepository,
    val foundationRepository: FoundationRepository,
) {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    suspend fun syncWorkspace(
        workspaceId: Long,
        tenantId: Long,
    ) {
        logger.trace("Adding permissions for workspace {} in tenant {}", workspaceId, tenantId)

        forEachFoundationInWorkspace(workspaceId = workspaceId, tenantId = tenantId)
        forEachFormInWorkspace(workspaceId, tenantId)

        // load workspace users
        handleWorkspaceUsersForWorkspace(workspaceId, tenantId)
    }

    private suspend fun handleWorkspaceUsersForWorkspace(workspaceId: Long, tenantId: Long) =
        withTenantTransactionScope {
            workspaceUserRepository.searchByCriteria(
                PageRequest.forMax(), WorkspaceUserSearchCriteria(Workspace.Id(workspaceId), null)
            )
                .items.forEach { user ->
                    workspaceUserRepository.setPermissionsForWorkspace(
                        workspaceId = Workspace.Id(workspaceId),
                        userId = User.Id(user.userId.value),
                        accessLevel = user.accessLevel,
                        tenantId = tenantId
                    )
                }
        }

    /**
     * Iterates through all foundations in a workspace and creates relationships for each foundation.
     */

    private suspend fun forEachFoundationInWorkspace(
        workspaceId: Long,
        tenantId: Long,
    ) {
        withTenantTransactionScope { foundationRepository.selectMany { FoundationsSchema.workspaceId eq workspaceId } }?.forEach { foundation ->

            var parentId: Long? = foundation.parentId?.value
            var parentType = ResourceType.FOUNDATION

            val userId: Long =
                withTenantTransactionScope { DatabaseUtils.getCreatedBy(foundation.id.value, tenantId, "foundations") }

            // if parentId is null, it means the foundation is a top-level foundation in the workspace
            if (parentId == null) {
                parentId = workspaceId
                parentType = ResourceType.WORKSPACE
            }

            relationshipRepository.doCreate(
                CreateResourcePermissionRequest.ForFoundation(
                    subjectId = userId.toString(),
                    tenantId = tenantId,
                    workspaceId = workspaceId,
                    parentType = parentType,
                    parentId = parentId.toString(),
                ), foundation.id.value.toString()
            )
        }
    }

    /**
     * Iterates through all foundations in a workspace and creates relationships for each foundation.
     */
    private suspend fun forEachFormInWorkspace(
        workspaceId: Long,
        tenantId: Long,
    ) {
        withTenantTransactionScope { formRepository.selectMany { FormsSchema.workspace eq workspaceId } }?.forEach { form ->

            val formId = form.id.value
            val parentId = form.foundation.id.value

            val userId: Long = withTenantTransactionScope { DatabaseUtils.getCreatedBy(formId, tenantId, "forms") }

            relationshipRepository.doCreate(
                CreateResourcePermissionRequest.ForForm(
                    subjectId = userId.toString(),
                    tenantId = tenantId,
                    workspaceId = workspaceId,
                    parentId = parentId.toString(),
                ), formId.toString()
            )
        }
    }

}