package services.oneteam.ai.shared.domains.workspace

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.json.jsonb
import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.permissions.rebac.Relationship
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.tenant.TenantSchema
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserEntity
import services.oneteam.ai.shared.domains.user.UserSchema
import services.oneteam.ai.shared.otSerializer


class WorkspaceUserEntity(id: EntityID<Long>) : BaseLongEntity(id, WorkspaceUsersSchema) {
    companion object : BaseLongEntityClass<WorkspaceUserEntity>(WorkspaceUsersSchema)

    var workspaceId by WorkspaceUsersSchema.workspaceId;
    var userId by WorkspaceUsersSchema.userId;
    var tenantId by WorkspaceUsersSchema.tenantId;
    var status by WorkspaceUsersSchema.status;
    var accessLevel: List<WorkspaceUser.AccessLevel> by WorkspaceUsersSchema.accessLevel
    var user by UserEntity referencedOn WorkspaceUsersSchema.userId

    fun toDTO(): WorkspaceUser.ForApi {
        return WorkspaceUser.ForApi(
            id = WorkspaceUser.Id(this.id.value),
            workspaceId = Workspace.Id(this.workspaceId.value),
            user = this.user.toDTO(),
            accessLevel = this.accessLevel,
            status = this.status,
            metadata = EntityMetadata.from(this),
        )
    }
}

object WorkspaceUsersSchema : BaseLongIdTable("workspace_users") {
    val status = enumerationByName("status", 50, WorkspaceUser.Status::class)
    val accessLevel = jsonb<List<WorkspaceUser.AccessLevel>>("access_level", otSerializer)
    val workspaceId = reference("workspace_id", Workspaces)
    val userId = reference("user_id", UserSchema)
    val tenantId = long("tenant_id").references(TenantSchema.id)

}


@Serializable
sealed class WorkspaceUser {

    @Serializable
    enum class AccessLevel(val relationship: Relationship) {
        SETTINGS(PermissionStructure.Workspace.Relationships.SETTINGS),
        CONFIGURATION(PermissionStructure.Workspace.Relationships.CONFIGURATION),
        COLLECTION(PermissionStructure.Workspace.Relationships.COLLECTION)
    }

    @Serializable
    enum class Status {
        ACTIVE, INACTIVE
    }

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    data class ForCreate(
        val workspaceId: Workspace.Id, val userId: User.Id, val accessLevel: List<AccessLevel>, val status: Status
    )

    @Serializable
    data class ForUpdate(
        val id: Id,
        val workspaceId: Workspace.Id,
        val userId: User.Id,
        val accessLevel: List<AccessLevel>? = null,
        val status: Status? = null
    )

    @Serializable
    data class ForApi(
        val id: Id,
        val workspaceId: Workspace.Id,
        val user: User.ForApi,
        val accessLevel: List<AccessLevel>,
        val status: Status,
        val metadata: EntityMetadata
    )

}