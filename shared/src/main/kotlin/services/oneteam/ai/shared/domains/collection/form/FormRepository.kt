package services.oneteam.ai.shared.domains.collection.form

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.json.jsonb
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.JsonValue.valueOrNull
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.domains.*
import services.oneteam.ai.shared.domains.collection.Visibility
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationEntity
import services.oneteam.ai.shared.domains.collection.foundation.FoundationsSchema
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.validation.Validator
import services.oneteam.ai.shared.extensions.paginate
import services.oneteam.ai.shared.otSerializer
import services.oneteam.ai.shared.permissions.CreateResourcePermissionRequest
import services.oneteam.ai.shared.permissions.DeleteResourcePermissionRequest
import services.oneteam.ai.shared.permissions.PermissionsService
import services.oneteam.ai.shared.permissions.ResourceType


class FormEntity(id: EntityID<Long>) : BaseLongEntity(id, FormsSchema) {
    companion object : BaseLongEntityClass<FormEntity>(FormsSchema)

    var formConfigurationId by FormsSchema.formConfigurationId
    var seriesId by FormsSchema.seriesId
    var intervalId by FormsSchema.intervalId
    var foundation by FoundationEntity referencedOn FormsSchema.foundation
    var documentId by FormsSchema.documentId
    var annotationDocumentId by FormsSchema.annotationDocumentId
    var workspace by WorkspaceEntity referencedOn FormsSchema.workspace
    var tenantId by FormsSchema.tenantId
    var properties by FormsSchema.properties
    var visibility by FormsSchema.visibility

    fun toDTO(): Form.ForApi {
        return Form.ForApi(
            id = Form.Id(this.id.value),
            formConfigurationId = FormConfiguration.Id(this.formConfigurationId),
            foundationId = Foundation.Id(this.foundation.id.value),
            intervalId = if (this.intervalId != null) IntervalId(this.intervalId!!) else null,
            seriesId = if (this.seriesId != null) SeriesConfiguration.Id(this.seriesId!!) else null,
            documentId = if (this.documentId != null) Form.DocumentId(this.documentId!!) else null,
            annotationDocumentId = if (this.annotationDocumentId != null) Form.AnnotationDocumentId(this.annotationDocumentId!!) else null,
            workspaceId = Workspace.Id(this.workspace.id.value),
            metadata = EntityMetadata.from(this),
            foundation = this.foundation.toDTO(),
            properties = if (this.properties != null) this.properties else null,
            visibility = this.visibility
        )
    }
}

object FormsSchema : BaseLongIdTable("forms") {
    val formConfigurationId = text("form_configuration_id")
    val seriesId = text("series_id").nullable()
    val intervalId = text("interval_id").nullable()
    val foundation = reference("foundation_id", FoundationsSchema)
    val properties = jsonb<JsonObject>("properties", otSerializer).nullable() // can later be strongly typed
    val visibility = enumerationByName("visibility", 50, Visibility::class)

    val documentId = text("document_id").nullable()
    val annotationDocumentId = text("annotation_document_id").nullable()

    val workspace = reference("workspace_id", Workspaces)
    val tenantId = long("tenant_id").references(id)
}

data class FormSearchCriteria(
    val workspaceId: Workspace.Id,
    val foundationId: Foundation.Id? = null,
    val foundationParentId: Foundation.Id? = null,
    val foundationConfigurationId: FoundationConfiguration.Id? = null,
    val formConfigurationId: FormConfiguration.Id? = null,
    val formConfigurationIdList: List<FormConfiguration.Id>? = null,
    val intervalIdList: List<IntervalId>? = null,
    val searchTerm: String? = null
) {
    companion object {
        // empty companion object so we can add extension functions
    }
}

@Serializable
sealed class Form {
    abstract val formConfigurationId: FormConfiguration.Id
    abstract val foundationId: Foundation.Id
    abstract val seriesId: SeriesConfiguration.Id? // TODO remove - we can derive this from formConfiguration
    abstract val intervalId: IntervalId?

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    @JvmInline
    value class DocumentId(val value: String)

    @Serializable
    @JvmInline
    value class AnnotationDocumentId(val value: String)

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    data class ForCreate(
        override val formConfigurationId: FormConfiguration.Id,
        override val foundationId: Foundation.Id,
        override val seriesId: SeriesConfiguration.Id? = null, // TODO remove
        override val intervalId: IntervalId? = null,
        val visibility: Visibility = Visibility.INHERIT,
    ) : Form()

    @Serializable
    data class ForUpdate(
        override val formConfigurationId: FormConfiguration.Id,
        override val foundationId: Foundation.Id,
        override val seriesId: SeriesConfiguration.Id? = null, // TODO remove
        override val intervalId: IntervalId? = null
    ) : Form()

    @Serializable
    data class ForApi(
        val id: Id,
        override val formConfigurationId: FormConfiguration.Id,
        override val foundationId: Foundation.Id,
        override val seriesId: SeriesConfiguration.Id? = null,
        override val intervalId: IntervalId? = null,
        val visibility: Visibility,
        val documentId: DocumentId? = null,
        val annotationDocumentId: AnnotationDocumentId? = null,
        val workspaceId: Workspace.Id,
        val metadata: EntityMetadata,
        val foundation: Foundation.ForApi,
        val properties: JsonObject? = null
    ) : Form() {
        fun toMap(foundationConfiguration: FoundationConfiguration.ForApi): JsonObject {
            return JsonObject(
                mapOf(
                    "id" to valueOrNull(id.value),
                    "formConfigurationId" to valueOrNull(formConfigurationId.value),
                    "foundationId" to valueOrNull(foundationId.value),
                    "foundation" to foundation.toMap(foundationConfiguration),
                    "seriesId" to valueOrNull(seriesId?.value),
                    "intervalId" to valueOrNull(intervalId?.value),
                    "documentId" to valueOrNull(documentId?.value),
                    "annotationDocumentId" to valueOrNull(annotationDocumentId?.value),
                    "properties" to (properties ?: JsonNull),
                )
            )
        }
    }

    fun validate(): ValidationErrors {
        val errors = Validator<Form>(
            listOf(

            )
        ).validate(this)

        return ValidationErrors.from(errors)
    }

}

class FormRepository(
    val check: Checks,
    val permissionsService: PermissionsService,
) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "name" to FoundationsSchema.name, "metadata.updatedAt" to FormsSchema.updatedAt
            ), FormsSchema.properties
        )
    }

    fun select(predicate: SqlExpressionBuilder.() -> Op<Boolean>): Form.ForApi? {
        return FormEntity.find { SqlExpressionBuilder.predicate() }.lastOrNull()?.toDTO()
    }

    fun selectMany(predicate: SqlExpressionBuilder.() -> Op<Boolean>): List<Form.ForApi>? {
        return FormEntity.find { SqlExpressionBuilder.predicate() }
            .map { it.toDTO() }
            .takeIf { it.isNotEmpty() }
    }

    fun getById(id: Long): Form.ForApi? {
        // join with foundation
        val dao = FormsSchema.innerJoin(FoundationsSchema).selectAll().andWhere { FormsSchema.id eq id }.toList()
        val result = dao.map(FormEntity::wrapRow).firstOrNull()
        return result?.toDTO()
    }


    fun getByIdAndWorkspaceId(id: Long, workspaceId: Long): Form.ForApi? {
        // join with foundation
        val query =
            FormsSchema.innerJoin(FoundationsSchema).selectAll()
                .andWhere { (FormsSchema.id eq id) and (FormsSchema.workspace eq workspaceId) }
        val dao = query.toList()
        val result = dao.map(FormEntity::wrapRow).firstOrNull()
        return result?.toDTO()
    }

    fun search(pageRequest: PageRequest, formSearchCriteria: FormSearchCriteria): Page<FormEntity> {
        return formSearchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
            FormEntity.wrapRow(it)
        }
    }

    fun delete(tenant: Tenant, id: Form.Id) {
        FormEntity.findById(id.value)?.delete()
        permissionsService.deleteResource(
            DeleteResourcePermissionRequest(
                ResourceType.FORM,
                id.value.toString(),
                tenant.id
            )
        )
    }

    fun create(tenant: Tenant, workspace: Workspace.ForApi, form: Form.ForCreate, forUserId: User.Id?): Form.ForApi {
        val workspaceEntity = WorkspaceEntity[workspace.id.value]
        val foundationEntity =
            check.exists(FoundationEntity.findById(form.foundationId.value)) { "Unknown foundation" }

        return permissionsService.createResource(
            CreateResourcePermissionRequest.ForForm(
                forUserId?.value?.toString(),
                tenant.id,
                workspaceEntity.id.value,
                foundationEntity.id.value.toString()
            )
        ) { id ->
            val dao = FormEntity.new(id) {
                formConfigurationId = form.formConfigurationId.value
                foundation = foundationEntity
                intervalId = form.intervalId?.value
                visibility = form.visibility

                tenantId = tenant.id
                this.workspace = workspaceEntity
            }
            return@createResource dao.toDTO()
        }
    }

    fun update(formId: Form.Id, form: Form.ForUpdate): Form.ForApi {
        val foundationEntity =
            check.exists(FoundationEntity.findById(form.foundationId.value)) { "Unknown foundation" }

        val dao = FormEntity.findByIdAndUpdate(formId.value) {
            it.formConfigurationId = form.formConfigurationId.value
            it.foundation = foundationEntity
            it.intervalId = form.intervalId?.value
        }
        return getById(dao!!.id.value)!!
    }

    fun updateProperties(formId: Form.Id, properties: JsonObject): Form.ForApi {
        return FormEntity.findByIdAndUpdate(formId.value) { it.properties = properties }!!.toDTO()
    }

    /**
     * This allows us to update fields after creation. Used to migrate objects when we add new capability or if we
     * have fields that are system controlled (not user controlled) and need to be lazily set.
     */
    fun updatePrivateFields(form: Form.ForApi): Form.ForApi {
        return FormEntity.findByIdAndUpdate(form.id.value) {
            it.documentId = form.documentId?.value
            it.annotationDocumentId = form.annotationDocumentId?.value
        }!!.toDTO()
    }

}

fun FormSearchCriteria.toQuery(): Query {
    val query =
        FormsSchema.innerJoin(FoundationsSchema).selectAll().andWhere { FormsSchema.workspace eq workspaceId.value }

    if (foundationId?.value != null) {
        query.andWhere { FormsSchema.foundation eq foundationId.value }
    }
    if (foundationParentId != null) {
        query.andWhere { FoundationsSchema.parentId eq foundationParentId.value }
    }
    if (foundationConfigurationId?.value?.isNotBlank() == true) {
        query.andWhere { FoundationsSchema.foundationConfigurationId eq foundationConfigurationId.value }
    }
    if (formConfigurationId?.value?.isNotBlank() == true) {
        query.andWhere { FormsSchema.formConfigurationId eq formConfigurationId.value }
    }

    val intervalIds = intervalIdList?.map(IntervalId::value).orEmpty()
    val formConfigurationIds = formConfigurationIdList?.map(FormConfiguration.Id::value).orEmpty()
    val lowerTerm = "%${searchTerm?.lowercase()}%"

    // need to do this because otherwise reading the jsonb column will match things if the query has things like : or "
    val strippedJson = CustomFunction(
        "regexp_replace",
        TextColumnType(),
        FormsSchema.properties.castTo(TextColumnType()),
        stringLiteral("""[{}\[\]":,]"""),
        //replace the above characters with an empty string
        stringLiteral(""),
        //globally
        stringLiteral("g")
    ).lowerCase()

    query.andWhere {
        (FormsSchema.intervalId inList intervalIds) or (FormsSchema.formConfigurationId inList formConfigurationIds) or (FoundationsSchema.name.lowerCase() like lowerTerm) or (strippedJson like lowerTerm)
    }


    return query
}
