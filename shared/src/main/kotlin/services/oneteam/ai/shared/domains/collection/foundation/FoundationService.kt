package services.oneteam.ai.shared.domains.collection.foundation

import kotlinx.coroutines.currentCoroutineContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.permissions.rebac.ResourceCollection
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.database.DatabaseUtils.nextValueOf
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.collection.Visibility
import services.oneteam.ai.shared.domains.collection.foundation.Foundation.Id
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.permissions.*
import services.oneteam.ai.shared.withTenantTransactionScope

class FoundationService(
    val foundationRepository: FoundationRepository,
    val workspaceVersionService: WorkspaceVersionService,
    val resourceRepository: ResourceRepository,
    val permissionService: PermissionsService,
    val foundationPermissionsService: FoundationPermissionService,
    val foundationMembersService: FoundationMembersService,
    val check: Checks
) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    private suspend fun augmentDto(
        foundation: Foundation.ForApi,
    ): Foundation.ForApi {
        val currentUserId = currentCoroutineContext()[RequestContext]?.principalId()
        val accessLevel = foundationMembersService.getAccessLevelForUser(foundation.id, currentUserId)
        return accessLevel?.let {
            foundation.copy(
                currentUser = Foundation.ForApi.CurrentUser(accessLevel)
            )
        } ?: foundation
    }

    suspend fun search(
        workspaceId: Workspace.Id,
        searchTerm: String?,
        parentKey: String?,
        parentConfigurationId: String?,
        pageRequest: PageRequest
    ): Page<Foundation.ForApi> = withTenantTransactionScope {

        // maybe do this as a subquery
        val parent =
            if (!parentKey.isNullOrBlank() && !parentConfigurationId.isNullOrBlank()) foundationRepository.getByKeyAndConfigurationId(
                workspaceId.value, parentKey, parentConfigurationId
            ) else foundationRepository.getRoot(
                workspaceId.value
            )

        val foundationSearchCriteria = FoundationSearchCriteria(
            workspaceId,
            searchTerm,
            parentId = parent?.id,
        )

        val searchResult = foundationRepository.search(
            foundationSearchCriteria, pageRequest
        )

        return@withTenantTransactionScope Page(
            searchResult.page,
            searchResult.total,
            searchResult.items.map { augmentDto(it.toDTO()) })
    }

    suspend fun searchByKeywords(
        workspaceId: Workspace.Id,
        pageRequest: PageRequest,
        keywords: String?,
        foundationConfigIds: List<FoundationConfiguration.Id>?
    ): Page<Foundation.ForApi> = withTenantTransactionScope {
        val foundationSearchCriteria = FoundationSearchCriteria(
            workspaceId, foundationConfigurationIdList = foundationConfigIds, name = keywords
        )
        val searchResult = foundationRepository.search(foundationSearchCriteria, pageRequest)
        return@withTenantTransactionScope Page(
            searchResult.page,
            searchResult.total,
            searchResult.items.map { augmentDto(it.toDTO()) })
    }

    suspend fun selectManyFoundations(
        params: SelectManyFoundationsParams
    ): SelectManyFoundationsResponse = withTenantTransactionScope {
        val isAllowNull = params.allowNull
        val foundations = selectMany(params)
        if (foundations != null && foundations.isNotEmpty()) {
            return@withTenantTransactionScope SelectManyFoundationsResponse(foundations)
        }
        if (isAllowNull == true) {
            return@withTenantTransactionScope SelectManyFoundationsResponse(null)
        }
        throw BadRequestException("Foundation not found")
    }

    suspend fun selectOrCreateFoundation(
        params: SelectOrCreateFoundationParams,
        userId: User.Id?
    ): SelectOrCreateFoundationResponse = withTenantTransactionScope {
        val isAllowNull = params.allowNull
        val foundation = select(params)
        val createIfNotExists = params.createIfNotExists
        if (foundation != null) {
            return@withTenantTransactionScope SelectOrCreateFoundationResponse(foundation, true)
        }
        if (createIfNotExists == true) {
            val foundationName = params.name?.value
            val parentId = params.parentId
            if (foundationName == null || foundationName == "" || parentId == null) {
                throw BadRequestException("name and parentId are required")
            }
            val workspace = workspaceVersionService.findVersion(params.workspaceId)
            val isFoundationContained = workspace.containsFoundation(params.foundationConfigurationId.value)
            val isRootFoundation = workspace.isRootFoundation(params.foundationConfigurationId.value)
            if (isRootFoundation || !isFoundationContained) {
                throw BadRequestException("Invalid Params")
            }
            val foundationForCreate = Foundation.ForCreate(
                name = params.name,
                key = params.key,
                foundationConfigurationId = params.foundationConfigurationId,
                workspaceId = params.workspaceId,
                parentId = parentId,
                visibility = params.visibility,
            )
            val newFoundation = create(foundationForCreate, userId)
            return@withTenantTransactionScope SelectOrCreateFoundationResponse(newFoundation, false)
        } else if (isAllowNull == true) {
            return@withTenantTransactionScope SelectOrCreateFoundationResponse(null, false)
        }
        throw BadRequestException("Foundation not found")
    }


    suspend fun select(params: SelectOrCreateFoundationParams): Foundation.ForApi? = withTenantTransactionScope {
        val predicate: SqlExpressionBuilder.() -> Op<Boolean> = {
            (FoundationsSchema.key eq params.key.value) and (FoundationsSchema.foundationConfigurationId eq params.foundationConfigurationId.value) and (FoundationsSchema.workspaceId eq params.workspaceId.value) and (params.name?.value?.takeIf { it.isNotBlank() }
                ?.let { FoundationsSchema.name eq it }
                ?: Op.TRUE) and (params.parentId?.value?.let { FoundationsSchema.parentId eq it } ?: Op.TRUE)
        }
        return@withTenantTransactionScope foundationRepository.select(predicate)?.let { augmentDto(it) }
    }

    suspend fun selectMany(params: SelectManyFoundationsParams): List<Foundation.ForApi>? = withTenantTransactionScope {
        val predicate: SqlExpressionBuilder.() -> Op<Boolean> = {
            (params.key?.value?.takeIf { it.isNotBlank() }
                ?.let { FoundationsSchema.key eq it }
                ?: Op.TRUE) and (FoundationsSchema.foundationConfigurationId eq params.foundationConfigurationId.value) and (FoundationsSchema.workspaceId eq params.workspaceId.value) and
                    (params.parentId?.value?.let { FoundationsSchema.parentId eq it } ?: Op.TRUE)
        }
        return@withTenantTransactionScope foundationRepository.selectMany(predicate)?.map { augmentDto(it) }
    }

    suspend fun validateFoundation(foundation: Foundation.ForCreate) = withTenantTransactionScope {
        val workspace = workspaceVersionService.findVersion(foundation.workspaceId)
        val orders = workspace.configuration.foundations.order
        val parentFoundation = get(id = foundation.parentId!!)
        val parentLevel = orders.indexOfFirst { it.value == parentFoundation.foundationConfigurationId.value }
        if (parentLevel + 1 < orders.size) {
            val nextFoundationConfigurationId = orders[parentLevel + 1].value
            if (foundation.foundationConfigurationId.value != nextFoundationConfigurationId) {
                throw BadRequestException("Invalid Request - mismatching foundation configuration for given parent id")
            }
        } else {
            throw BadRequestException("Invalid Request")
        }
    }

    /**
     * Create a new foundation for a workspace
     * - Check the foundationConfiguration should be the next level of parentId
     * - user must have edit/manage permissions on the parent foundation
     */
    suspend fun create(
        foundation: Foundation.ForCreate,
        userId: User.Id?, // userId can be null when a flow is calling this
        skipValidation: Boolean = false
    ): Foundation.ForApi =
        withTenantTransactionScope { tenant ->

            /*
             * Ensure the user has permissions to create under this parent foundation
             */
            foundationPermissionsService.apply {
                canCreate(foundation.workspaceId, foundation.parentId, userId).assertAllowed()
            }

            if (!skipValidation) {
                validateFoundation(foundation)
            }

            val newId = TransactionManager.current().nextValueOf(ResourceType.FOUNDATION.sequenceId)


            // update spicedb and sync to local index
            permissionService.update(
                tenant.id,
                FoundationRelationshipBuilder.buildAll(
                    tenant.id,
                    foundation.workspaceId,
                    Foundation.Id(newId),
                    foundation.parentId,
                    foundation.visibility,
                    userId?.let {
                        listOf(
                            FoundationMembers.ForApi.Summary(
                                userId = userId,
                                accessLevel = FoundationMembers.AccessLevel.MANAGER,
                                status = FoundationMembers.Status.ACTIVE
                            )
                        )
                    } ?: emptyList()
                ),
                listOf(
                    // since this is a new foundation it won't have any children yet, so we only need to update its own permissions
                    ResourceCollection(
                        PermissionStructure.Foundation.NAME,
                        setOf(newId)
                    )
                )
            )
            // create foundation in db
            val foundation = foundationRepository.create(newId, tenant, foundation).toDTO()

            // Add member when a user (not flow) is creating.
            val members = userId?.let {
                foundationMembersService.createMembers(
                    foundation,
                    listOf(
                        FoundationMembers.ForCreate(
                            userId,
                            // the user creating the foundation is a manager
                            FoundationMembers.AccessLevel.MANAGER,
                        )
                    )
                )
            }

            return@withTenantTransactionScope foundation
        }

    /**
     * Update foundation for a workspace
     * - Check that the linked foundation configuration exists for the workspace
     */
    suspend fun update(foundation: Foundation.ForUpdate): Foundation.ForApi = withTenantTransactionScope {
        // to do we'll need to get the appropriate workspace configuration version to validate
        // to do validation:
        //   check if foundation configuration exists for the workspace
        return@withTenantTransactionScope augmentDto(foundationRepository.update(foundation))
    }

    suspend fun updateProperties(
        foundationId: Id,
        properties: JsonObject,
        workspaceId: Workspace.Id? = null
    ): Foundation.ForApi =
        withTenantTransactionScope {
            findOneOrThrow(foundationId, workspaceId)
            return@withTenantTransactionScope augmentDto(
                foundationRepository.updateProperties(
                    foundationId,
                    properties
                )
            )
        }

    suspend fun updateVisibility(
        foundationId: Id,
        visibility: Visibility,
        userId: User.Id
    ): Foundation.ForApi {

        return withTenantTransactionScope { tenant ->
            val foundation = get(foundationId)

            /*
             * Ensure the user has permissions to change the visibility
             */
            foundationPermissionsService.apply {
                canChangeVisibility(foundation, userId).assertAllowed()
            }

            if (foundation.visibility == visibility) {
                logger.info("Foundation $foundationId already has visibility $visibility, no change needed")
                return@withTenantTransactionScope foundation
            }

            /*
             * If changing to INVITE_ONLY, ensure the user is a member of the foundation as a MANAGER
             */
            if (visibility == Visibility.INVITE_ONLY) {
                foundationMembersService.createMembers(
                    foundation,
                    listOf(
                        // create the user as a manager if they are not already a member, so they don't lock themselves out
                        FoundationMembers.ForCreate(
                            userId = userId,
                            // the user creating the foundation is a manager
                            accessLevel = FoundationMembers.AccessLevel.MANAGER,
                        )
                    )
                )
            }

            /*
             * Perform change
             */
            return@withTenantTransactionScope augmentDto(
                foundationRepository.updateVisibility(
                    tenant,
                    foundation.id,
                    visibility,
                    // could be optimised here by only passing those in the hierarchy from this point down
                    resourceRepository.findAllForWorkspace(foundation.workspaceId)
                )
            )
        }
    }

    suspend fun delete(foundationId: Id) = withTenantTransactionScope { tenant ->
        foundationRepository.delete(tenant, findOneOrThrow(foundationId).id)
    }

    suspend fun get(id: Id): Foundation.ForApi = withTenantTransactionScope {
        val members = foundationMembersService.getMembers(id)
        return@withTenantTransactionScope augmentDto(
            findOneOrThrow(id).toDTO().copy(members = members.map { it.toSummary() })
        )
    }

    suspend fun get(
        workspaceId: Workspace.Id, key: Foundation.Key, configurationId: FoundationConfiguration.Id
    ): Foundation.ForApi = withTenantTransactionScope {
        return@withTenantTransactionScope (check.exists(
            foundationRepository.getByKeyAndConfigurationId(
                workspaceId.value, key.value, configurationId.value
            )
        ) { "Unknown foundation" })
    }

    suspend fun getFoundationHierarchy(id: Id): List<Foundation.ForApi> = withTenantTransactionScope {
        val hierarchy = foundationRepository.getHierarchyForFoundation(id.value)
        check.exists(hierarchy) { "Unknown foundation" }
        return@withTenantTransactionScope hierarchy!!.map { augmentDto(it.toDTO()) }
    }

    suspend fun root(workspaceId: Workspace.Id): Foundation.ForApi = withTenantTransactionScope {
        return@withTenantTransactionScope foundationRepository.getRoot(workspaceId.value)
    }

    /**
     * Find a foundation by id or throw a not found exception.
     */
    private fun findOneOrThrow(foundationId: Id, workspaceId: Workspace.Id? = null): FoundationEntity {
        val foundationEntity = if (workspaceId != null) {
            foundationRepository.getByIdAndWorkspaceId(foundationId.value, workspaceId.value)
        } else {
            foundationRepository.getById(foundationId.value)
        }
        check.exists(foundationEntity) { "Unknown foundation $foundationId" }
        return foundationEntity!!
    }

}

@Serializable
data class SelectManyFoundationsParams(
    val workspaceId: Workspace.Id,
    val foundationConfigurationId: FoundationConfiguration.Id,
    val key: Foundation.Key? = null,
    val parentId: Id? = null,
    val allowNull: Boolean? = null,
)

@Serializable
data class SelectOrCreateFoundationParams(
    val workspaceId: Workspace.Id,
    val foundationConfigurationId: FoundationConfiguration.Id,
    val key: Foundation.Key,
    val name: Foundation.Name? = null,
    val parentId: Id? = null,
    val allowNull: Boolean? = null,
    val createIfNotExists: Boolean? = false,
    val visibility: Visibility = Visibility.INHERIT, // hardcoded to inherit because flows will be using this
)

@Serializable
data class SelectOrCreateFoundationResponse(
    val foundation: Foundation.ForApi?,
    val alreadyExists: Boolean = false,
)

@Serializable
data class SelectManyFoundationsResponse(
    val foundations: List<Foundation.ForApi>?,
)

@Serializable
data class FoundationPropertiesRequestBody(
    val properties: JsonObject,
    val workspaceId: Workspace.Id
)

@Serializable
data class FoundationVisibilityRequestBody(
    val visibility: Visibility
)
