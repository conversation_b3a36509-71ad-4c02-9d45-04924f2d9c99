package services.oneteam.ai.shared.domains.collection.foundation

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.json.jsonb
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.JsonValue.valueOrNull
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.domains.*
import services.oneteam.ai.shared.domains.collection.Visibility
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceEntity
import services.oneteam.ai.shared.domains.workspace.Workspaces
import services.oneteam.ai.shared.domains.workspace.validation.IdValidator
import services.oneteam.ai.shared.domains.workspace.validation.KeyValidator
import services.oneteam.ai.shared.domains.workspace.validation.NameValidator
import services.oneteam.ai.shared.domains.workspace.validation.Validator
import services.oneteam.ai.shared.extensions.paginate
import services.oneteam.ai.shared.otSerializer
import services.oneteam.ai.shared.permissions.CreateResourcePermissionRequest
import services.oneteam.ai.shared.permissions.DeleteResourcePermissionRequest
import services.oneteam.ai.shared.permissions.PermissionsService
import services.oneteam.ai.shared.permissions.ResourceType

class FoundationEntity(id: EntityID<Long>) : BaseLongEntity(id, FoundationsSchema) {
    companion object : BaseLongEntityClass<FoundationEntity>(FoundationsSchema)

    var name by FoundationsSchema.name
    var key by FoundationsSchema.key

    var parentId by FoundationsSchema.parentId
    var foundationConfigurationId by FoundationsSchema.foundationConfigurationId
    var workspace by WorkspaceEntity referencedOn FoundationsSchema.workspaceId
    var tenantId by FoundationsSchema.tenantId
    var properties by FoundationsSchema.properties
    var visibility by FoundationsSchema.visibility

    fun toDTO(): Foundation.ForApi {
        return Foundation.ForApi(
            id = Foundation.Id(this.id.value),
            name = Foundation.Name(this.name),
            key = Foundation.Key(this.key),
            foundationConfigurationId = FoundationConfiguration.Id(this.foundationConfigurationId.toString()),
            parentId = if (this.parentId == null) null else Foundation.Id(this.parentId!!),
            workspaceId = Workspace.Id(this.workspace.id.value),
            metadata = EntityMetadata.from(this),
            properties = if (this.properties != null) this.properties else null,
            visibility = this.visibility
        )
    }
}

object FoundationsSchema : BaseLongIdTable("foundations") {
    val name = text("name")
    val key = text("key")
    val parentId = long("parent_id").references(id).nullable()
    val foundationConfigurationId = text("foundation_configuration_id")
    val properties = jsonb<JsonObject>("properties", otSerializer).nullable() // can later be strongly typed
    val visibility = enumerationByName("visibility", 50, Visibility::class)
    val workspaceId = reference("workspace_id", Workspaces)
    val tenantId = long("tenant_id").references(id)
}

data class FoundationSearchCriteria(
    val workspaceId: Workspace.Id,
    val searchTerm: String? = null,
    val parentId: Long? = null,
    val name: String? = null,
    val foundationConfigurationIdList: List<FoundationConfiguration.Id>? = null,
) {
    companion object {
        // empty companion object so we can add extension functions
    }
}

@Serializable
sealed class Foundation {
    abstract val name: Name
    abstract val key: Key
    abstract val foundationConfigurationId: FoundationConfiguration.Id
    abstract val workspaceId: Workspace.Id
    abstract val parentId: Id?

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    @JvmInline
    value class Key(val value: String)

    @Serializable
    data class ForCreate(
        override val name: Name,
        override val key: Key,
        override val foundationConfigurationId: FoundationConfiguration.Id,
        override val workspaceId: Workspace.Id,
        override val parentId: Id?,
        val visibility: Visibility = Visibility.INHERIT,
    ) : Foundation()

    @Serializable
    data class ForUpdate(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val foundationConfigurationId: FoundationConfiguration.Id,
        override val workspaceId: Workspace.Id,
        override val parentId: Id?
    ) : Foundation()

    @Serializable
    data class ForApi(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val foundationConfigurationId: FoundationConfiguration.Id,
        override val workspaceId: Workspace.Id,
        override val parentId: Id?,
        val metadata: EntityMetadata,
        val properties: JsonObject? = null,
        val visibility: Visibility,
    ) : Foundation() {
        fun toMap(foundationConfiguration: FoundationConfiguration.ForApi): JsonObject {
            return JsonObject(
                mapOf(
                    "id" to valueOrNull(id.value),
                    "name" to valueOrNull(name.value),
                    "key" to valueOrNull(key.value),
                    "foundationConfigurationId" to valueOrNull(foundationConfigurationId.value),
                    "foundationConfiguration" to foundationConfiguration.toMap(),
                    "parentId" to valueOrNull(parentId?.value),
                    "properties" to (properties ?: JsonNull)
                )
            )
        }
    }

    fun validate(): ValidationErrors {
        val errors = Validator<Foundation>(
            listOf(
                NameValidator.build { obj: Foundation -> obj.name.value },
                KeyValidator.build { obj: Foundation -> obj.key.value },
                Validator.conditionalConstraint(
                    (this is ForUpdate),
                    IdValidator.build<Foundation> { obj -> (obj as Foundation.ForUpdate).id.value })
            )
        ).validate(this)

        return ValidationErrors.from(errors)
    }

}


class FoundationRepository(val permissionsService: PermissionsService) {
    val checks = Checks()
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "name" to FoundationsSchema.name, "metadata.updatedAt" to FoundationsSchema.updatedAt
            ),
            FoundationsSchema.properties,
        )
    }

    fun getRoot(workspaceId: Long): FoundationEntity {
        return FoundationEntity.find {
            FoundationsSchema.workspaceId eq workspaceId and FoundationsSchema.parentId.isNull()
        }.single()
    }

    fun select(predicate: SqlExpressionBuilder.() -> Op<Boolean>): Foundation.ForApi? {
        return FoundationEntity.find { SqlExpressionBuilder.predicate() }.firstOrNull()?.toDTO()
    }

    fun selectMany(predicate: SqlExpressionBuilder.() -> Op<Boolean>): List<Foundation.ForApi>? {
        return FoundationEntity.find { SqlExpressionBuilder.predicate() }
            .map { it.toDTO() }
            .takeIf { it.isNotEmpty() }
    }

    fun getById(id: Long): FoundationEntity? {
        return FoundationEntity.find { FoundationsSchema.id eq id }.firstOrNull()
    }

    fun getByIdAndWorkspaceId(id: Long, workspaceId: Long): FoundationEntity? {
        return FoundationEntity.find { (FoundationsSchema.id eq id) and (FoundationsSchema.workspaceId eq workspaceId) }
            .firstOrNull()
    }


    /**
     * @return Ordered list of foundations in the hierarchy, starting from the root (0) and ending with the foundation itself
     */
    fun getHierarchyForFoundation(foundationId: Long): List<FoundationEntity>? {
        val foundation = FoundationEntity.findById(foundationId)
        if (foundation == null) {
            logger.error("Foundation with id $foundationId not found")
            return null;
        }

        val hierarchy = mutableListOf<FoundationEntity>()
        var currentFoundation = foundation
        while (currentFoundation != null) {
            hierarchy.add(currentFoundation)

            val parentId = currentFoundation.parentId
            currentFoundation = if (parentId == null) {
                null
            } else {
                FoundationEntity.findById(parentId)
            }
        }

        return hierarchy.reversed()
    }

    fun getByKey(workspaceId: Long, key: String): FoundationEntity? {
        return FoundationEntity.find { FoundationsSchema.key eq key and (FoundationsSchema.workspaceId eq workspaceId) }
            .singleOrNull()
    }

    fun getByKeyAndConfigurationId(workspaceId: Long, key: String, configurationId: String): FoundationEntity? {
        return FoundationEntity.find { FoundationsSchema.foundationConfigurationId eq configurationId and (FoundationsSchema.key eq key) and (FoundationsSchema.workspaceId eq workspaceId) }
            .singleOrNull()
    }

    fun search(
        foundationSearchCriteria: FoundationSearchCriteria, pageRequest: PageRequest
    ): Page<FoundationEntity> {
        return foundationSearchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
            FoundationEntity.wrapRow(it)
        }
    }

    fun delete(tenant: Tenant, id: EntityID<Long>) {
        FoundationEntity.findById(id.value)?.delete()
        permissionsService.deleteResource(
            DeleteResourcePermissionRequest(
                ResourceType.FOUNDATION,
                id.value.toString(),
                tenant.id
            )
        )
    }

    fun create(
        tenant: Tenant,
        foundation: Foundation.ForCreate,
        forUserId: User.Id?
    ): FoundationEntity {

        val workspaceEntity = WorkspaceEntity.findById(foundation.workspaceId.value)
            ?: throw NotFoundException("Workspace ${foundation.workspaceId.value} not found")

        return permissionsService.createResource(
            CreateResourcePermissionRequest.ForFoundation(
                forUserId?.value?.toString(),
                tenant.id,
                foundation.workspaceId.value,
                ResourceType.FOUNDATION,
                foundation.parentId?.value?.toString()
            )
        ) { id ->
            val dao = FoundationEntity.new(id) {
                name = foundation.name.value
                key = foundation.key.value
            visibility = foundation.visibility
                foundationConfigurationId = foundation.foundationConfigurationId.value

                parentId = foundation.parentId?.value
                tenantId = tenant.id
                workspace = workspaceEntity
            }
            return@createResource dao
        }
    }

    fun update(foundation: Foundation.ForUpdate): FoundationEntity {
        return FoundationEntity.findByIdAndUpdate(foundation.id.value) {
            it.name = foundation.name.value
            it.key = foundation.key.value
            it.foundationConfigurationId = foundation.foundationConfigurationId.value
            it.parentId = foundation.parentId?.value
        }!!
    }

    fun updateProperties(foundationId: Foundation.Id, properties: JsonObject): Foundation.ForApi {
        return FoundationEntity.findByIdAndUpdate(foundationId.value) { it.properties = properties }!!.toDTO()
    }

    private fun FoundationSearchCriteria.toQuery(): Query {
        val query = FoundationsSchema.selectAll()
        query.andWhere { FoundationsSchema.workspaceId eq workspaceId.value }
        if (!searchTerm.isNullOrEmpty()) {
            query.andWhere { FoundationsSchema.name.lowerCase() like "%${searchTerm.lowercase()}%" }
        }
        if (parentId != null) {
            query.andWhere { FoundationsSchema.parentId eq parentId }
        }
        if (foundationConfigurationIdList != null) {
            val foundationConfigurationIds = foundationConfigurationIdList.map(FoundationConfiguration.Id::value)
            query.andWhere {
                FoundationsSchema.foundationConfigurationId inList foundationConfigurationIds or (FoundationsSchema.name.lowerCase() like "%${name?.lowercase()}%") or (FoundationsSchema.key.lowerCase() like "%${name?.lowercase()}%")
            }
        }

        return query
    }

}