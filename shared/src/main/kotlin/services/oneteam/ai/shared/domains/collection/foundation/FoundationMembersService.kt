package services.oneteam.ai.shared.domains.collection.foundation

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserService
import services.oneteam.ai.shared.domains.workspace.WorkspaceUser
import services.oneteam.ai.shared.domains.workspace.WorkspaceUserService
import services.oneteam.ai.shared.permissions.FoundationPermissionService
import services.oneteam.ai.shared.permissions.FoundationRelationshipBuilder
import services.oneteam.ai.shared.permissions.PermissionsService
import services.oneteam.ai.shared.permissions.ResourceRepository
import services.oneteam.ai.shared.withCurrentUser
import services.oneteam.ai.shared.withTenantTransactionScope

class FoundationMembersService(
    private val foundationPermissionsService: FoundationPermissionService,
    private val permissionsService: PermissionsService,
    private val foundationMembersRepository: FoundationMembersRepository,
    private val resourceRepository: ResourceRepository,
    private val workspaceUserService: WorkspaceUserService,
    private val userService: UserService,
) {

    /**
     * Update permissions in SpiceDB and sync to local index for all resources in the foundation's workspace
     * for affected members.
     */
    suspend fun doPermissionsUpdate(
        foundation: Foundation.ForApi,
        members: List<FoundationMembers.ForApi.Summary>
    ) {
        withTenantTransactionScope { tenant ->
            // update spicedb and sync to local index
            permissionsService.update(
                tenant.id,
                FoundationRelationshipBuilder.buildMembers(
                    foundation.id,
                    members
                ),
                // this could be optimized to only sync the affected users
                resourceRepository.findAllForWorkspace(foundation.workspaceId)
            )

        }
    }

    suspend fun getMembers(foundationId: Foundation.Id): List<FoundationMembers.ForApi> = withTenantTransactionScope {
        val activeMembers = foundationMembersRepository.search(
            FoundationMembersSearchCriteria(foundationId),
            PageRequest.forMax()
        )

        return@withTenantTransactionScope activeMembers.items
    }

    /**
     * Will create or update members as needed, ensuring workspace user access levels are correct as well.
     * This will do an upsert and set status to active if the member already exists but was inactive.
     */
    suspend fun createMembers(
        foundation: Foundation.ForApi,
        members: List<FoundationMembers.ForCreate>
    ): List<FoundationMembers.ForApi> =
        withCurrentUser { currentUser ->
            val newMembers = withTenantTransactionScope { tenant ->

                members.map { member ->

                    // Ensure the user to be added exists and is visible
                    userService.findById(member.userId)

                    // Ensure the current user has permissions to add members
                    // A user cannot grant a higher access level than they have themselves
                    foundationPermissionsService.apply {
                        canManageMembers(
                            foundation.id,
                            User.Id(currentUser.user.id),
                            member.accessLevel
                        ).assertAllowed()
                    }

                    // If any member is not in the workspace users they need to be added to workspace users table with accessLevel set to just collections
                    // If they are already a workspace user then we need to add collections to their accessLevel if not already present
                    // If any member is in the workspace users as status=inactive TBD - for now throw an error
                    // BUT, we can only do that if the user making this request has SETTINGS access to the workspace.
                    workspaceUserService.upsertMember(
                        foundation.workspaceId,
                        member.userId,
                        listOf(WorkspaceUser.AccessLevel.COLLECTION)
                    )

                    // Now add the foundation member or update if they already exist
                    // If a member is on the members list with status = inactive, change their status to active (and update access level to new one as well)
                    foundationMembersRepository.upsert(
                        tenant,
                        foundation.workspaceId,
                        foundationId = foundation.id,
                        userId = member.userId,
                        accessLevel = member.accessLevel,
                        status = FoundationMembers.Status.ACTIVE,
                    )
                }
            }

            doPermissionsUpdate(foundation, newMembers.map { it.toSummary() })

            newMembers
        }


    suspend fun updateMembers(
        foundation: Foundation.ForApi,
        members: List<FoundationMembers.ForUpdate>
    ): List<FoundationMembers.ForApi> =
        withCurrentUser { currentUser ->
            val members = withTenantTransactionScope {

                // A user cannot deactivate others if there will be no remaining MANAGER of the foundation
                fun getActiveManagers(): Page<FoundationMembers.ForApi> = foundationMembersRepository.search(
                    FoundationMembersSearchCriteria(
                        foundationId = foundation.id,
                        accessLevel = FoundationMembers.AccessLevel.MANAGER,
                        status = FoundationMembers.Status.ACTIVE
                    ), PageRequest.forMax()
                )

                val existingActiveManagers = getActiveManagers().items.map { it.userId }
                val newManagers =
                    members.filter { it.accessLevel == FoundationMembers.AccessLevel.MANAGER }.map { it.userId }
                val usersToChangeThatAreNotManagers =
                    members.filter { it.accessLevel != FoundationMembers.AccessLevel.MANAGER }.map { it.userId }.toSet()
                val willHaveRemainingManagers =
                    existingActiveManagers.plus(newManagers).minus(usersToChangeThatAreNotManagers).isNotEmpty()

                if (!willHaveRemainingManagers) {
                    throw BadRequestException("There must be at least one active MANAGER member of a foundation")
                }

                members.map { member ->

                    if (currentUser.user.id == member.userId.value) {
                        throw BadRequestException("A user cannot update themselves")
                    }

                    val existingMember =
                        foundationMembersRepository.findByFoundationAndUser(foundation.id, member.userId)
                            ?: throw BadRequestException("Member with userId ${member.userId.value} not found")

                    // Ensure the current user has permissions to add members
                    // A user cannot grant a higher access level than they have themselves
                    foundationPermissionsService.apply {
                        canManageMembers(
                            foundation.id,
                            User.Id(currentUser.user.id),
                            member.accessLevel
                        ).assertAllowed()
                    }

                    foundationMembersRepository.update(
                        existingMember.id,
                        accessLevel = member.accessLevel,
                        status = existingMember.status,
                    )
                }
            }

            doPermissionsUpdate(foundation, members.map { it.toSummary() })

            members
        }

    /**
     * Deactivate members - change status to inactive
     *
     * A user cannot deactivate themselves
     * A user cannot deactivate a member with a higher access level than they have themselves
     * A user cannot deactivate others if there will be no remaining MANAGER of the foundation
     */
    suspend fun deactivateMembers(
        foundation: Foundation.ForApi,
        members: List<FoundationMembers.ForDeactivate>
    ): List<FoundationMembers.ForApi> =
        withCurrentUser { currentUser ->
            val members = withTenantTransactionScope {

                // A user cannot deactivate others if there will be no remaining MANAGER of the foundation
                fun getActiveManagers(): Page<FoundationMembers.ForApi> = foundationMembersRepository.search(
                    FoundationMembersSearchCriteria(
                        foundationId = foundation.id,
                        accessLevel = FoundationMembers.AccessLevel.MANAGER,
                        status = FoundationMembers.Status.ACTIVE
                    ), PageRequest.forMax()
                )

                val existingActiveManagers = getActiveManagers().items.map { it.userId }
                val usersToDeactivate = members.map { it.userId }.toSet()
                val willHaveRemainingManagers = existingActiveManagers.minus(usersToDeactivate).isNotEmpty()

                if (!willHaveRemainingManagers) {
                    throw BadRequestException("There must be at least one active MANAGER member of a foundation")
                }

                members.map { member ->

                    if (currentUser.user.id == member.userId.value) {
                        throw BadRequestException("You cannot deactivate yourself")
                    }

                    val existingMember =
                        foundationMembersRepository.findByFoundationAndUser(foundation.id, member.userId)
                            ?: throw BadRequestException("Member with userId ${member.userId.value} not found")

                    // Ensure the current user has permissions to add members
                    // A user cannot deactivate a member with a higher access level than they have themselves
                    foundationPermissionsService.apply {
                        canManageMembers(
                            foundation.id,
                            User.Id(currentUser.user.id),
                            existingMember.accessLevel
                        ).assertAllowed()
                    }

                    // Update the member to inactive
                    foundationMembersRepository.update(
                        existingMember.id,
                        accessLevel = existingMember.accessLevel,
                        status = FoundationMembers.Status.INACTIVE,
                    )
                }
            }

            doPermissionsUpdate(foundation, members.map { it.toSummary() })

            members
        }

    suspend fun search(
        foundationMembersSearchCriteria: FoundationMembersSearchCriteria,
        pageRequest: PageRequest
    ): Page<FoundationMembers.ForApi> = withTenantTransactionScope {
        foundationMembersRepository.search(foundationMembersSearchCriteria, pageRequest)
    }

    fun getAccessLevelForUser(id: Foundation.Id, currentUserId: Long?): FoundationMembers.AccessLevel? {
        if (currentUserId == null) {
            return null
        }
        return foundationPermissionsService.getAccessLevelForUser(id, User.Id(currentUserId))
    }
}

@OptIn(ExperimentalSerializationApi::class)
@JsonClassDiscriminator("operation")
@Serializable
sealed class FoundationMembersRequestBody {

    @Serializable
    @SerialName("create")
    data class Create(
        val members: List<FoundationMembers.ForCreate>
    )

    @Serializable
    @SerialName("update")
    data class Update(
        val members: List<FoundationMembers.ForUpdate>
    )


    @Serializable
    @SerialName("deactivate")
    data class Deactivate(
        val members: List<FoundationMembers.ForDeactivate>
    )
}