package services.oneteam.ai.shared.permissions

import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.permissions.rebac.LocalRLSIndexSyncService
import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.permissions.rebac.Relationship
import services.oneteam.ai.permissions.rebac.SubjectResourcePermissionsRepository
import services.oneteam.ai.shared.database.DatabaseUtils.nextValueOf

sealed class CreateResourcePermissionRequest {
    abstract val resourceType: ResourceType
    abstract val subjectId: String?
    abstract val tenantId: Long
    abstract val workspaceId: Long?
    abstract val parentType: ResourceType?
    abstract val parentId: String?

    abstract val relationships: List<Relationship>

    data class ForForm(
        override val subjectId: String?,
        override val tenantId: Long,
        override val workspaceId: Long,
        override val parentId: String // parent foundation id
    ) : CreateResourcePermissionRequest() {
        override val resourceType = ResourceType.FORM
        override val relationships = listOf(PermissionStructure.Form.Relationships.MANAGER)
        override val parentType: ResourceType = ResourceType.FOUNDATION
    }

    data class ForFoundation(
        override val subjectId: String?,
        override val tenantId: Long,
        override val workspaceId: Long,

        // parent can be foundation or workspace
        override val parentType: ResourceType? = null,
        override val parentId: String? = null
    ) : CreateResourcePermissionRequest() {
        override val resourceType = ResourceType.FOUNDATION
        override val relationships = listOf(PermissionStructure.Foundation.Relationships.MANAGER)
    }

    data class ForWorkspace(
        override val subjectId: String,
        override val tenantId: Long,
    ) : CreateResourcePermissionRequest() {
        override val resourceType: ResourceType = ResourceType.WORKSPACE
        override val relationships = PermissionStructure.Workspace.Relationships.accessLevels()
        override val workspaceId: Long? = null
        override val parentType: ResourceType? = null
        override val parentId: String? = null
    }
}

data class DeleteResourcePermissionRequest(
    val resourceType: ResourceType,
    val resourceId: String,
    val tenantId: Long
)

data class SetRelationshipsPermissionRequest(
    val resourceType: ResourceType,
    val resourceId: String,
    val subjectId: String,
    val relationships: List<Relationship>,
    val tenantId: Long
)

/**
 * Service for managing permissions related to resources. This service coordinates spicedb and the local index
 * `subject_resource_permissions` table.
 */
class PermissionsService(
    val localIndexRepository: SubjectResourcePermissionsRepository,
    val relationshipRepository: RelationshipRepository,
    val localRLSIndexSyncService: LocalRLSIndexSyncService,
) {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun deleteResource(permissionRequest: DeleteResourcePermissionRequest) {
        localIndexRepository.deleteByResource(
            permissionRequest.resourceType.id,
            permissionRequest.resourceId.toLong(), permissionRequest.tenantId
        )
        relationshipRepository.deleteResource(permissionRequest)
        // TODO should we delete all things related to this resource in spicedb?
    }

    fun setRelationships(permissionRequest: SetRelationshipsPermissionRequest) {
        val token = relationshipRepository.setRelationships(permissionRequest)
        // sync all permissions for the subject in the local index because many resources can be affected due to inheritance
        localRLSIndexSyncService.syncViewBySubject(
            permissionRequest.tenantId,
            permissionRequest.subjectId.toLong(),
            token
        )
    }

    /**
     * Creates a resource and adds the necessary permissions for the subject by updating spicedb and `subject_resource_permissions` table.
     */
    fun <T> createResource(permissionRequest: CreateResourcePermissionRequest, block: (id: Long) -> T): T {

        val newResourceId = TransactionManager.current().nextValueOf(permissionRequest.resourceType.sequenceId)

        try {
            // create the resource
            val token = relationshipRepository.doCreate(permissionRequest, newResourceId.toString())
            // update index
            updateIndexByResource(
                permissionRequest.resourceType,
                newResourceId,
                permissionRequest.tenantId,
                token
            )

            return block(newResourceId)
        } catch (e: Exception) {
            // roll back any changes made to spicedb
            relationshipRepository.deleteAllRelationshipsForResource(
                permissionRequest.resourceType.key,
                newResourceId.toString()
            )
            throw e
        }
    }

    private fun updateIndexByResource(
        resourceType: ResourceType,
        resourceId: Long,
        tenantId: Long,
        token: String?,
    ) {
        // query to find the users that would inherit view permissions from the resource
        val visibleTo = relationshipRepository.findSubjectsWithView(resourceType, resourceId.toString(), token)

        val subjectIds = visibleTo.map { it.toLong() }
        logger.debug("{} {} will be visible to users: {}", resourceType.key, resourceId, visibleTo)

        localIndexRepository.updateForResourceAndSubjects(
            resourceId,
            resourceType.id,
            subjectIds,
            tenantId
        )
    }

}