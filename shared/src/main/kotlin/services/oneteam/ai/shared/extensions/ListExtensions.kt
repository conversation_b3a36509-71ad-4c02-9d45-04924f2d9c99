package services.oneteam.ai.shared.extensions

import java.math.BigDecimal
import java.time.LocalDate

fun List<*>.toBigDecimalList(): List<BigDecimal> {
    return this.mapNotNull {
        toNumberOrNull(it)
    }
}

// Handle both numbers and strings that can be parsed as numbers
fun isNumeric(any: Any?): Boolean = any is Number || (any is String && any.toDoubleOrNull() != null)

val allNumbers =
    { inputs: Array<out Any?> -> inputs.all { isNumeric(it) } }

/**
 * Recursively checks if all elements in the input array (and any nested lists) are numeric.
 */
fun allNumbersRecursive(inputs: Array<out Any?>): Boolean =
    inputs.all {
        when (it) {
            is List<*> -> allNumbersRecursive(it.toTypedArray())
            else -> isNumeric(it)
        }
    }

fun anyNumericRecursive(inputs: Array<out Any?>): Boolean =
    inputs.any {
        when (it) {
            is List<*> -> anyNumericRecursive(it.toTypedArray())
            else -> isNumeric(it)
        }
    }


fun toNumberOrNull(any: Any?): BigDecimal? = any.toString().toDoubleOrNull()?.toBigDecimal()?.setScale(scale)

val toNumber = { inputs: Array<out Any?> ->
    inputs.map {
        toNumberOrNull(it)
    }
}

fun maybeParseDate(value: Any?): LocalDate? {
    return if (value is String) {
        try {
            LocalDate.parse(value)
        } catch (_: Exception) {
            null
        }
    } else {
        null
    }
}

fun isParsableDate(value: Any?): Boolean {
    if (value == "") {
        // Empty string is used as date default value
        return true
    }
    return maybeParseDate(value) != null
}

val allDates = { inputs: Array<out Any?> ->
    inputs.all { isParsableDate(it) }
}

/**
 * Recursively checks if all elements in the input array (and any nested lists) are parsable dates.
 */
fun allDatesRecursive(inputs: Array<out Any?>): Boolean =
    inputs.all {
        when (it) {
            is List<*> -> allDatesRecursive(it.toTypedArray())
            else -> isParsableDate(it)
        }
    }

fun anyDateRecursive(inputs: Array<out Any?>): Boolean =
    inputs.any {
        when (it) {
            is List<*> -> anyDateRecursive(it.toTypedArray())
            else -> isParsableDate(it)
        }
    }