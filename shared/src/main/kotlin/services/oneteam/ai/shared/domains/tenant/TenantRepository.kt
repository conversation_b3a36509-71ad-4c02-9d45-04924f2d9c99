package services.oneteam.ai.shared.domains.tenant

import org.jetbrains.exposed.dao.id.EntityID
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.database.BaseLongIdTable
import services.oneteam.ai.shared.domains.NotFoundException

object TenantSchema : BaseLongIdTable("tenants") {
    val name = varchar("name", length = 255)
    val originUrl = varchar("origin_url", length = 255).uniqueIndex()
    val internalUrl = varchar("internal_url", length = 255)
    val internalSyncUrl = varchar("internal_sync_url", length = 255)
}

class TenantDAO(id: EntityID<Long>) : BaseLongEntity(id, TenantSchema) {
    companion object : BaseLongEntityClass<TenantDAO>(TenantSchema)

    var name by TenantSchema.name
    var originUrl by TenantSchema.originUrl
    var internalUrl by TenantSchema.internalUrl
    var internalSyncUrl by TenantSchema.internalSyncUrl
}

class TenantRepository {

    fun create(tenant: Tenant): Long {
        val dao = TenantDAO.new {
            name = tenant.name
            originUrl = tenant.originUrl
            internalUrl = tenant.internalUrl
            internalSyncUrl = tenant.internalSyncUrl
        }
        return dao.id.value
    }

    fun getAll(): List<Tenant> {
        return TenantDAO.all().map { Tenant(it.id.value, it.name, it.originUrl, it.internalUrl, it.internalSyncUrl) }
    }

    fun getByOriginUrl(originUrl: String): Tenant? {
        val tenants = TenantDAO.find { TenantSchema.originUrl eq originUrl }
        if (tenants.empty()) {
            return null
        }
        return fromDao(tenants.first())
    }

    fun getByInternalUrl(internalUrl: String): Tenant? {
        val tenants = TenantDAO.find { TenantSchema.internalUrl eq internalUrl }
        if (tenants.empty()) {
            return null
        }
        return fromDao(tenants.first())
    }

    fun fromDao(tenant: TenantDAO): Tenant {
        return Tenant(tenant.id.value, tenant.name, tenant.originUrl, tenant.internalUrl, tenant.internalSyncUrl)
    }

    fun getById(id: Long): Tenant {
        val tenant = TenantDAO.findById(id) ?: throw NotFoundException("Tenant with id $id not found")
        return fromDao(tenant)
    }
}
