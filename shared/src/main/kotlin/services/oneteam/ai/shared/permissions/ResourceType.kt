package services.oneteam.ai.shared.permissions

import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.permissions.rebac.Resource
import services.oneteam.ai.permissions.rebac.ResourceTypeIds

/**
 * These represent the different types of resources that can be managed in the system.
 * We use INT IDs to keep the amount of data in the database small and manageable and efficient.
 * The IDs are used in the database RLS policies to reference the resource type.
 *
 * DO NOT CHANGE THE IDs.
 */
enum class ResourceType(val key: Resource, val id: Int, val sequenceId: String) {
    FOUNDATION(PermissionStructure.Foundation.NAME, ResourceTypeIds.FOUNDATION, "foundations_id_seq"),
    FORM(PermissionStructure.Form.NAME, ResourceTypeIds.FORM, "forms_id_seq"),
    WORKSPACE(PermissionStructure.Workspace.NAME, ResourceTypeIds.WORKSPACE, "workspaces_id_seq"),
    TENANT(PermissionStructure.Tenant.NAME, ResourceTypeIds.TENANT, "tenants_id_seq"), ;

    companion object {
        fun fromId(id: Int): ResourceType {
            return entries.find { it.id == id }
                ?: throw IllegalArgumentException("No ResourceType found for id: $id")
        }
    }
}