package services.oneteam.ai.shared.permissions

import services.oneteam.ai.permissions.rebac.LocalRLSIndexSyncService
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.domains.user.UserSearchCriteria
import services.oneteam.ai.shared.withTenantTransactionScope

class SyncUser(
    val localRLSIndexSyncService: LocalRLSIndexSyncService,
    val userRepository: UserRepository,
) {

    suspend fun syncUsers() {
        withTenantTransactionScope {
            userRepository.searchByCriteria(
                PageRequest.forMax(),
                UserSearchCriteria("")
            )
        }.items.forEach { user ->
            // sync view for this workspace
            withTenantTransactionScope {
                localRLSIndexSyncService.syncViewBySubject(
                    user.tenantId, user.id.value
                )
            }
        }
    }
}