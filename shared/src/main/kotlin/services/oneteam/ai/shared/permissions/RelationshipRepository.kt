package services.oneteam.ai.shared.permissions

import services.oneteam.ai.permissions.rebac.Permission
import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.permissions.rebac.RebacRepository
import services.oneteam.ai.permissions.rebac.Resource
import services.oneteam.ai.shared.logger

/**
 * This is a generic ReBAC (Resource-Based Access Control) repository for managing relationships.
 * It is backed by a RebacRepository which is an abstraction over a ReBAC system like SpiceDB or Permify.
 *
 * It knows about WHAT relationships to create/delete when, but doesn't know HOW. It relies on the
 * specific implementation of a ReBAC Repository to perform the actual operations against an implementation like
 * SpiceDB or Permify etc.
 *
 * While atm this is very Workspace/Foundation/Form centric, we should adapt it to suit our needs
 *
 * The KEY point is that it knows about our domain and the relationships we want to create/delete,
 * but it does not know about the underlying implementation details of how those relationships are stored.
 */
class RelationshipRepository(
    private val rebacRepository: RebacRepository
) {

    /**
     * Deletes all relationships for a resource in SpiceDB.
     */
    fun deleteResource(permissionRequest: DeleteResourcePermissionRequest) {
        rebacRepository.deleteAllRelationshipsForResource(
            permissionRequest.resourceType.key,
            permissionRequest.resourceId
        )
    }

    /**
     * Finds all subjects that can view a given resource.
     */
    fun findSubjectsWithView(
        resourceType: ResourceType,
        resourceId: String,
        permission: Permission,
        token: String?
    ): List<String> =
        rebacRepository.findAllSubjectsWithPermissionOnResource(
            resourceType.key,
            resourceId,
            permission,
            token
        )

    fun setRelationships(permissionRequest: SetRelationshipsPermissionRequest): String {
        var token: String = deleteRelationshipForResourceAndSubject(
            permissionRequest.resourceType.key,
            permissionRequest.resourceId,
            permissionRequest.subjectId,
        )

        permissionRequest.relationships.forEach { relationship ->
            // add relationship for this new resource to spicedb
            token = rebacRepository.writeRelationship(
                permissionRequest.resourceType.key,
                permissionRequest.resourceId,
                relationship,
                PermissionStructure.Subject.NAME,
                permissionRequest.subjectId,
            )
        }
        return token
    }

    fun deleteAllRelationshipsForResource(resourceType: Resource, resourceId: String): String {
        return rebacRepository.deleteAllRelationshipsForResource(resourceType, resourceId)
    }

    fun deleteRelationshipForResourceAndSubject(resourceType: Resource, resourceId: String, subjectId: String): String {
        return rebacRepository.deleteRelationshipsForResourceAndSubject(resourceType, resourceId, subjectId)
    }

    fun deleteRelationship(relationship: RelationshipDefinition): String {
        return rebacRepository.deleteRelationshipForResource(
            relationship.resourceType.key,
            relationship.resourceId,
            relationship.relationship
        )
    }

    fun touchRelationship(relationship: RelationshipDefinition): String {
        return rebacRepository.writeRelationship(
            relationship.resourceType.key,
            relationship.resourceId,
            relationship.relationship,
            relationship.subjectType.key,
            relationship.subjectId,
        ).also {
            logger.trace("Touched relationship: {} with token: {}", relationship, it)
        }
    }

}