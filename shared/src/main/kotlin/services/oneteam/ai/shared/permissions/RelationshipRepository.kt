package services.oneteam.ai.shared.permissions

import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.permissions.rebac.PermissionStructure.Common
import services.oneteam.ai.permissions.rebac.RebacRepository
import services.oneteam.ai.permissions.rebac.Resource

/**
 * This is a generic ReBAC (Resource-Based Access Control) repository for managing relationships.
 * It is backed by a RebacRepository which is an abstraction over a ReBAC system like SpiceDB or Permify.
 *
 * It knows about WHAT relationships to create/delete when, but doesn't know HOW. It relies on the
 * specific implementation of a ReBAC Repository to perform the actual operations against an implementation like
 * SpiceDB or Permify etc.
 *
 * While atm this is very Workspace/Foundation/Form centric, we should adapt it to suit our needs
 *
 * The KEY point is that it knows about our domain and the relationships we want to create/delete,
 * but it does not know about the underlying implementation details of how those relationships are stored.
 */
class RelationshipRepository(
    private val rebacRepository: RebacRepository
) {

    /**
     * Deletes all relationships for a resource in SpiceDB.
     */
    fun deleteResource(permissionRequest: DeleteResourcePermissionRequest) {
        rebacRepository.deleteAllRelationshipsForResource(
            permissionRequest.resourceType.key,
            permissionRequest.resourceId
        )
    }

    /**
     * Finds all subjects that can view a given resource.
     */
    fun findSubjectsWithView(
        resourceType: ResourceType,
        resourceId: String,
        token: String?
    ): List<String> =
        rebacRepository.findAllSubjectsWithPermissionOnResource(
            resourceType.key,
            resourceId,
            Common.Permissions.VIEW,
            token
        )

    /**
     * Creates a new resource and sets the initial relationships for it.
     */
    fun doCreate(
        permissionRequest: CreateResourcePermissionRequest,
        resourceId: String
    ): String {

        // add relationship for this new resource to spicedb
        var token = rebacRepository.writeRelationship(
            permissionRequest.resourceType.key,
            resourceId,
            Common.Relationships.TENANT,
            ResourceType.TENANT.key,
            permissionRequest.tenantId.toString(),
        )

        // can be null if the resource is not associated with a workspace (tenant or workspace)
        if (permissionRequest.workspaceId != null) {
            token = rebacRepository.writeRelationship(
                permissionRequest.resourceType.key,
                resourceId,
                Common.Relationships.WORKSPACE,
                ResourceType.WORKSPACE.key,
                permissionRequest.workspaceId.toString(),
            )
        }

        if (permissionRequest.subjectId != null && permissionRequest.relationships.isNotEmpty()) {
            token = setRelationships(
                SetRelationshipsPermissionRequest(
                    resourceType = permissionRequest.resourceType,
                    resourceId = resourceId,
                    subjectId = permissionRequest.subjectId!!,
                    relationships = permissionRequest.relationships,
                    tenantId = permissionRequest.tenantId,
                )
            )
        }

        if (permissionRequest.parentId != null) {
            require(permissionRequest.parentType != null) { "parentType cannot be null when parentId is provided" }
            // add relationship for this new resource to spicedb
            token = rebacRepository.writeRelationship(
                permissionRequest.resourceType.key,
                resourceId,
                Common.Relationships.PARENT,
                permissionRequest.parentType!!.key,
                permissionRequest.parentId!!
            )
            if (permissionRequest.parentType != ResourceType.WORKSPACE) {
                token = rebacRepository.writeRelationship(
                    permissionRequest.resourceType.key,
                    resourceId,
                    Common.Relationships.INHERIT_FROM,
                    permissionRequest.parentType!!.key,
                    permissionRequest.parentId!!
                )
            }
        }

        return token

    }

    fun setRelationships(permissionRequest: SetRelationshipsPermissionRequest): String {
        var token: String = deleteRelationshipForResourceAndSubject(
            permissionRequest.resourceType.key,
            permissionRequest.resourceId,
            permissionRequest.subjectId,
        )

        permissionRequest.relationships.forEach { relationship ->
            // add relationship for this new resource to spicedb
            token = rebacRepository.writeRelationship(
                permissionRequest.resourceType.key,
                permissionRequest.resourceId,
                relationship,
                PermissionStructure.Subject.NAME,
                permissionRequest.subjectId,
            )
        }
        return token
    }

    fun deleteAllRelationshipsForResource(resourceType: Resource, resourceId: String): String {
        return rebacRepository.deleteAllRelationshipsForResource(resourceType, resourceId)
    }

    fun deleteRelationshipForResourceAndSubject(resourceType: Resource, resourceId: String, subjectId: String): String {
        return rebacRepository.deleteRelationshipsForResourceAndSubject(resourceType, resourceId, subjectId)
    }

}