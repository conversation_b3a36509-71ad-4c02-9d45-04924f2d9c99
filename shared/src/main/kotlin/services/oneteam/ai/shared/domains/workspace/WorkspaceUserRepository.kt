package services.oneteam.ai.shared.domains.workspace

import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.json.extract
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.domains.user.UserSchema
import services.oneteam.ai.shared.extensions.paginate
import services.oneteam.ai.shared.permissions.PermissionsService
import services.oneteam.ai.shared.permissions.ResourceType
import services.oneteam.ai.shared.permissions.SetRelationshipsPermissionRequest
import services.oneteam.ai.shared.withTenantTransactionScope

data class WorkspaceUserSearchCriteria(
    val workspaceId: Workspace.Id, val searchTerm: String?
) {
    companion object {
        // empty companion object so we can add extension functions
    }
}

class WorkspaceUserRepository(
    private val checks: Checks,
    val workspaceRepository: WorkspaceRepository,
    val userRepository: UserRepository,
    val permissionService: PermissionsService,
    val foundationRepository: FoundationRepository,
) {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "status" to WorkspaceUsersSchema.status, "userId" to WorkspaceUsersSchema.userId
            )
        )
    }

    /**
     * Give a user access to a workspace.
     */
    suspend fun create(tenant: Tenant, workspaceUser: WorkspaceUser.ForCreate): WorkspaceUserEntity {
        val workspace = workspaceRepository.findOne(workspaceUser.workspaceId)
        val user = userRepository.findOne(workspaceUser.userId.value)

        //check to see if this user is already in this workspace
        val existingEntity = WorkspaceUserEntity.findSingleByAndUpdate(
            //@formatter:off
            WorkspaceUsersSchema.workspaceId eq workspace.id.value
            and (WorkspaceUsersSchema.userId eq user.id)
            and (WorkspaceUsersSchema.status eq WorkspaceUser.Status.INACTIVE)
            //@formatter:on
        ) {
            //if so, then reactivate them (OA-2255)
            it.status = WorkspaceUser.Status.ACTIVE
            it.accessLevel = workspaceUser.accessLevel
        }

        existingEntity?.let { return it }

        val dao = WorkspaceUserEntity.new {
            workspaceId = WorkspaceEntity[workspace.id.value].id
            userId = user.id
            tenantId = tenant.id
            accessLevel = workspaceUser.accessLevel
            status = workspaceUser.status
        }

        setPermissionsForWorkspace(
            workspaceUser.workspaceId,
            workspaceUser.userId,
            workspaceUser.accessLevel,
            dao.tenantId
        )

        return dao
    }

    fun searchByCriteria(
        pageRequest: PageRequest, workspaceUserSearchCriteria: WorkspaceUserSearchCriteria
    ): Page<WorkspaceUserEntity> {
        return workspaceUserSearchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
            WorkspaceUserEntity.wrapRow(
                it
            )
        }
    }

    fun getById(id: Long): WorkspaceUserEntity? {
        return WorkspaceUserEntity.findById(id)
    }

    fun findOne(id: Long): WorkspaceUserEntity {
        return checks.exists(getById(id)) { "WorkspaceUser $id not found" }
    }

    suspend fun update(workspaceUser: WorkspaceUser.ForUpdate): WorkspaceUserEntity {
        val dao = WorkspaceUserEntity.findByIdAndUpdate(workspaceUser.id.value) {
            it.status = workspaceUser.status ?: it.status
            it.accessLevel = workspaceUser.accessLevel ?: it.accessLevel
        }
        setPermissionsForWorkspace(
            workspaceUser.workspaceId,
            workspaceUser.userId,
            workspaceUser.accessLevel,
            dao!!.tenantId
        )
        return dao
    }

    suspend fun setPermissionsForWorkspace(
        workspaceId: Workspace.Id,
        userId: User.Id,
        accessLevel: List<WorkspaceUser.AccessLevel>? = emptyList(),
        tenantId: Long
    ) {
        // Ensure the user has the necessary permissions for the workspace
        permissionService.setRelationships(
            SetRelationshipsPermissionRequest(
                ResourceType.WORKSPACE,
                workspaceId.value.toString(),
                userId.value.toString(),
                accessLevel?.map { it.relationship } ?: emptyList(),
                tenantId
            )
        )

        // for the root foundation
        val resourceId = withTenantTransactionScope { foundationRepository.getRoot(workspaceId.value) }.id.toString()
        permissionService.setRelationships(
            SetRelationshipsPermissionRequest(
                ResourceType.FOUNDATION,
                resourceId,
                userId.value.toString(),
                if (accessLevel?.isNotEmpty() == true && accessLevel.contains(WorkspaceUser.AccessLevel.COLLECTION)) listOf(
                    PermissionStructure.Foundation.Relationships.WRITE
                ) else emptyList(),
                tenantId
            )
        )
    }

    fun findByWorkspaceAndUserId(workspaceId: Workspace.Id, userId: User.Id): WorkspaceUserEntity {
        return WorkspaceUserEntity.find {
            (WorkspaceUsersSchema.workspaceId eq workspaceId.value) and (WorkspaceUsersSchema.userId eq userId.value)
        }.singleOrNull() ?: throw IllegalArgumentException("Workspace user not found")
    }


}

fun WorkspaceUserSearchCriteria.toQuery(): Query {
    val query = WorkspaceUsersSchema.join(UserSchema, JoinType.INNER) { WorkspaceUsersSchema.userId eq UserSchema.id }
        .selectAll().andWhere { WorkspaceUsersSchema.workspaceId eq workspaceId.value }

    if (searchTerm?.isNotBlank() == true) {
        val lowerSearchTerm = "%${searchTerm.lowercase()}%"
        query.andWhere {
            (UserSchema.email.lowerCase() like lowerSearchTerm) or (UserSchema.properties.extract<String>("firstName")
                .lowerCase() like lowerSearchTerm) or (UserSchema.properties.extract<String>("lastName")
                .lowerCase() like lowerSearchTerm)
        }
    }

    return query
}