package services.oneteam.ai.shared.domains.proxy

import io.ktor.http.*
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import services.oneteam.ai.shared.middlewares.RequestContext
import java.net.URLConnection
import kotlin.coroutines.coroutineContext

object ContentTypeSerializer : KSerializer<ContentType> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("ContentType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: ContentType) {
        encoder.encodeString(value.toString())
    }

    override fun deserialize(decoder: Decoder): ContentType {
        return ContentType.parse(decoder.decodeString())
    }
}

interface ProxyService {

    suspend fun call(
        body: ProxyEndpointBody, isExternalResponse: Boolean? = false, timeoutMillis: Long? = null
    ): ProxyEndpointResponse

    @Serializable
    data class FileInfo(
        val fileBytes: ByteArray,
        val fileName: String,
        val fileKey: String
    ) {
        val contentType: String = URLConnection.guessContentTypeFromName(fileName) ?: "application/octet-stream"
    }

    @Serializable
    sealed class ProxyRequestBody {
        @Serializable
        data class Json(val value: JsonElement) : ProxyRequestBody()
        @Serializable
        data class FormUrlEncoded(val params: Map<String, String>) : ProxyRequestBody()
        @Serializable
        data class Multipart(val fileInfos: List<FileInfo>) : ProxyRequestBody()
        // Add more types as needed
    }

    @Serializable
    data class ProxyEndpointBody(
        val url: String,
        val method: String,
        val body: ProxyRequestBody? = null,
        @Serializable(with = ContentTypeSerializer::class) val queryParams: Map<String, String>? = null,
        @Serializable(with = ContentTypeSerializer::class) val contentType: ContentType? = ContentType.Application.Json,
        val headers: Map<String, String>? = null,
        val authentication: Map<String, String>? = null,
        val options: Map<String, String>? = null,
    ) {
        override fun toString(): String {
            return Json.encodeToString(this)
        }
    }

    @Serializable
    data class ProxyEndpointResponse(
        val response: String?, val status: ProxyEndpointResponseStatus, val error: String? = null
    ) {
        override fun toString(): String {
            return Json.encodeToString(this)
        }
    }

    @Serializable
    enum class ProxyEndpointResponseStatus(val status: String) {
        SUCCESS("success"), FAIL("fail")
    }

    @Serializable
    data class ExternalEndpointResponse(
        val status: Int, val payload: JsonElement, val errors: JsonElement? = null
    )

    companion object {
        // Used for internal API calls (generally this is what we'd want to use)
        suspend fun buildInternalTenantUrl(endpoint: String = ""): String {
            val tenant = coroutineContext[RequestContext]!!.tenant
            return "${tenant.internalUrl}$endpoint"
        }

        // Used for internal API calls to the SyncServer (generally this is what we'd want to use)
        suspend fun buildInternalSyncTenantUrl(endpoint: String = ""): String {
            val tenant = coroutineContext[RequestContext]!!.tenant
            return "${tenant.internalSyncUrl}$endpoint"
        }

        // Quite rare we need this, but use this if the URL needs to be built from the tenant origin URL
        suspend fun buildTenantUrl(endpoint: String = ""): String {
            val tenant = coroutineContext[RequestContext]!!.tenant
            return "${tenant.originUrl}$endpoint"
        }
    }
}

fun ProxyService.ProxyEndpointBody.includeInternalServiceAccount(): ProxyService.ProxyEndpointBody {
    val authentication = authentication?.toMutableMap() ?: mutableMapOf()
    authentication["useOtaiServiceAccount"] = true.toString()
    return this.copy(authentication = authentication)
}

fun ProxyService.ProxyEndpointBody.includeCookies(cookies: String): ProxyService.ProxyEndpointBody {
    val headers = headers?.toMutableMap() ?: mutableMapOf()
    headers[HttpHeaders.Cookie] = cookies
    return this.copy(headers = headers)
}

fun ProxyService.ProxyEndpointBody.useCookiesOrFallOverToServiceAccount(cookies: String?): ProxyService.ProxyEndpointBody {
    if (cookies.isNullOrEmpty()) {
        return this.includeInternalServiceAccount()
    }
    return this.includeCookies(cookies)
}