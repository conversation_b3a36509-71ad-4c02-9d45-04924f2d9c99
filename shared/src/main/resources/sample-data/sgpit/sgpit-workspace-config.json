{"description": "SGPIT workspace", "errors": [], "flows": {"entities": {"2eWIo3r9W0GlPt0o3PIr0": {"description": "", "endingVariables": [], "id": "2eWIo3r9W0GlPt0o3PIr0", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-18T00:59:52.773Z", "updatedAt": "2025-07-16T04:05:47.549Z"}, "name": "webhook list schema", "start": "aZmBUJUEy3kUOoqtDoFHm", "startingVariables": [{"identifier": "incomingPayload", "properties": {"items": [{"identifier": "ah6g5L4BKCw3oO37FTJh6", "properties": {"items": [{"identifier": "form", "properties": {"items": [{"identifier": "id", "properties": {"required": false}, "type": "number"}]}, "type": "json"}, {"identifier": "number_1", "properties": {"required": false}, "type": "number"}, {"identifier": "text_1", "properties": {"required": false}, "type": "text"}]}, "type": "json"}], "required": true, "schema": {"identifier": "ah6g5L4BKCw3oO37FTJh6", "properties": {"items": [{"identifier": "ah6g5L4BKCw3oO37FTJh6", "properties": {"items": [{"identifier": "form", "properties": {"items": [{"identifier": "id", "properties": {"required": false}, "type": "number"}]}, "type": "json"}, {"identifier": "number_1", "properties": {"required": false}, "type": "number"}, {"identifier": "text_1", "properties": {"required": false}, "type": "text"}]}, "type": "json"}]}, "type": "list"}}, "type": "list"}], "steps": {"aLGuNL3XLIyT54CXfSg4I": {"id": "aLGuNL3XLIyT54CXfSg4I", "metadata": {"createdAt": "2025-06-26T07:55:35.467Z", "updatedAt": "2025-06-26T07:55:35.467Z"}, "name": "Set form answer(s)", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{incomingPayload}}", "columnId": "aDAq4RmV5s", "operation": "setCell", "questionId": "aDAq4RmV5s", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "1", "rowSelection": "rowIndex"}], "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "aZmBUJUEy3kUOoqtDoFHm": {"id": "aZmBUJUEy3kUOoqtDoFHm", "metadata": {"createdAt": "2025-06-25T01:47:01.411Z", "updatedAt": "2025-06-25T01:47:01.411Z"}, "name": "Set variable(s)", "next": "aLGuNL3XLIyT54CXfSg4I", "properties": {"variables": [{"identifier": "number_1", "polyType": "VARIABLE", "type": "text", "value": "{{incomingPayload[0].number_1}}"}, {"identifier": "boolean_1", "polyType": "VARIABLE", "type": "text", "value": "{{incomingPayload[0].boolean_1}}"}, {"identifier": "form", "polyType": "VARIABLE", "type": "json", "value": "{{incomingPayload[0].form}}"}]}, "variant": "setVariables"}}, "triggers": {"aGAcqzGY82qYCH64oYO7P": {"id": "aGAcqzGY82qYCH64oYO7P", "metadata": {"createdAt": "2025-06-18T00:59:55.922Z", "updatedAt": "2025-06-18T00:59:55.922Z"}, "name": "When this flow's webhook is received", "next": "", "properties": {"inputs": {"continueFlowIfApiFails": "false", "expectedPayload": {"sample": [{"form": {"id": 17}, "number_1": 123, "text_1": "qwe"}], "schema": {"id": "ah6g5L4BKCw3oO37FTJh6", "identifier": "ah6g5L4BKCw3oO37FTJh6", "properties": {"items": [{"id": "ah6g5L4BKCw3oO37FTJh6", "identifier": "ah6g5L4BKCw3oO37FTJh6", "properties": {"items": [{"id": "form", "identifier": "form", "properties": {"items": [{"id": "id", "identifier": "id", "properties": {"required": false}, "text": "id", "type": "number"}]}, "text": "form", "type": "json"}, {"id": "number_1", "identifier": "number_1", "properties": {"required": false}, "text": "number_1", "type": "number"}, {"id": "text_1", "identifier": "text_1", "properties": {"required": false}, "text": "text_1", "type": "text"}]}, "text": "JSON", "type": "json"}]}, "text": "List of JSON elements", "type": "list"}}, "payloadVariableName": "incomingPayload"}, "typePrimaryIdentifier": "receiveIncomingWebhookForFlow"}, "variant": "trigger"}}}, "7IbcClHBHX5lxmmaJVBeg": {"description": "", "endingVariables": [], "id": "7IbcClHBHX5lxmmaJVBeg", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-17T05:31:09.941Z", "updatedAt": "2025-07-16T04:05:23.131Z"}, "name": "Send API Request json schema", "start": "aBzsVffoFIkF0HjylHiGn", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.axaNyaPRfi9cn5mgA1rbK"}], "steps": {"aBzsVffoFIkF0HjylHiGn": {"id": "aBzsVffoFIkF0HjylHiGn", "metadata": {"createdAt": "2025-06-17T05:31:21.150Z", "updatedAt": "2025-06-17T05:31:21.150Z"}, "name": "Send an API Request", "next": "atlNxUfseXpH8SKxYd1w3", "properties": {"inputs": {"bodyParameters": "", "contentType": "", "continueFlowIfApiFails": "true", "headers": [{"key": "", "value": ""}], "httpMethod": "GET", "response": [{"expectedPayload": {"sample": {"author": "local", "branch": "main", "date": "now", "hash": "0"}, "schema": {"id": "a6ymUh1tRIpZWnT5ZGAYL", "identifier": "a6ymUh1tRIpZWnT5ZGAYL", "properties": {"items": [{"id": "branch", "identifier": "branch", "properties": {"required": false}, "text": "branch", "type": "text"}, {"id": "hash", "identifier": "hash", "properties": {"required": false}, "text": "hash", "type": "text"}, {"id": "date", "identifier": "date", "properties": {"required": false}, "text": "date", "type": "text"}, {"id": "author", "identifier": "author", "properties": {"required": false}, "text": "author", "type": "text"}]}, "text": "JSON", "type": "json"}}, "httpCode": "200"}, {"expectedPayload": {"sample": {"context": null, "debug": null, "errors": null, "key": null, "message": "Not Found /ai/api/build2", "statusCode": 404}, "schema": {"id": "aIjKrf3qukq0wJEKyhVxB", "identifier": "aIjKrf3qukq0wJEKyhVxB", "properties": {"items": [{"id": "context", "identifier": "context", "properties": {"required": false}, "text": "context", "type": "text"}, {"id": "debug", "identifier": "debug", "properties": {"required": false}, "text": "debug", "type": "text"}, {"id": "errors", "identifier": "errors", "properties": {"required": false}, "text": "errors", "type": "text"}, {"id": "key", "identifier": "key", "properties": {"required": false}, "text": "key", "type": "text"}, {"id": "message", "identifier": "message", "properties": {"required": false}, "text": "message", "type": "text"}, {"id": "statusCode", "identifier": "statusCode", "properties": {"required": false}, "text": "statusCode", "type": "number"}]}, "text": "JSON", "type": "json"}}, "httpCode": "404"}], "responseVariableName": "response_var", "url": "{{_workspace_.variables.CURRENT_HOST}}/ai/api/build"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}, "aLqwDdHsWoD1CImwTO2GW": {"id": "aLqwDdHsWoD1CImwTO2GW", "metadata": {"createdAt": "2025-06-27T01:03:34.028Z", "updatedAt": "2025-06-27T01:03:34.028Z"}, "name": "Set form answer(s)", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{response_var.payload}}", "columnId": "{{form.aC2mcecXgh.columns.aDAq4RmV5s.id}}", "operation": "setCell", "questionId": "aDAq4RmV5s", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "4", "rowSelection": "rowIndex"}], "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "atlNxUfseXpH8SKxYd1w3": {"id": "atlNxUfseXpH8SKxYd1w3", "metadata": {"createdAt": "2025-06-17T06:54:16.249Z", "updatedAt": "2025-06-17T06:54:16.249Z"}, "name": "Set variable(s)", "next": "aLqwDdHsWoD1CImwTO2GW", "properties": {"variables": [{"identifier": "version_var", "polyType": "VARIABLE", "type": "text", "value": "{{response_var.payload.version}}"}, {"identifier": "branch_var", "polyType": "VARIABLE", "type": "text", "value": "{{response_var.payload.branch}}"}]}, "variant": "setVariables"}}, "triggers": {"acwrMqm2lu4XFjXONCduA": {"id": "acwrMqm2lu4XFjXONCduA", "metadata": {"createdAt": "2025-06-17T05:31:12.312Z", "updatedAt": "2025-06-17T05:31:12.312Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Send API Request json schema", "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "E2Eamanualatriggerafromafoundationaflow": {"description": "E2Eamanualatriggerafromafoundationaflow", "id": "E2Eamanualatriggerafromafoundationaflow", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "E2Eamanualatriggerafromafoundationaflow", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Sample step just to keep the flow valid for now", "properties": {"variables": [{"identifier": "test", "polyType": "VARIABLE", "type": "boolean", "value": true}]}, "variant": "setVariables"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "Send Email", "foundationConfigurationId": "cli", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "RDE2zjfrLkqmDCfa3E4r4": {"description": "A simple flow gets an answer from a form and sets it in the context", "id": "RDE2zjfrLkqmDCfa3E4r4", "labels": [], "metadata": {"createdAt": "2025-01-01T22:55:45.267Z", "updatedAt": "2025-01-01T22:55:45.267Z"}, "name": "flow2", "start": "step1", "startingVariables": [{"identifier": "notused", "properties": {"required": false}, "type": "list"}], "steps": {"step1": {"id": "step1", "name": "Get answer from form in context - string", "next": "step2", "properties": {"variables": [{"identifier": "s1value", "polyType": "VARIABLE", "type": "text", "value": "{{form.plBrtlhc3W.answer}}"}]}, "variant": "setVariables"}, "step2": {"id": "step2", "name": "Get answer from form in context - number and date", "next": "step3", "properties": {"variables": [{"identifier": "s2value", "polyType": "VARIABLE", "type": "number", "value": "{{form.np9GiUMruw.answer}}"}, {"identifier": "mydate", "polyType": "VARIABLE", "type": "number", "value": "$YEAR({{form.r5QWKQYgKe.answer}})"}]}, "variant": "setVariables"}, "step3": {"id": "step3", "name": "Put table answers into variable - table", "next": "step4", "properties": {"variables": [{"identifier": "table", "polyType": "VARIABLE", "properties": {"operation": "setTable"}, "type": "table", "value": "{{form.VEOTahOeLj.answer}}"}]}, "variant": "setVariables"}, "step4": {"id": "step4", "name": "Get answer from form in context - table", "properties": {"variables": [{"identifier": "s4value", "polyType": "VARIABLE", "type": "text", "value": "{{form.VEOTahOeLj.answer[1].O1REtNdy50}}"}, {"identifier": "s5value", "polyType": "VARIABLE", "type": "text", "value": "{{table[1].O1REtNdy50}}"}, {"identifier": "tableColumnValues", "polyType": "VARIABLE", "type": "json", "value": "{{table.*.eALIKF2hFv}}"}, {"identifier": "tableColumnValueMultiplied", "polyType": "VARIABLE", "type": "number", "value": "{{table[1].eALIKF2hFv}} * 10"}]}, "variant": "setVariables"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Flow 2", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "WRXHnwzRNXW46Rk5UjHi5": {"description": "A simple flow that sets variables in the context", "id": "WRXHnwzRNXW46Rk5UjHi5", "labels": [], "metadata": {"createdAt": "2025-01-01T22:55:45.267Z", "updatedAt": "2025-01-01T22:55:45.267Z"}, "name": "flow 1", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Literal addition", "next": "step2", "properties": {"variables": [{"identifier": "s1value", "polyType": "VARIABLE", "type": "number", "value": "1 + 1"}, {"identifier": "s1valueTimes10", "polyType": "VARIABLE", "type": "number", "value": "{{s1value}} * 10"}]}, "variant": "setVariables"}, "step2": {"id": "step2", "name": "Variable multiplication", "next": "step3", "properties": {"variables": [{"identifier": "s2value", "polyType": "VARIABLE", "type": "number", "value": "{{s1value}} * 3"}]}, "variant": "setVariables"}, "step3": {"id": "step3", "name": "Variable summation", "next": "step4", "properties": {"variables": [{"identifier": "s3value", "polyType": "VARIABLE", "type": "number", "value": "$sum([{{s1value}}, {{s2value}}])"}, {"identifier": "hardcodedArray", "polyType": "VARIABLE", "type": "json", "value": "[1,2,3]"}, {"identifier": "hardcodedArraySize", "polyType": "VARIABLE", "type": "number", "value": "$count({{hardcodedArray}})"}, {"identifier": "hardcodedStringArray", "polyType": "VARIABLE", "type": "json", "value": "[\"a\",\"b\",\"c\"]"}, {"identifier": "calculatedStringArraySize", "polyType": "VARIABLE", "type": "number", "value": "$count({{hardcodedStringArray}})"}, {"identifier": "calculatedStringArraySizeMultiplied", "polyType": "VARIABLE", "type": "number", "value": "{{calculatedStringArraySize}} * 5"}]}, "variant": "setVariables"}, "step4": {"id": "step4", "name": "Literals", "next": "step5", "properties": {"variables": [{"identifier": "s4valueA", "polyType": "VARIABLE", "type": "text", "value": "'hello world'"}, {"identifier": "s4valueB", "polyType": "VARIABLE", "type": "number", "value": 5.5}]}, "variant": "setVariables"}, "step5": {"id": "step5", "name": "Set variable(s)", "properties": {"variables": [{"identifier": "TableColumnSum", "polyType": "VARIABLE", "type": "number", "value": "$sum({{formVariable.VEOTahOeLj.columns.eALIKF2hFv.answer}})"}]}, "variant": "setVariables"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Flow 1", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a0KFaRuWArpdysxEfZppO": {"description": "", "endingVariables": [], "id": "a0KFaRuWArpdysxEfZppO", "labels": [], "metadata": {"createdAt": "2025-08-08T07:18:44.907Z", "updatedAt": "2025-08-08T07:18:44.907Z"}, "name": "set table answers — set cell", "start": "aNJg9aHJ8YPNetktqsOx6", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aNJg9aHJ8YPNetktqsOx6": {"id": "aNJg9aHJ8YPNetktqsOx6", "metadata": {"createdAt": "2025-08-08T07:19:10.037Z", "updatedAt": "2025-08-08T07:19:10.037Z"}, "name": "Set table cell", "next": "", "properties": {"inputs": {"answers": [{"answer": "910", "columnId": "{{form.VEOTahOeLj.columns.eALIKF2hFv.id}}", "operation": "setCell", "questionId": "VEOTahOeLj", "questionTypeWithOperation": "table", "rowIndex": "1", "rowSelection": "rowIndex"}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}}, "triggers": {"a0MR7wiFPrtvQ6gt5S4zc": {"id": "a0MR7wiFPrtvQ6gt5S4zc", "metadata": {"createdAt": "2025-08-08T07:18:47.088Z", "updatedAt": "2025-08-08T07:18:47.089Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set table answers — set cell", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a0r1puSnYJY9k77SEIjgD": {"description": "", "endingVariables": [], "id": "a0r1puSnYJY9k77SEIjgD", "labels": [], "metadata": {"createdAt": "2025-08-08T07:18:44.907Z", "updatedAt": "2025-08-08T07:18:44.907Z"}, "name": "set table answers — whole table working", "start": "aR0quPTur9fJdX4CVR3Og", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"a0et89wVmK5mFB7AROoPe": {"id": "a0et89wVmK5mFB7AROoPe", "metadata": {"createdAt": "2025-08-08T07:42:22.001Z", "updatedAt": "2025-08-08T07:42:22.001Z"}, "name": "Set table column", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{table}}", "columnId": "", "operation": "setTable", "questionId": "VEOTahOeLj", "questionTypeWithOperation": "table", "rowIndex": "", "rowSelection": "", "valueRowIdShouldUpdate": "false"}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "aR0quPTur9fJdX4CVR3Og": {"id": "aR0quPTur9fJdX4CVR3Og", "metadata": {"createdAt": "2025-08-08T07:41:59.142Z", "updatedAt": "2025-08-08T07:41:59.142Z"}, "name": "Set variable(s)", "next": "a0et89wVmK5mFB7AROoPe", "properties": {"variables": [{"identifier": "table", "polyType": "VARIABLE", "properties": {"operation": "setTable"}, "type": "table", "value": "[{  {{form.VEOTahOeLj.columns.O1REtNdy50.id}} : \"textFlow_2\",   {{form.VEOTahOeLj.columns.eALIKF2hFv.id}} : 7689 }, {  {{form.VEOTahOeLj.columns.O1REtNdy50.id}} : \"textFlow_2222\",   {{form.VEOTahOeLj.columns.eALIKF2hFv.id}} : 7689222 }]"}]}, "variant": "setVariables"}}, "triggers": {"a0MR7wiFPrtvQ6gt5S4zc": {"id": "a0MR7wiFPrtvQ6gt5S4zc", "metadata": {"createdAt": "2025-08-08T07:18:47.088Z", "updatedAt": "2025-08-08T07:18:47.089Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set table answers — whole table working", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a1079aE2Eamanualatriggerafromaformaflow": {"description": "a1079aE2Eamanualatriggerafromaformaflow", "id": "a1079aE2Eamanualatriggerafromaformaflow", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "1079aE2Eamanualatriggerafromaformaflow", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Set form answer", "properties": {"inputs": {"answer": "0.3", "formId": "{{form.id}}", "questionId": "krM9xaFvah", "questionTypeWithOperation": "no", "rowId": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Run Flow", "formConfigurationId": "a1132aE2Eaform", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a1106aE2Eacreateaformawhenafoundationacreated": {"description": "a1106aE2Eacreateaformawhenafoundationacreated", "id": "a1106aE2Eacreateaformawhenafoundationacreated", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "1106-E2E-create-form-when-foundation-created", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Create a form", "properties": {"inputs": {"formConfigurationId": "a1132aE2Eaform", "formVariableName": "form", "foundationId": "{{foundation.id}}", "intervalId": ""}, "typePrimaryIdentifier": "createForm"}, "variant": "action"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "When a foundation is created", "next": "", "properties": {"inputs": {"foundationConfigurationId": "emp", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "foundationCreated"}, "variant": "trigger"}}}, "a1107aE2Easetavariableastep": {"description": "a1107aE2Easetavariableastep", "id": "a1107aE2Easetavariableastep", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "1107-E2E-set-variable-step", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Set variable(s)", "next": "step2", "properties": {"variables": [{"identifier": "TaxPayable", "polyType": "VARIABLE", "type": "text", "value": "MULTIPLY({{form.O42Cd6cSeZ.answer}}* {{form.krM9xaFvah.answer}})"}]}, "variant": "setVariables"}, "step2": {"id": "step2", "name": "Set form answer", "properties": {"inputs": {"answer": "{{TaxPayable}}", "formId": "{{form.id}}", "questionId": "{{form.acxx72aoNN.id}}", "questionTypeWithOperation": "no", "rowId": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "When answer(s) changed in a form", "next": "", "properties": {"inputs": {"changedQuestionVariableName": "answer", "formConfigurationId": "a1132aE2Eaform", "formVariableName": "form", "questionIds": "[O42Cd6cSeZ]"}, "typePrimaryIdentifier": "answerChanged"}, "variant": "trigger"}}}, "a1487aflowainaflowasetatableaanswerainaiterator": {"description": "", "id": "a1487aflowainaflowasetatableaanswerainaiterator", "labels": [], "metadata": {"createdAt": "2025-04-09T08:15:49.908Z", "updatedAt": "2025-04-09T08:15:49.908Z"}, "name": "1487 flow in flow set table answer in iterator", "start": "a4TrMGI19iRQKtIaW2pa7Y", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.a1872aformawithatable"}], "steps": {"a4TrMGI19iRQKtIaW2pa7Y": {"id": "a4TrMGI19iRQKtIaW2pa7Y", "metadata": {"createdAt": "2025-04-09T08:16:33.045Z", "updatedAt": "2025-04-09T08:16:33.045Z"}, "name": "For each", "next": "a5VAm6xu3LnGXU1gAFv7Dx", "properties": {"configuration": {"labels": [], "start": "a7F1V56yECRnBNl4wvkvit", "startingVariables": [{"identifier": "item_a4TrMGI19iRQKtIaW2pa7Y", "type": "json"}, {"identifier": "item_a4TrMGI19iRQKtIaW2pa7Y_index", "type": "number"}, {"identifier": "item_a4TrMGI19iRQKtIaW2pa7Y_output", "type": "unknown"}], "steps": {"Qr5MLh6vF9sZxh0vMnYeX": {"id": "Qr5MLh6vF9sZxh0vMnYeX", "metadata": {"createdAt": "2025-04-09T08:21:49.240Z", "updatedAt": "2025-04-09T08:21:49.240Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{answer}}", "columnId": "{{form.Io1TX1OJM2.columns.bihoQERn53.id}}", "formConfigurationId": "a1872aformawithatable", "formId": "{{form.id}}", "operation": "setCell", "questionId": "bihoQERn53", "questionTypeWithOperation": "table", "rowIndex": "{{item_a4TrMGI19iRQKtIaW2pa7Y_index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "a7F1V56yECRnBNl4wvkvit": {"id": "a7F1V56yECRnBNl4wvkvit", "metadata": {"createdAt": "2025-04-09T08:49:26.064Z", "updatedAt": "2025-04-09T08:49:26.064Z"}, "name": "Set variable(s)", "next": "Qr5MLh6vF9sZxh0vMnYeX", "properties": {"variables": [{"identifier": "answer", "polyType": "VARIABLE", "type": "text", "value": "$CONCAT(\"test_\", {{item_a4TrMGI19iRQKtIaW2pa7Y_index}})"}]}, "variant": "setVariables"}}}, "inputs": {"isReturn": "false", "itemVariableName": "item_a4TrMGI19iRQKtIaW2pa7Y", "list": "{{form.Io1TX1OJM2.answer}}", "resultVariableName": "output__step_a4TrMGI19iRQKtIaW2pa7Y", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}, "a5VAm6xu3LnGXU1gAFv7Dx": {"id": "a5VAm6xu3LnGXU1gAFv7Dx", "metadata": {"createdAt": "2025-04-09T08:54:14.787Z", "updatedAt": "2025-04-09T08:54:14.787Z"}, "name": "For each", "next": "", "properties": {"configuration": {"labels": [], "start": "s67OyozaIMLt2kvzqfWBS", "startingVariables": [{"identifier": "item_a5VAm6xu3LnGXU1gAFv7Dx", "type": "json"}, {"identifier": "item_a5VAm6xu3LnGXU1gAFv7Dx_index", "type": "number"}, {"identifier": "item_a5VAm6xu3LnGXU1gAFv7Dx_output", "type": "unknown"}], "steps": {"s67OyozaIMLt2kvzqfWBS": {"id": "s67OyozaIMLt2kvzqfWBS", "metadata": {"createdAt": "2025-04-09T08:54:22.553Z", "updatedAt": "2025-04-09T08:54:22.553Z"}, "name": "Flow", "next": "", "properties": {"inputs": {"flowConfigurationId": "a1487asetatableaanswer", "form": "{{form.id}}", "index": "{{item_a5VAm6xu3LnGXU1gAFv7Dx_index}}"}}, "variant": "flow"}}}, "inputs": {"isReturn": "false", "itemVariableName": "item_a5VAm6xu3LnGXU1gAFv7Dx", "list": "{{form.Io1TX1OJM2.answer}}", "resultVariableName": "output__step_a5VAm6xu3LnGXU1gAFv7Dx", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"a21hqYTVik4ILv0RanBtsm": {"id": "a21hqYTVik4ILv0RanBtsm", "metadata": {"createdAt": "2025-04-09T08:15:57.021Z", "updatedAt": "2025-04-09T08:15:57.021Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "1487 flow in flow set table answer in iterator", "formConfigurationId": "a1872aformawithatable", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a1487aflowsainaflowsa3alevelsasetavariable": {"description": "", "endingVariables": [], "id": "a1487aflowsainaflowsa3alevelsasetavariable", "labels": [], "metadata": {"createdAt": "2025-04-11T03:17:27.978Z", "updatedAt": "2025-04-11T03:17:27.978Z"}, "name": "1487 flows in flows 3 levels set variable", "start": "eDfbrGPIvhwa3dWH5V4a3", "startingVariables": [{"identifier": "foundationFlow3", "properties": {"required": true}, "type": "foundation.aQXFmGHm2w1LUvSVi9bjw"}], "steps": {"eDfbrGPIvhwa3dWH5V4a3": {"id": "eDfbrGPIvhwa3dWH5V4a3", "metadata": {"createdAt": "2025-04-15T04:03:24.474Z", "updatedAt": "2025-04-15T04:03:24.474Z"}, "name": "1487 flows in flows set variable", "next": "", "properties": {"inputs": {"flowConfigurationId": "a1487aflowsainaflowsasetavariable", "foundationFlow2": "{{foundationFlow3.id}}", "teesttt": "{{foundation.id}}"}}, "variant": "flow"}}, "triggers": {"hQTPormksEw40QFVFMUgc": {"id": "hQTPormksEw40QFVFMUgc", "metadata": {"createdAt": "2025-04-11T03:17:31.066Z", "updatedAt": "2025-04-11T03:17:31.066Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "1487 flows in flows 3 levels set variable", "foundationConfigurationId": "aQXFmGHm2w1LUvSVi9bjw", "foundationVariableName": "foundationFlow3"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "a1487aflowsainaflowsasetavariable": {"description": "", "endingVariables": [{"identifier": "set_variable_foundation_id", "type": "text"}], "id": "a1487aflowsainaflowsasetavariable", "labels": [], "metadata": {"createdAt": "2025-04-08T06:16:49.767Z", "updatedAt": "2025-04-08T06:16:49.767Z"}, "name": "1487 flows in flows set variable", "start": "htToBa07NH6F9auhEnEOq", "startingVariables": [{"identifier": "foundationFlow2", "properties": {"required": true}, "type": "foundation.aQXFmGHm2w1LUvSVi9bjw"}], "steps": {"htToBa07NH6F9auhEnEOq": {"id": "htToBa07NH6F9auhEnEOq", "metadata": {"createdAt": "2025-04-16T02:49:55.416Z", "updatedAt": "2025-04-16T02:49:55.416Z"}, "name": "1487 set variable helper", "next": "", "properties": {"inputs": {"flowConfigurationId": "a1487asetavariableahelper", "foundation": "{{foundationFlow2.id}}"}}, "variant": "flow"}}, "triggers": {"KrAaCspkB5UkUDgprMuNq": {"id": "KrAaCspkB5UkUDgprMuNq", "metadata": {"createdAt": "2025-04-08T06:16:53.287Z", "updatedAt": "2025-04-08T06:16:53.287Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "1487 flows in flows set variable", "foundationConfigurationId": "aQXFmGHm2w1LUvSVi9bjw", "foundationVariableName": "foundationFlow2"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "a1487asetatableaanswer": {"description": "", "id": "a1487asetatableaanswer", "labels": [], "metadata": {"createdAt": "2025-04-09T08:16:44.074Z", "updatedAt": "2025-04-09T08:16:44.074Z"}, "name": "1487 set table answer - not to be manually triggered", "start": "x3IkP6aAAM1Eym9FcNH98", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.a1872aformawithatable"}, {"identifier": "index", "properties": {"required": true}, "type": "number"}], "steps": {"k3bWqm0hayLH9gJYxTzVa": {"id": "k3bWqm0hayLH9gJYxTzVa", "metadata": {"createdAt": "2025-04-09T08:51:00.447Z", "updatedAt": "2025-04-09T08:51:00.447Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{answer}}", "columnId": "{{form.Io1TX1OJM2.columns.bihoQERn53.id}}", "formConfigurationId": "a1872aformawithatable", "formId": "{{form.id}}", "operation": "setCell", "questionId": "bihoQERn53", "questionTypeWithOperation": "table", "rowIndex": "{{index}}", "rowSelection": "rowIndex"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "x3IkP6aAAM1Eym9FcNH98": {"id": "x3IkP6aAAM1Eym9FcNH98", "metadata": {"createdAt": "2025-04-09T08:50:55.739Z", "updatedAt": "2025-04-09T08:50:55.739Z"}, "name": "Set variable(s)", "next": "k3bWqm0hayLH9gJYxTzVa", "properties": {"variables": [{"identifier": "answer", "polyType": "VARIABLE", "type": "text", "value": "$CONCAT(\"testFlowInFlow_\",{{index}})"}]}, "variant": "setVariables"}}, "triggers": {"a1LYoN7caZziV6qlFXKDx0": {"id": "a1LYoN7caZziV6qlFXKDx0", "metadata": {"createdAt": "2025-04-09T08:16:47.858Z", "updatedAt": "2025-04-09T08:16:47.858Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "1487 set table answer - not to be manually triggered", "formConfigurationId": "a1872aformawithatable", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a1487asetavariableahelper": {"description": "", "endingVariables": [{"identifier": "set_variable_foundation_id", "type": "text"}], "id": "a1487asetavariableahelper", "labels": [], "metadata": {"createdAt": "2025-04-08T06:17:28.930Z", "updatedAt": "2025-04-08T06:17:28.930Z"}, "name": "1487 set variable helper", "start": "WYCHF6GrBCVsDEbDpxWA9", "startingVariables": [{"identifier": "foundation", "properties": {"required": true}, "type": "foundation.aQXFmGHm2w1LUvSVi9bjw"}], "steps": {"WYCHF6GrBCVsDEbDpxWA9": {"id": "WYCHF6GrBCVsDEbDpxWA9", "metadata": {"createdAt": "2025-04-09T04:33:29.771Z", "updatedAt": "2025-04-09T04:33:29.771Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "set_variable_foundation_id", "polyType": "VARIABLE", "type": "text", "value": "{{foundation.id}}"}]}, "variant": "setVariables"}}, "triggers": {"a542UVmNduYBZmmZKScwaX": {"id": "a542UVmNduYBZmmZKScwaX", "metadata": {"createdAt": "2025-04-08T06:17:34.407Z", "updatedAt": "2025-04-08T06:17:34.407Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "1487 set variable helper", "foundationConfigurationId": "aQXFmGHm2w1LUvSVi9bjw", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "a1872afilteraformulaabasic": {"description": "", "endingVariables": [], "id": "a1872afilteraformulaabasic", "labels": [], "metadata": {"createdAt": "2025-04-30T02:07:09.164Z", "updatedAt": "2025-04-30T02:07:09.164Z"}, "name": "1872 filter formula basic", "start": "u6RLpBcmXVtBUfhwPVlD1", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.a1872aformawithatable"}], "steps": {"Zrh0PjhepRZ0Yrar5apu4": {"id": "Zrh0PjhepRZ0Yrar5apu4", "metadata": {"createdAt": "2025-05-02T06:44:58.653Z", "updatedAt": "2025-05-02T06:44:58.653Z"}, "name": "Set form answer", "next": "", "properties": {"inputs": {"answer": "{{variable_1_first_2_rows}}", "formConfigurationId": "a1872aformawithatable", "formId": "{{form.id}}", "operation": "setTable", "questionId": "Io1TX1OJM2", "questionTypeWithOperation": "table", "rowSelection": "", "valueRowIdShouldUpdate": "true"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}, "u6RLpBcmXVtBUfhwPVlD1": {"id": "u6RLpBcmXVtBUfhwPVlD1", "metadata": {"createdAt": "2025-04-30T02:07:21.334Z", "updatedAt": "2025-04-30T02:07:21.334Z"}, "name": "Set variable(s)", "next": "Zrh0PjhepRZ0Yrar5apu4", "properties": {"variables": [{"identifier": "variable_1_first_2_rows", "polyType": "VARIABLE", "properties": {"operation": "setTable"}, "type": "table", "value": "$FILTER({{form.Io1TX1OJM2.answer}}, [true,true,false])"}, {"identifier": "variable_2", "polyType": "VARIABLE", "properties": {"operation": "setTable"}, "type": "table", "value": "$FILTER({{form.Io1TX1OJM2.answer}}, $LISTALL(true,true,false))"}, {"identifier": "variable_3", "polyType": "VARIABLE", "properties": {"operation": "setTable"}, "type": "table", "value": "$FILTER({{form.Io1TX1OJM2.answer}}, $map([10,20,30], function($v, $i){$v>15}))"}, {"identifier": "variable_4", "polyType": "VARIABLE", "properties": {"operation": "setTable"}, "type": "table", "value": "$FILTER({{form.Io1TX1OJM2.answer}}, $map({{form.Io1TX1OJM2.columns.Psnak4wzY6.answer}}, function($v, $i){$v<15}))"}, {"identifier": "variable_5", "polyType": "VARIABLE", "properties": {"listOperation": "setList"}, "type": "list", "value": "$FILTER([\"1\",\"2\",\"3\"], [true,true,false])"}]}, "variant": "setVariables"}}, "triggers": {"Ju685WFbayW8uCvcw2yXY": {"id": "Ju685WFbayW8uCvcw2yXY", "metadata": {"createdAt": "2025-04-30T02:07:13.451Z", "updatedAt": "2025-04-30T02:07:13.451Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "1872 filter formula basic", "formConfigurationId": "a1872aformawithatable", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a1r8Uwe87ex93ZQIp45sm": {"description": "", "endingVariables": [], "id": "a1r8Uwe87ex93ZQIp45sm", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-27T01:52:18.959Z", "updatedAt": "2025-07-16T04:06:01.481Z"}, "name": "Check Test Results", "start": "a6osbJA2fSlaPEVwmmO3C", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.axaNyaPRfi9cn5mgA1rbK"}], "steps": {"a6osbJA2fSlaPEVwmmO3C": {"id": "a6osbJA2fSlaPEVwmmO3C", "metadata": {"createdAt": "2025-08-19T04:02:53.291Z", "updatedAt": "2025-08-19T04:02:53.292Z"}, "name": "Set variable(s)", "next": "af1INRJkfnGOS5YPa9jUA", "properties": {"variables": [{"identifier": "actualAnswersWithEmptyFilled", "polyType": "VARIABLE", "properties": {"listOperation": "setList"}, "type": "list", "value": "$map({{form.aC2mcecXgh.columns.aDAq4RmV5s.answer}}, function($v) { $v = null or $v = \"\" ? \"NA\" : $v })"}]}, "variant": "setVariables"}, "aQpYAZfBfuwq4lM308Osw": {"id": "aQpYAZfBfuwq4lM308Osw", "metadata": {"createdAt": "2025-06-27T03:39:24.190Z", "updatedAt": "2025-06-27T03:39:24.190Z"}, "name": "Set form answer(s)", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{results}}", "columnId": "{{form.aC2mcecXgh.columns.a1RySJBz6K.id}}", "operation": "setColumn", "questionId": "a1RySJBz6K", "questionTypeWithOperation": "table", "rowSelection": ""}], "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "aRdK7QdpMmTMGDrHkj8qc": {"id": "aRdK7QdpMmTMGDrHkj8qc", "metadata": {"createdAt": "2025-06-27T06:26:14.156Z", "updatedAt": "2025-06-27T06:26:14.156Z"}, "name": "Set form answer(s)", "next": "apaaUInhngYrDBQBBj7nn", "properties": {"inputs": {"answers": [{"answer": "{{emptyArray}}", "columnId": "{{form.aC2mcecXgh.columns.a1RySJBz6K.id}}", "operation": "setColumn", "questionId": "a1RySJBz6K", "questionTypeWithOperation": "table", "rowSelection": ""}], "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "af1INRJkfnGOS5YPa9jUA": {"id": "af1INRJkfnGOS5YPa9jUA", "metadata": {"createdAt": "2025-06-27T06:33:57.338Z", "updatedAt": "2025-06-27T06:33:57.338Z"}, "name": "Set variable(s)", "next": "aRdK7QdpMmTMGDrHkj8qc", "properties": {"variables": [{"identifier": "emptyArray", "polyType": "VARIABLE", "properties": {"listOperation": "setList"}, "type": "list", "value": "[]"}]}, "variant": "setVariables"}, "apaaUInhngYrDBQBBj7nn": {"id": "apaaUInhngYrDBQBBj7nn", "metadata": {"createdAt": "2025-06-27T03:37:50.884Z", "updatedAt": "2025-06-27T03:37:50.884Z"}, "name": "Set variable(s)", "next": "aQpYAZfBfuwq4lM308Osw", "properties": {"variables": [{"identifier": "results", "polyType": "VARIABLE", "properties": {"listOperation": "setList"}, "type": "list", "value": "$map({{actualAnswersWithEmptyFilled}}, function($v, $i) {\n    $IF($OR($type($v) = \"null\", $type($v) = undefined), \"No Result\", $IF($eval($v)=$eval({{form.aC2mcecXgh.columns.aashs2eBrn.answer}}[$i]), \"PASS\", \"FAIL\"))\n})"}]}, "variant": "setVariables"}}, "triggers": {"aEW3JiosUYr2P98LyTdjM": {"id": "aEW3JiosUYr2P98LyTdjM", "metadata": {"createdAt": "2025-06-27T01:55:47.224Z", "updatedAt": "2025-06-27T01:55:47.224Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Check Test Results", "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a35KCpseEhiQyUytboCi1": {"description": "", "endingVariables": [], "id": "a35KCpseEhiQyUytboCi1", "labels": [], "metadata": {"createdAt": "2025-06-02T06:12:42.015Z", "updatedAt": "2025-06-02T06:12:42.015Z"}, "name": "Pet Flow", "start": "abLb3gEsIDRKYo3rZ1aKC", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.a0ZjxHUe06Mm0VZSwKfSE"}], "steps": {"abLb3gEsIDRKYo3rZ1aKC": {"id": "abLb3gEsIDRKYo3rZ1aKC", "metadata": {"createdAt": "2025-06-02T06:15:32.686Z", "updatedAt": "2025-06-02T06:15:32.686Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "variable_1", "polyType": "VARIABLE", "type": "text", "value": "{{form.id}}"}]}, "variant": "setVariables"}}, "triggers": {"ahEsErNnearKirRua62H5": {"id": "ahEsErNnearKirRua62H5", "metadata": {"createdAt": "2025-06-02T06:12:51.175Z", "updatedAt": "2025-06-02T06:12:51.175Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Pet Flow", "formConfigurationId": "a0ZjxHUe06Mm0VZSwKfSE", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a57wFN4WFBLAcFdfZ0ZHE": {"description": "", "endingVariables": [], "id": "a57wFN4WFBLAcFdfZ0ZHE", "labels": [], "metadata": {"createdAt": "2025-06-02T22:55:10.855Z", "updatedAt": "2025-06-02T22:55:10.855Z"}, "name": "Send Api Request", "start": "aj7ZKisgxGRhWshZIt3VK", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aj7ZKisgxGRhWshZIt3VK": {"id": "aj7ZKisgxGRhWshZIt3VK", "metadata": {"createdAt": "2025-06-02T22:55:44.148Z", "updatedAt": "2025-06-02T22:55:44.148Z"}, "name": "Send an API Request", "next": "", "properties": {"inputs": {"bodyParameters": "", "contentType": "", "continueFlowIfApiFails": "false", "headers": [{"key": "VAL1", "value": "A"}, {"key": "VAL2", "value": "B"}, {"key": "VAL3", "value": "{{form.id}}"}], "httpMethod": "GET", "response": [{"httpCode": "200"}], "responseVariableName": "response__step_aj7ZKisgxGRhWshZIt3VK", "url": "http://localhost:8000/ai/api/build"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}}, "triggers": {"acxbiPJUpwV4f5tVN8Vfs": {"id": "acxbiPJUpwV4f5tVN8Vfs", "metadata": {"createdAt": "2025-06-02T22:55:18.122Z", "updatedAt": "2025-06-02T22:55:18.122Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Send Api Request", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a9EUf1iYjsxVIVYxJXZdf": {"description": "", "endingVariables": [], "id": "a9EUf1iYjsxVIVYxJXZdf", "labels": [], "metadata": {"createdAt": "2025-08-08T07:18:44.907Z", "updatedAt": "2025-08-08T07:18:44.907Z"}, "name": "set table answers — set column", "start": "aR0quPTur9fJdX4CVR3Og", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"a0et89wVmK5mFB7AROoPe": {"id": "a0et89wVmK5mFB7AROoPe", "metadata": {"createdAt": "2025-08-08T07:42:22.001Z", "updatedAt": "2025-08-08T07:42:22.001Z"}, "name": "Set table column", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{table_col}}", "columnId": "{{form.VEOTahOeLj.columns.O1REtNdy50.id}}", "operation": "setColumn", "questionId": "VEOTahOeLj", "questionTypeWithOperation": "table", "rowIndex": "", "rowSelection": ""}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "aR0quPTur9fJdX4CVR3Og": {"id": "aR0quPTur9fJdX4CVR3Og", "metadata": {"createdAt": "2025-08-08T07:41:59.142Z", "updatedAt": "2025-08-08T07:41:59.142Z"}, "name": "Set variable(s)", "next": "a0et89wVmK5mFB7AROoPe", "properties": {"variables": [{"identifier": "table_col", "polyType": "VARIABLE", "properties": {"listOperation": "setList"}, "type": "list", "value": "[\"flow1\",\"flow2\"]"}]}, "variant": "setVariables"}}, "triggers": {"a0MR7wiFPrtvQ6gt5S4zc": {"id": "a0MR7wiFPrtvQ6gt5S4zc", "metadata": {"createdAt": "2025-08-08T07:18:47.088Z", "updatedAt": "2025-08-08T07:18:47.089Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set table answers — set column", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "a9lAj1wtT4zk8AfSg918f": {"description": "", "endingVariables": [], "id": "a9lAj1wtT4zk8AfSg918f", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-17T05:31:09.941Z", "updatedAt": "2025-07-16T04:06:19.265Z"}, "name": "Send API Request no schema", "start": "aBzsVffoFIkF0HjylHiGn", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.axaNyaPRfi9cn5mgA1rbK"}], "steps": {"aBzsVffoFIkF0HjylHiGn": {"id": "aBzsVffoFIkF0HjylHiGn", "metadata": {"createdAt": "2025-06-17T05:31:21.150Z", "updatedAt": "2025-06-17T05:31:21.150Z"}, "name": "Send an API Request", "next": "atlNxUfseXpH8SKxYd1w3", "properties": {"inputs": {"continueFlowIfApiFails": "true", "headers": [{"key": "", "value": ""}], "httpMethod": "GET", "response": [{"expectedPayload": {"sample": {}, "schema": {}}, "httpCode": "200"}, {"expectedPayload": {"sample": {}, "schema": {}}, "httpCode": "404"}], "responseVariableName": "response_var", "url": "{{_workspace_.variables.CURRENT_HOST}}/ai/api/build"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}, "aLqwDdHsWoD1CImwTO2GW": {"id": "aLqwDdHsWoD1CImwTO2GW", "metadata": {"createdAt": "2025-06-27T01:03:34.028Z", "updatedAt": "2025-06-27T01:03:34.028Z"}, "name": "Set form answer(s)", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{response_var.payload}}", "columnId": "{{form.aC2mcecXgh.columns.aDAq4RmV5s.id}}", "operation": "setCell", "questionId": "aDAq4RmV5s", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "6", "rowSelection": "rowIndex"}], "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "atlNxUfseXpH8SKxYd1w3": {"id": "atlNxUfseXpH8SKxYd1w3", "metadata": {"createdAt": "2025-06-17T06:54:16.249Z", "updatedAt": "2025-06-17T06:54:16.249Z"}, "name": "Set variable(s)", "next": "aLqwDdHsWoD1CImwTO2GW", "properties": {"variables": [{"identifier": "version_var", "polyType": "VARIABLE", "type": "text", "value": "{{response_var.payload.version}}"}, {"identifier": "branch_var", "polyType": "VARIABLE", "type": "text", "value": "{{response_var.payload.branch}}"}]}, "variant": "setVariables"}}, "triggers": {"acwrMqm2lu4XFjXONCduA": {"id": "acwrMqm2lu4XFjXONCduA", "metadata": {"createdAt": "2025-06-17T05:31:12.312Z", "updatedAt": "2025-06-17T05:31:12.312Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Send API Request no schema", "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aDSiGV1CGoovzfRYm7lw0": {"description": "REQUIREMENT: API Key to be set in workspace variables under the name of INTERNAL_API_KEY\nInstructions: Run this flow and observe OTAI-BE logs for received payload. See relevant endpoint in code", "endingVariables": [], "id": "aDSiGV1CGoovzfRYm7lw0", "labels": ["aqH1fHtaj65vgqtNIRnb8"], "metadata": {"createdAt": "2025-09-01T07:32:55.416Z", "updatedAt": "2025-09-08T07:26:57.729Z"}, "name": "test send api content type json data", "start": "aH4HK68XCeYnG39c4uJZu", "startingVariables": [{"identifier": "foundation", "properties": {"required": true}, "type": "foundation.aQXFmGHm2w1LUvSVi9bjw"}], "steps": {"aH4HK68XCeYnG39c4uJZu": {"id": "aH4HK68XCeYnG39c4uJZu", "metadata": {"createdAt": "2025-09-01T07:34:32.469Z", "updatedAt": "2025-09-01T07:34:32.469Z"}, "name": "Set variable(s)", "next": "apeEe4xpqUl9gXGVPHigU", "properties": {"variables": [{"identifier": "body", "polyType": "VARIABLE", "type": "json", "value": "{\n    \"test\": \"456\"\n}"}, {"identifier": "api_key", "polyType": "VARIABLE", "type": "text", "value": "\"Bearer \" & {{_workspace_.variables.INTERNAL_API_KEY}}"}]}, "variant": "setVariables"}, "apeEe4xpqUl9gXGVPHigU": {"id": "apeEe4xpqUl9gXGVPHigU", "metadata": {"createdAt": "2025-09-01T07:33:08.270Z", "updatedAt": "2025-09-01T07:33:08.270Z"}, "name": "Send an API Request", "next": "", "properties": {"inputs": {"bodyParameters": "{{body}}", "contentType": "application/json", "continueFlowIfApiFails": "false", "headers": [{"key": "Authorization", "value": "{{api_key}}"}], "httpMethod": "POST", "response": [{"httpCode": "200"}], "responseVariableName": "response", "url": "{{_workspace_.variables.CURRENT_HOST}}/ai/api/{{_workspace_.variables.test_content_types_url_base}}/json"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}}, "triggers": {"aKUAt5VT3Ujdltf5WWFZE": {"id": "aKUAt5VT3Ujdltf5WWFZE", "metadata": {"createdAt": "2025-09-01T07:33:00.654Z", "updatedAt": "2025-09-01T07:33:00.654Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "test send api content type json data", "foundationConfigurationId": "aQXFmGHm2w1LUvSVi9bjw", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "aDkYOiGywKRq1o2djZQ3S": {"description": "", "endingVariables": [], "id": "aDkYOiGywKRq1o2djZQ3S", "labels": [], "metadata": {"createdAt": "2025-08-15T07:11:58.915Z", "updatedAt": "2025-08-15T07:11:58.915Z"}, "name": "set list answer — remove item", "start": "a4odGSWKNwwT3tPh2YVNM", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"a4odGSWKNwwT3tPh2YVNM": {"id": "a4odGSWKNwwT3tPh2YVNM", "metadata": {"createdAt": "2025-08-15T07:12:11.906Z", "updatedAt": "2025-08-15T07:12:11.906Z"}, "name": "set list", "next": "", "properties": {"inputs": {"answers": [{"answer": "", "itemIndex": "2", "itemSelection": "itemIndex", "listOperation": "removeItem", "questionId": "apZe1DTF8B", "questionTypeWithOperation": "list"}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}}, "triggers": {"aPOZZPyj1JUOQYYyNf1Yq": {"id": "aPOZZPyj1JUOQYYyNf1Yq", "metadata": {"createdAt": "2025-08-15T07:12:02.243Z", "updatedAt": "2025-08-15T07:12:02.243Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set list answer — remove item", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aFL3kpI953xWTdBO9GWg9": {"description": "", "endingVariables": [], "id": "aFL3kpI953xWTdBO9GWg9", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-26T07:41:55.033Z", "updatedAt": "2025-07-16T04:05:52.164Z"}, "name": "Trigger Webhook list schema", "start": "a3QAdg0THt65GfjsJjvsw", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.axaNyaPRfi9cn5mgA1rbK"}], "steps": {"a3QAdg0THt65GfjsJjvsw": {"id": "a3QAdg0THt65GfjsJjvsw", "metadata": {"createdAt": "2025-06-26T08:16:59.854Z", "updatedAt": "2025-06-26T08:16:59.854Z"}, "name": "Set variable(s)", "next": "aW0CYcnF9RxyhXHR5DEZs", "properties": {"variables": [{"identifier": "jwt", "polyType": "VARIABLE", "type": "text", "value": "\"Bearer \" & {{_workspace_.variables.INTERNAL_API_KEY}}"}]}, "variant": "setVariables"}, "a9ijpcrz43tJrz9xRQkYc": {"id": "a9ijpcrz43tJrz9xRQkYc", "metadata": {"createdAt": "2025-06-26T07:50:49.474Z", "updatedAt": "2025-06-26T07:50:49.474Z"}, "name": "Send an API Request", "next": "", "properties": {"inputs": {"bodyParameters": "{{req_payload}}", "continueFlowIfApiFails": "false", "headers": [{"key": "Authorization", "value": "{{jwt}}"}, {"key": "Content-Type", "value": "application/json"}], "httpMethod": "POST", "response": [{"httpCode": "200"}], "responseVariableName": "response", "url": "{{_workspace_.variables.CURRENT_HOST}}/ai/api/webhooks/workspaces/{{_workspace_.id}}"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}, "aW0CYcnF9RxyhXHR5DEZs": {"id": "aW0CYcnF9RxyhXHR5DEZs", "metadata": {"createdAt": "2025-06-27T01:34:54.886Z", "updatedAt": "2025-06-27T01:34:54.886Z"}, "name": "Set variable(s)", "next": "a9ijpcrz43tJrz9xRQkYc", "properties": {"variables": [{"identifier": "req_payload", "polyType": "VARIABLE", "properties": {"listOperation": "setList"}, "type": "list", "value": "[{\n  \"number_1\": 123,\n  \"text_1\": \"qwe\",\n  \"boolean_1\": true,\n  \"form\": {\n    \"id\": {{form.id}}\n  }\n}]"}]}, "variant": "setVariables"}}, "triggers": {"aTzoI8KByiqCeiUhjPrti": {"id": "aTzoI8KByiqCeiUhjPrti", "metadata": {"createdAt": "2025-06-26T07:50:36.496Z", "updatedAt": "2025-06-26T07:50:36.496Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Trigger Webhook list schema", "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aFgSMhUjZabwII3l01NZ6": {"description": "", "endingVariables": [], "id": "aFgSMhUjZabwII3l01NZ6", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-17T05:31:09.941Z", "updatedAt": "2025-07-16T04:05:28.863Z"}, "name": "Send API Request list schema", "start": "aBzsVffoFIkF0HjylHiGn", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.axaNyaPRfi9cn5mgA1rbK"}], "steps": {"aBzsVffoFIkF0HjylHiGn": {"id": "aBzsVffoFIkF0HjylHiGn", "metadata": {"createdAt": "2025-06-17T05:31:21.150Z", "updatedAt": "2025-06-17T05:31:21.150Z"}, "name": "Send an API Request", "next": "atlNxUfseXpH8SKxYd1w3", "properties": {"inputs": {"continueFlowIfApiFails": "true", "headers": [{"key": "", "value": ""}], "httpMethod": "GET", "response": [{"expectedPayload": {"sample": [{"number_1": 123, "text_1": "qwe"}], "schema": {"id": "axaUP5dRCgfOkuizhJPV6", "identifier": "axaUP5dRCgfOkuizhJPV6", "properties": {"items": [{"id": "axaUP5dRCgfOkuizhJPV6", "identifier": "axaUP5dRCgfOkuizhJPV6", "properties": {"items": [{"id": "number_1", "identifier": "number_1", "properties": {"required": false}, "text": "number_1", "type": "number"}, {"id": "text_1", "identifier": "text_1", "properties": {"required": false}, "text": "text_1", "type": "text"}]}, "text": "JSON", "type": "json"}]}, "text": "List of JSON elements", "type": "list"}}, "httpCode": "200"}, {"expectedPayload": {"sample": {"context": null, "debug": null, "errors": null, "key": null, "message": "Not Found /ai/api/build2", "statusCode": 404}, "schema": {"id": "aIjKrf3qukq0wJEKyhVxB", "identifier": "aIjKrf3qukq0wJEKyhVxB", "properties": {"items": [{"id": "context", "identifier": "context", "properties": {"required": false}, "text": "context", "type": "text"}, {"id": "debug", "identifier": "debug", "properties": {"required": false}, "text": "debug", "type": "text"}, {"id": "errors", "identifier": "errors", "properties": {"required": false}, "text": "errors", "type": "text"}, {"id": "key", "identifier": "key", "properties": {"required": false}, "text": "key", "type": "text"}, {"id": "message", "identifier": "message", "properties": {"required": false}, "text": "message", "type": "text"}, {"id": "statusCode", "identifier": "statusCode", "properties": {"required": false}, "text": "statusCode", "type": "number"}]}, "text": "JSON", "type": "json"}}, "httpCode": "404"}], "responseVariableName": "response_var", "url": "{{_workspace_.variables.CURRENT_HOST}}/ai/api/build2"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}, "aUrFJmyN5FI9O6RlVC4Xj": {"id": "aUrFJmyN5FI9O6RlVC4Xj", "metadata": {"createdAt": "2025-06-27T01:21:28.698Z", "updatedAt": "2025-06-27T01:21:28.698Z"}, "name": "Set form answer(s)", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{response_var.payload}}", "columnId": "{{form.aC2mcecXgh.columns.aDAq4RmV5s.id}}", "operation": "setCell", "questionId": "aDAq4RmV5s", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "3", "rowSelection": "rowIndex"}], "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "atlNxUfseXpH8SKxYd1w3": {"id": "atlNxUfseXpH8SKxYd1w3", "metadata": {"createdAt": "2025-06-17T06:54:16.249Z", "updatedAt": "2025-06-17T06:54:16.249Z"}, "name": "Set variable(s)", "next": "aUrFJmyN5FI9O6RlVC4Xj", "properties": {"variables": [{"identifier": "number_var", "polyType": "VARIABLE", "type": "number", "value": "{{response_var.payload.number_1}}"}, {"identifier": "bool_var", "polyType": "VARIABLE", "type": "text", "value": "{{response_var.payload.boolean_1}}"}]}, "variant": "setVariables"}}, "triggers": {"acwrMqm2lu4XFjXONCduA": {"id": "acwrMqm2lu4XFjXONCduA", "metadata": {"createdAt": "2025-06-17T05:31:12.312Z", "updatedAt": "2025-06-17T05:31:12.312Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Send API Request list schema", "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aLztDqfJA2Y3XETTQyp6h": {"description": "", "endingVariables": [], "id": "aLztDqfJA2Y3XETTQyp6h", "labels": [], "metadata": {"createdAt": "2025-08-08T07:18:44.907Z", "updatedAt": "2025-08-08T07:18:44.907Z"}, "name": "set table answers — set rows", "start": "asz8V06qfM2jCrPlXK0M5", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aNJg9aHJ8YPNetktqsOx6": {"id": "aNJg9aHJ8YPNetktqsOx6", "metadata": {"createdAt": "2025-08-08T07:19:10.037Z", "updatedAt": "2025-08-08T07:19:10.037Z"}, "name": "Set table row 1 and 2", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{table_row}}", "operation": "setRow", "questionId": "VEOTahOeLj", "questionTypeWithOperation": "table", "rowIndex": "1", "rowSelection": "rowIndex"}, {"answer": "{{table_row}}", "operation": "setRow", "questionId": "VEOTahOeLj", "questionTypeWithOperation": "table", "rowIndex": "2", "rowSelection": "rowIndex"}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "asz8V06qfM2jCrPlXK0M5": {"id": "asz8V06qfM2jCrPlXK0M5", "metadata": {"createdAt": "2025-08-08T07:24:34.046Z", "updatedAt": "2025-08-08T07:24:34.046Z"}, "name": "Set variable(s)", "next": "aNJg9aHJ8YPNetktqsOx6", "properties": {"variables": [{"identifier": "table_row", "polyType": "VARIABLE", "type": "json", "value": "{  {{form.VEOTahOeLj.columns.O1REtNdy50.id}} : \"textFlow_1\",   {{form.VEOTahOeLj.columns.eALIKF2hFv.id}} : 345435 }"}]}, "variant": "setVariables"}}, "triggers": {"a0MR7wiFPrtvQ6gt5S4zc": {"id": "a0MR7wiFPrtvQ6gt5S4zc", "metadata": {"createdAt": "2025-08-08T07:18:47.088Z", "updatedAt": "2025-08-08T07:18:47.089Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set table answers — set rows", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aN1o7Ch9U9ldfqdr0xXGb": {"description": "REQUIREMENT: API Key to be set in workspace variables under the name of INTERNAL_API_KEY\nInstructions: Run this flow and observe OTAI-BE logs for received payload. See relevant endpoint in code", "endingVariables": [], "id": "aN1o7Ch9U9ldfqdr0xXGb", "labels": ["aqH1fHtaj65vgqtNIRnb8"], "metadata": {"createdAt": "2025-09-01T07:32:55.416Z", "updatedAt": "2025-09-08T07:26:51.660Z"}, "name": "test send api content type form urlencoded", "start": "aH4HK68XCeYnG39c4uJZu", "startingVariables": [{"identifier": "foundation", "properties": {"required": true}, "type": "foundation.aQXFmGHm2w1LUvSVi9bjw"}], "steps": {"aH4HK68XCeYnG39c4uJZu": {"id": "aH4HK68XCeYnG39c4uJZu", "metadata": {"createdAt": "2025-09-01T07:34:32.469Z", "updatedAt": "2025-09-01T07:34:32.469Z"}, "name": "Set variable(s)", "next": "apeEe4xpqUl9gXGVPHigU", "properties": {"variables": [{"identifier": "body", "polyType": "VARIABLE", "type": "json", "value": "{\n    \"test\": 123,\n    \"abc\": \"def\"\n}"}, {"identifier": "api_key", "polyType": "VARIABLE", "type": "text", "value": "\"Bearer \" & {{_workspace_.variables.INTERNAL_API_KEY}}"}]}, "variant": "setVariables"}, "apeEe4xpqUl9gXGVPHigU": {"id": "apeEe4xpqUl9gXGVPHigU", "metadata": {"createdAt": "2025-09-01T07:33:08.270Z", "updatedAt": "2025-09-01T07:33:08.270Z"}, "name": "Send an API Request", "next": "", "properties": {"inputs": {"bodyParameters": "{{body}}", "contentType": "application/x-www-form-urlencoded", "continueFlowIfApiFails": "false", "headers": [{"key": "Authorization", "value": "{{api_key}}"}], "httpMethod": "POST", "response": [{"httpCode": "200"}], "responseVariableName": "response", "url": "{{_workspace_.variables.CURRENT_HOST}}/ai/api/{{_workspace_.variables.test_content_types_url_base}}/form-urlencoded"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}}, "triggers": {"aKUAt5VT3Ujdltf5WWFZE": {"id": "aKUAt5VT3Ujdltf5WWFZE", "metadata": {"createdAt": "2025-09-01T07:33:00.654Z", "updatedAt": "2025-09-01T07:33:00.654Z"}, "name": "Manually trigger from a foundation", "next": "", "properties": {"inputs": {"buttonLabel": "test send api content type form urlencoded", "foundationConfigurationId": "aQXFmGHm2w1LUvSVi9bjw", "foundationVariableName": "foundation"}, "typePrimaryIdentifier": "manualTriggerFromFoundation"}, "variant": "trigger"}}}, "aNcNypc8Cf5vwky2ei4DE": {"description": "", "endingVariables": [], "id": "aNcNypc8Cf5vwky2ei4DE", "labels": [], "metadata": {"createdAt": "2025-08-08T07:18:44.907Z", "updatedAt": "2025-08-08T07:18:44.907Z"}, "name": "set file answers — add file", "start": "aHa18wm5V8yZOHr16aSyn", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aHa18wm5V8yZOHr16aSyn": {"id": "aHa18wm5V8yZOHr16aSyn", "metadata": {"createdAt": "2025-08-18T03:10:47.028Z", "updatedAt": "2025-08-18T03:10:47.028Z"}, "name": "Download a file", "next": "aNJg9aHJ8YPNetktqsOx6", "properties": {"inputs": {"fileUrl": "https://fujiframe.com/assets/images/_3000x2000_fit_center-center_85_none/10085/xhs2-fuji-70-300-Amazilia-Hummingbird.webp", "fileVariableName": "add_file_sample_file_bird", "outputFilename": "add_file_xhs2-fuji-70-300-Amazilia-Hummingbird.webp", "urlSource": "INTERNET"}, "typePrimaryIdentifier": "downloadFile"}, "variant": "action"}, "aNJg9aHJ8YPNetktqsOx6": {"id": "aNJg9aHJ8YPNetktqsOx6", "metadata": {"createdAt": "2025-08-08T07:19:10.037Z", "updatedAt": "2025-08-08T07:19:10.037Z"}, "name": "Set file", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{add_file_sample_file_bird}}", "columnId": "", "fileOperation": "addFile", "operation": "", "questionId": "VA7AgyzZST", "questionTypeWithOperation": "file", "rowIndex": "", "rowSelection": ""}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}}, "triggers": {"a0MR7wiFPrtvQ6gt5S4zc": {"id": "a0MR7wiFPrtvQ6gt5S4zc", "metadata": {"createdAt": "2025-08-08T07:18:47.088Z", "updatedAt": "2025-08-08T07:18:47.089Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set file answers — add file", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aPZqzFUt5rIF8B2dZTap3": {"description": "", "endingVariables": [], "id": "aPZqzFUt5rIF8B2dZTap3", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-26T07:41:55.033Z", "updatedAt": "2025-07-16T04:05:43.264Z"}, "name": "<PERSON><PERSON>hook json schema", "start": "a3QAdg0THt65GfjsJjvsw", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.axaNyaPRfi9cn5mgA1rbK"}], "steps": {"a3QAdg0THt65GfjsJjvsw": {"id": "a3QAdg0THt65GfjsJjvsw", "metadata": {"createdAt": "2025-06-26T08:16:59.854Z", "updatedAt": "2025-06-26T08:16:59.854Z"}, "name": "Set variable(s)", "next": "aRmKGORdi2JiLuMhJTTJv", "properties": {"variables": [{"identifier": "jwt", "polyType": "VARIABLE", "type": "text", "value": "\"Bearer \" & {{_workspace_.variables.INTERNAL_API_KEY}}"}]}, "variant": "setVariables"}, "a9ijpcrz43tJrz9xRQkYc": {"id": "a9ijpcrz43tJrz9xRQkYc", "metadata": {"createdAt": "2025-06-26T07:50:49.474Z", "updatedAt": "2025-06-26T07:50:49.474Z"}, "name": "Send an API Request", "next": "", "properties": {"inputs": {"bodyParameters": "{{req_payload}}", "contentType": "application/json", "continueFlowIfApiFails": "true", "headers": [{"key": "Authorization", "value": "{{jwt}}"}], "httpMethod": "POST", "response": [{"httpCode": "200"}], "responseVariableName": "response", "url": "{{_workspace_.variables.CURRENT_HOST}}/ai/api/webhooks/workspaces/{{_workspace_.id}}/flows/aXixKsXjK7dHBBSA6u6Gp"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}, "aRmKGORdi2JiLuMhJTTJv": {"id": "aRmKGORdi2JiLuMhJTTJv", "metadata": {"createdAt": "2025-06-26T08:41:33.844Z", "updatedAt": "2025-06-26T08:41:33.844Z"}, "name": "Set variable(s)", "next": "a9ijpcrz43tJrz9xRQkYc", "properties": {"variables": [{"identifier": "req_payload", "polyType": "VARIABLE", "type": "json", "value": "{\n  \"form\": {\n    \"id\": {{form.id}}\n  },\n  \"number_1\": 123,\n  \"text_1\": \"qwe\",\n  \"boolean_1\": true,\n  \"nested\": {\n    \"test\": true,\n    \"shouldBeFiltered\": \"abc\"\n  }\n}"}]}, "variant": "setVariables"}}, "triggers": {"aTzoI8KByiqCeiUhjPrti": {"id": "aTzoI8KByiqCeiUhjPrti", "metadata": {"createdAt": "2025-06-26T07:50:36.496Z", "updatedAt": "2025-06-26T07:50:36.496Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "<PERSON><PERSON>hook json schema", "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aWnxtfz6lEqoeNXN4URpJ": {"description": "", "endingVariables": [], "id": "aWnxtfz6lEqoeNXN4URpJ", "labels": [], "metadata": {"createdAt": "2025-06-25T01:31:04.693Z", "updatedAt": "2025-06-25T01:31:04.693Z"}, "name": "Update form with properties", "start": "aiWFiyaLJlv8rnC86DVjx", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aiWFiyaLJlv8rnC86DVjx": {"id": "aiWFiyaLJlv8rnC86DVjx", "metadata": {"createdAt": "2025-06-25T01:31:36.473Z", "updatedAt": "2025-06-25T01:31:36.473Z"}, "name": "Update a form", "next": "", "properties": {"inputs": {"formConfigurationId": "aqt", "formId": "{{form.id}}", "formVariableName": "form__step_aiWFiyaLJlv8rnC86DVjx", "properties": [{"key": "key1", "value": "value1"}, {"key": "key2", "value": "value2"}]}, "typePrimaryIdentifier": "updateForm"}, "variant": "action"}}, "triggers": {"aNSXG2Uvve5L4BcXOf2Jd": {"id": "aNSXG2Uvve5L4BcXOf2Jd", "metadata": {"createdAt": "2025-06-25T01:31:09.610Z", "updatedAt": "2025-06-25T01:31:09.610Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Update form with properties", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aXixKsXjK7dHBBSA6u6Gp": {"description": "", "endingVariables": [], "id": "aXixKsXjK7dHBBSA6u6Gp", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-18T00:59:52.773Z", "updatedAt": "2025-07-16T04:05:37.594Z"}, "name": "webhook json schema", "start": "aZmBUJUEy3kUOoqtDoFHm", "startingVariables": [{"identifier": "incomingPayloadXYZ", "properties": {"items": [{"identifier": "form", "properties": {"items": [{"identifier": "id", "properties": {"required": false}, "type": "number"}]}, "type": "json"}, {"identifier": "nested", "properties": {"items": [{"identifier": "test", "properties": {"required": false}, "type": "boolean"}]}, "type": "json"}, {"identifier": "number_1", "properties": {"required": false}, "type": "number"}, {"identifier": "text_1", "properties": {"required": false}, "type": "text"}], "required": true, "schema": {"identifier": "a7hOzZjxYKE51BEtdSpWL", "properties": {"items": [{"identifier": "form", "properties": {"items": [{"identifier": "id", "properties": {"required": false}, "type": "number"}]}, "type": "json"}, {"identifier": "nested", "properties": {"items": [{"identifier": "test", "properties": {"required": false}, "type": "boolean"}]}, "type": "json"}, {"identifier": "number_1", "properties": {"required": false}, "type": "number"}, {"identifier": "text_1", "properties": {"required": false}, "type": "text"}]}, "type": "json"}}, "type": "json"}], "steps": {"aZmBUJUEy3kUOoqtDoFHm": {"id": "aZmBUJUEy3kUOoqtDoFHm", "metadata": {"createdAt": "2025-06-25T01:47:01.411Z", "updatedAt": "2025-06-25T01:47:01.411Z"}, "name": "Set variable(s)", "next": "adtSQD1q3aovv9Io2OEPw", "properties": {"variables": [{"identifier": "number_1", "polyType": "VARIABLE", "type": "text", "value": "{{incomingPayloadXYZ.number_1}}"}, {"identifier": "boolean_1", "polyType": "VARIABLE", "type": "text", "value": "{{incomingPayloadXYZ.boolean_1}}"}]}, "variant": "setVariables"}, "abF1nk7UWTZmoW4huKvgv": {"id": "abF1nk7UWTZmoW4huKvgv", "metadata": {"createdAt": "2025-06-26T08:39:21.055Z", "updatedAt": "2025-06-26T08:39:21.055Z"}, "name": "Set form answer(s)", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{incomingPayloadXYZ}}", "columnId": "aDAq4RmV5s", "operation": "setCell", "questionId": "aDAq4RmV5s", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "2", "rowSelection": "rowIndex"}], "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "adtSQD1q3aovv9Io2OEPw": {"id": "adtSQD1q3aovv9Io2OEPw", "metadata": {"createdAt": "2025-06-26T08:38:56.963Z", "updatedAt": "2025-06-26T08:38:56.963Z"}, "name": "Set variable(s)", "next": "abF1nk7UWTZmoW4huKvgv", "properties": {"variables": [{"identifier": "form", "polyType": "VARIABLE", "type": "json", "value": "{{incomingPayloadXYZ.form}}"}]}, "variant": "setVariables"}}, "triggers": {"aGAcqzGY82qYCH64oYO7P": {"id": "aGAcqzGY82qYCH64oYO7P", "metadata": {"createdAt": "2025-06-18T00:59:55.922Z", "updatedAt": "2025-06-18T00:59:55.922Z"}, "name": "When this flow's webhook is received", "next": "", "properties": {"inputs": {"continueFlowIfApiFails": "false", "expectedPayload": {"sample": {"form": {"id": 17}, "nested": {"test": true}, "number_1": 123, "text_1": "qwe"}, "schema": {"id": "a7hOzZjxYKE51BEtdSpWL", "identifier": "a7hOzZjxYKE51BEtdSpWL", "properties": {"items": [{"id": "form", "identifier": "form", "properties": {"items": [{"id": "id", "identifier": "id", "properties": {"required": false}, "text": "id", "type": "number"}]}, "text": "form", "type": "json"}, {"id": "nested", "identifier": "nested", "properties": {"items": [{"id": "test", "identifier": "test", "properties": {"required": false}, "text": "test", "type": "boolean"}]}, "text": "nested", "type": "json"}, {"id": "number_1", "identifier": "number_1", "properties": {"required": false}, "text": "number_1", "type": "number"}, {"id": "text_1", "identifier": "text_1", "properties": {"required": false}, "text": "text_1", "type": "text"}]}, "text": "JSON", "type": "json"}}, "payloadVariableName": "incomingPayloadXYZ"}, "typePrimaryIdentifier": "receiveIncomingWebhookForFlow"}, "variant": "trigger"}}}, "aYkoyrEM9zcJYE0ycu2w1": {"description": "", "endingVariables": [], "id": "aYkoyrEM9zcJYE0ycu2w1", "labels": [], "metadata": {"createdAt": "2025-08-15T07:11:58.915Z", "updatedAt": "2025-08-15T07:11:58.915Z"}, "name": "set list answer — whole answer", "start": "aDUQzMutVk2jrcHGM8Mwe", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"a4odGSWKNwwT3tPh2YVNM": {"id": "a4odGSWKNwwT3tPh2YVNM", "metadata": {"createdAt": "2025-08-15T07:12:11.906Z", "updatedAt": "2025-08-15T07:12:11.906Z"}, "name": "set list", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{full_list}}", "itemSelection": "", "listOperation": "setList", "questionId": "apZe1DTF8B", "questionTypeWithOperation": "list"}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "aDUQzMutVk2jrcHGM8Mwe": {"id": "aDUQzMutVk2jrcHGM8Mwe", "metadata": {"createdAt": "2025-08-15T07:18:19.494Z", "updatedAt": "2025-08-15T07:18:19.494Z"}, "name": "Set variable(s)", "next": "a4odGSWKNwwT3tPh2YVNM", "properties": {"variables": [{"identifier": "full_list", "polyType": "VARIABLE", "properties": {"listOperation": "setList"}, "type": "list", "value": "[\"123\", \"qwe\"]"}]}, "variant": "setVariables"}}, "triggers": {"aPOZZPyj1JUOQYYyNf1Yq": {"id": "aPOZZPyj1JUOQYYyNf1Yq", "metadata": {"createdAt": "2025-08-15T07:12:02.243Z", "updatedAt": "2025-08-15T07:12:02.243Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set list answer — whole answer", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aZhbyE3seSXCZoEZVvFk3": {"description": "", "endingVariables": [], "id": "aZhbyE3seSXCZoEZVvFk3", "labels": [], "metadata": {"createdAt": "2025-08-08T07:18:44.907Z", "updatedAt": "2025-08-08T07:18:44.907Z"}, "name": "set json answers", "start": "an9dn5It8MdJRa7KZ3VYO", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aNJg9aHJ8YPNetktqsOx6": {"id": "aNJg9aHJ8YPNetktqsOx6", "metadata": {"createdAt": "2025-08-08T07:19:10.037Z", "updatedAt": "2025-08-08T07:19:10.037Z"}, "name": "Set json answer", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{json_answer}}", "columnId": "", "operation": "", "questionId": "a1sUhILky8o", "questionTypeWithOperation": "no", "rowIndex": "", "rowSelection": ""}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "an9dn5It8MdJRa7KZ3VYO": {"id": "an9dn5It8MdJRa7KZ3VYO", "metadata": {"createdAt": "2025-08-18T02:37:53.231Z", "updatedAt": "2025-08-18T02:37:53.232Z"}, "name": "Set variable(s)", "next": "aNJg9aHJ8YPNetktqsOx6", "properties": {"variables": [{"identifier": "json_answer", "polyType": "VARIABLE", "type": "json", "value": "{  \"Text\": \"ALO\", \"Numberaccounting\":7}"}, {"identifier": "variable", "polyType": "VARIABLE", "type": "json", "value": "$merge([{{form.a1sUhILky8o.answer}}, {  {{form.Json.answer.Text.configuration.id}}  : \"Los Angeles\"}])"}, {"identifier": "textAnswer", "polyType": "VARIABLE", "type": "text", "value": "{{form.a1sUhILky8o.answer.a7amxY9Mgw3}}"}, {"identifier": "json_answer_init", "polyType": "VARIABLE", "type": "json", "value": "{{form.a1sUhILky8o.answer}}"}, {"identifier": "variable_1", "polyType": "VARIABLE", "type": "text", "value": "{{form.a1sUhILky8o.items.a7amxY9Mgw3}}"}, {"identifier": "json_answer_2", "polyType": "VARIABLE", "type": "json", "value": "$merge([{{form.a1sUhILky8o.answer}}, {  \"a7amxY9Mgw3\": \"ALO\"}])"}]}, "variant": "setVariables"}}, "triggers": {"a0MR7wiFPrtvQ6gt5S4zc": {"id": "a0MR7wiFPrtvQ6gt5S4zc", "metadata": {"createdAt": "2025-08-08T07:18:47.088Z", "updatedAt": "2025-08-08T07:18:47.089Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set json answers", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aa411a1078aE2Eaupdateaansweraandaraiseaalert": {"description": "aa411a1078aE2Eaupdateaansweraandaraiseaalert", "id": "aa411a1078aE2Eaupdateaansweraandaraiseaalert", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "411-1078-E2E-update-answer-and-raise-alert", "start": "step1", "steps": {"step1": {"id": "step1", "name": "Condition", "next": "step2", "properties": {"branches": [{"condition": {"lhs": "{{answer.answer}}", "operator": ">", "rhs": "100000"}, "name": "If", "next": "step2"}, {"condition": {"lhs": "", "operator": "=", "rhs": ""}, "name": "else"}]}, "variant": "condition"}, "step2": {"id": "step2", "name": "Set form alert", "properties": {"inputs": {"formId": "a1132aE2Eaform", "message": "Answer provided is greater than 100,000. Please check.", "operation": "add", "questionId": "O42Cd6cSeZ", "variant": "blocker"}, "typePrimaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>"}, "variant": "action"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "When answer(s) changed in a form", "next": "", "properties": {"inputs": {"changedQuestionVariableName": "answer", "formConfigurationId": "a1132aE2Eaform", "formVariableName": "form", "questionIds": "[O42Cd6cSeZ]"}, "typePrimaryIdentifier": "answerChanged"}, "variant": "trigger"}}}, "ab5w1WUyC4H4gjSz4s60U": {"description": "REQUIREMENT: API Key to be set in workspace variables under the name of INTERNAL_API_KEY\nInstructions: Run this flow and observe OTAI-BE logs for received payload. See relevant endpoint in code", "endingVariables": [], "id": "ab5w1WUyC4H4gjSz4s60U", "labels": ["aqH1fHtaj65vgqtNIRnb8"], "metadata": {"createdAt": "2025-09-01T07:32:55.416Z", "updatedAt": "2025-09-08T07:27:06.436Z"}, "name": "test send api content type multipart form data using file upload", "start": "aWmf6brGuuW9G5pxfCjYS", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aH4HK68XCeYnG39c4uJZu": {"id": "aH4HK68XCeYnG39c4uJZu", "metadata": {"createdAt": "2025-09-01T07:34:32.469Z", "updatedAt": "2025-09-01T07:34:32.469Z"}, "name": "Set variable(s)", "next": "apeEe4xpqUl9gXGVPHigU", "properties": {"variables": [{"identifier": "body", "polyType": "VARIABLE", "type": "json", "value": "{\n    \"files\": { \"file1\": \n        {{fileToSend}}\n    }\n}"}, {"identifier": "api_key", "polyType": "VARIABLE", "type": "text", "value": "\"Bearer \" & {{_workspace_.variables.INTERNAL_API_KEY}}"}]}, "variant": "setVariables"}, "aWmf6brGuuW9G5pxfCjYS": {"id": "aWmf6brGuuW9G5pxfCjYS", "metadata": {"createdAt": "2025-09-01T13:53:08.728Z", "updatedAt": "2025-09-01T13:53:08.729Z"}, "name": "Set variable(s)", "next": "aH4HK68XCeYnG39c4uJZu", "properties": {"variables": [{"identifier": "fileToSend", "polyType": "VARIABLE", "type": "json", "value": "{{form.VA7AgyzZST.answer}}[0]"}]}, "variant": "setVariables"}, "apeEe4xpqUl9gXGVPHigU": {"id": "apeEe4xpqUl9gXGVPHigU", "metadata": {"createdAt": "2025-09-01T07:33:08.270Z", "updatedAt": "2025-09-01T07:33:08.270Z"}, "name": "Send an API Request", "next": "", "properties": {"inputs": {"bodyParameters": "{{body}}", "contentType": "multipart/form-data", "continueFlowIfApiFails": "false", "headers": [{"key": "Authorization", "value": "{{api_key}}"}], "httpMethod": "POST", "response": [{"httpCode": "200"}], "responseVariableName": "response", "url": "{{_workspace_.variables.CURRENT_HOST}}/ai/api/{{_workspace_.variables.test_content_types_url_base}}/multipart"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}}, "triggers": {"aKUAt5VT3Ujdltf5WWFZE": {"id": "aKUAt5VT3Ujdltf5WWFZE", "metadata": {"createdAt": "2025-09-01T07:33:00.654Z", "updatedAt": "2025-09-01T07:33:00.654Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "test send api content type multipart form data using file upload", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "actiona1": {"description": "A simple flow that run the action of set answer", "id": "actiona1", "labels": [], "metadata": {"createdAt": "2025-01-01T22:55:45.267Z", "updatedAt": "2025-01-01T22:55:45.267Z"}, "name": "set answer action", "start": "step1", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"step1": {"id": "step1", "name": "Set answer", "next": "", "properties": {"inputs": {"answer": "by flow4", "formConfigurationId": "aqt", "formId": "{{form.id}}", "questionId": "plBrtlhc3W", "questionTypeWithOperation": "no"}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Action 1", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "af9Hjd4d7xd4N7vsCOv1Y": {"description": "", "endingVariables": [], "id": "af9Hjd4d7xd4N7vsCOv1Y", "labels": [], "metadata": {"createdAt": "2025-05-14T05:56:32.906Z", "updatedAt": "2025-05-14T05:56:32.906Z"}, "name": "Pairwise", "start": "avWITIW7XqxTzUzzMbCKd", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aK5h7h9Rhc8inDd0vho1K": {"id": "aK5h7h9Rhc8inDd0vho1K", "metadata": {"createdAt": "2025-05-14T05:57:06.423Z", "updatedAt": "2025-05-14T05:57:06.423Z"}, "name": "Set variable(s)", "next": "aq2KXnMpfHClFlTOyiRtU", "properties": {"variables": [{"identifier": "variable_1", "polyType": "VARIABLE", "type": "json", "value": "$SUM([1,2], [3,4], [5,6])"}]}, "variant": "setVariables"}, "aq2KXnMpfHClFlTOyiRtU": {"id": "aq2KXnMpfHClFlTOyiRtU", "metadata": {"createdAt": "2025-05-14T06:08:23.516Z", "updatedAt": "2025-05-14T06:08:23.516Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "variable_z", "polyType": "VARIABLE", "type": "json", "value": "$SUM([0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1])"}]}, "variant": "setVariables"}, "avWITIW7XqxTzUzzMbCKd": {"id": "avWITIW7XqxTzUzzMbCKd", "metadata": {"createdAt": "2025-05-14T06:03:58.571Z", "updatedAt": "2025-05-14T06:03:58.571Z"}, "name": "Set variable(s)", "next": "aK5h7h9Rhc8inDd0vho1K", "properties": {"variables": [{"identifier": "variable_a", "polyType": "VARIABLE", "type": "json", "value": "[1,2]"}, {"identifier": "variable_b", "polyType": "VARIABLE", "type": "json", "value": "[3,4]"}, {"identifier": "variable_c", "polyType": "VARIABLE", "type": "json", "value": "[5,6]"}, {"identifier": "variable_d", "polyType": "VARIABLE", "type": "json", "value": "$SUM({{variable_a}}, {{variable_b}}, {{variable_c}})"}]}, "variant": "setVariables"}}, "triggers": {"ahNmSIvqpCuCQc1CmgTrT": {"id": "ahNmSIvqpCuCQc1CmgTrT", "metadata": {"createdAt": "2025-05-14T05:56:42.428Z", "updatedAt": "2025-05-14T05:56:42.428Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Pairwise", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "afdVCnedfO1dLNUR0laBi": {"description": "", "endingVariables": [], "id": "afdVCnedfO1dLNUR0laBi", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-18T00:59:52.773Z", "updatedAt": "2025-07-16T04:06:06.853Z"}, "name": "receive webhook no schema", "start": "aZmBUJUEy3kUOoqtDoFHm", "startingVariables": [{"identifier": "incomingPayloadXYZ", "properties": {"items": [], "required": true, "schema": {"identifier": "ao6QtKPTua7J15uDHUYgt", "properties": {}, "type": "json"}}, "type": "json"}], "steps": {"aZmBUJUEy3kUOoqtDoFHm": {"id": "aZmBUJUEy3kUOoqtDoFHm", "metadata": {"createdAt": "2025-06-25T01:47:01.411Z", "updatedAt": "2025-06-25T01:47:01.411Z"}, "name": "Set variable(s)", "next": "adtSQD1q3aovv9Io2OEPw", "properties": {"variables": [{"identifier": "number_1", "polyType": "VARIABLE", "type": "text", "value": "{{incomingPayloadXYZ.number_1}}"}, {"identifier": "boolean_1", "polyType": "VARIABLE", "type": "text", "value": "{{incomingPayloadXYZ.boolean_1}}"}]}, "variant": "setVariables"}, "abF1nk7UWTZmoW4huKvgv": {"id": "abF1nk7UWTZmoW4huKvgv", "metadata": {"createdAt": "2025-06-26T08:39:21.055Z", "updatedAt": "2025-06-26T08:39:21.055Z"}, "name": "Set form answer(s)", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{incomingPayloadXYZ}}", "columnId": "aDAq4RmV5s", "operation": "setCell", "questionId": "aDAq4RmV5s", "questionTypeWithOperation": "table", "rowId": "", "rowIndex": "5", "rowSelection": "rowIndex"}], "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "adtSQD1q3aovv9Io2OEPw": {"id": "adtSQD1q3aovv9Io2OEPw", "metadata": {"createdAt": "2025-06-26T08:38:56.963Z", "updatedAt": "2025-06-26T08:38:56.963Z"}, "name": "Set variable(s)", "next": "abF1nk7UWTZmoW4huKvgv", "properties": {"variables": [{"identifier": "form", "polyType": "VARIABLE", "type": "json", "value": "{{incomingPayloadXYZ.form}}"}]}, "variant": "setVariables"}}, "triggers": {"aGAcqzGY82qYCH64oYO7P": {"id": "aGAcqzGY82qYCH64oYO7P", "metadata": {"createdAt": "2025-06-18T00:59:55.922Z", "updatedAt": "2025-06-18T00:59:55.922Z"}, "name": "When this flow's webhook is received", "next": "", "properties": {"inputs": {"continueFlowIfApiFails": "false", "expectedPayload": {"sample": {}, "schema": {"id": "ao6QtKPTua7J15uDHUYgt", "identifier": "ao6QtKPTua7J15uDHUYgt", "properties": {"items": []}, "text": "JSON", "type": "json"}}, "payloadVariableName": "incomingPayloadXYZ"}, "typePrimaryIdentifier": "receiveIncomingWebhookForFlow"}, "variant": "trigger"}}}, "ahMqR0GgQHdbmYVMYA9F7": {"description": "", "endingVariables": [], "id": "ahMqR0GgQHdbmYVMYA9F7", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-27T01:44:32.036Z", "updatedAt": "2025-07-16T04:05:57.498Z"}, "name": "Start Schema Tests", "start": "avsWd4kMVgxqI7d8Ctjre", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.axaNyaPRfi9cn5mgA1rbK"}], "steps": {"a1Xo8rVcGSDJSMbx8g1RF": {"id": "a1Xo8rVcGSDJSMbx8g1RF", "metadata": {"createdAt": "2025-06-28T01:31:08.230Z", "updatedAt": "2025-06-28T01:31:08.230Z"}, "name": "Send API Request list schema", "next": "", "properties": {"inputs": {"flowConfigurationId": "aFgSMhUjZabwII3l01NZ6", "form": "{{form.id}}"}}, "variant": "flow"}, "a6fmyMjWUBDwLWVmYRNBS": {"id": "a6fmyMjWUBDwLWVmYRNBS", "metadata": {"createdAt": "2025-06-27T06:14:02.835Z", "updatedAt": "2025-06-27T06:14:02.835Z"}, "name": "Set form answer(s)", "next": "ae8HpoZShA6Iv9thn1lxG", "properties": {"inputs": {"answers": [{"answer": "{{emptyArray}}", "columnId": "{{form.aC2mcecXgh.columns.aDAq4RmV5s.id}}", "operation": "setColumn", "questionId": "aDAq4RmV5s", "questionTypeWithOperation": "table", "rowSelection": ""}], "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "aQdAox3xAhCcU8fpTBxyc": {"id": "aQdAox3xAhCcU8fpTBxyc", "metadata": {"createdAt": "2025-06-30T07:36:13.950Z", "updatedAt": "2025-06-30T07:36:13.950Z"}, "name": "Trigger Webhook no schema", "next": "aRgpgGG9KApGiRaIwpTA8", "properties": {"inputs": {"flowConfigurationId": "aiU5RJCqFTbz7bSvNYIiY", "form": "{{form.id}}"}}, "variant": "flow"}, "aRgpgGG9KApGiRaIwpTA8": {"id": "aRgpgGG9KApGiRaIwpTA8", "metadata": {"createdAt": "2025-06-28T01:31:15.326Z", "updatedAt": "2025-06-28T01:31:15.326Z"}, "name": "Trigger Webhook list schema", "next": "a1Xo8rVcGSDJSMbx8g1RF", "properties": {"inputs": {"flowConfigurationId": "aFL3kpI953xWTdBO9GWg9", "form": "{{form.id}}"}}, "variant": "flow"}, "aXqlj4XS9dexpeGUVfq6H": {"id": "aXqlj4XS9dexpeGUVfq6H", "metadata": {"createdAt": "2025-06-30T07:36:00.781Z", "updatedAt": "2025-06-30T07:36:00.781Z"}, "name": "Send API Request no schema", "next": "aQdAox3xAhCcU8fpTBxyc", "properties": {"inputs": {"flowConfigurationId": "a9lAj1wtT4zk8AfSg918f", "form": "{{form.id}}"}}, "variant": "flow"}, "abSO7kgGSLRPI7JcKfTpg": {"id": "abSO7kgGSLRPI7JcKfTpg", "metadata": {"createdAt": "2025-06-27T01:46:48.933Z", "updatedAt": "2025-06-27T01:46:48.933Z"}, "name": "<PERSON><PERSON>hook json schema", "next": "aXqlj4XS9dexpeGUVfq6H", "properties": {"inputs": {"flowConfigurationId": "aPZqzFUt5rIF8B2dZTap3", "form": "{{form.id}}"}}, "variant": "flow"}, "ae8HpoZShA6Iv9thn1lxG": {"id": "ae8HpoZShA6Iv9thn1lxG", "metadata": {"createdAt": "2025-06-27T01:44:40.215Z", "updatedAt": "2025-06-27T01:44:40.215Z"}, "name": "Send API Request json schema", "next": "abSO7kgGSLRPI7JcKfTpg", "properties": {"inputs": {"flowConfigurationId": "7IbcClHBHX5lxmmaJVBeg", "form": "{{form.id}}"}}, "variant": "flow"}, "avsWd4kMVgxqI7d8Ctjre": {"id": "avsWd4kMVgxqI7d8Ctjre", "metadata": {"createdAt": "2025-06-27T06:33:03.010Z", "updatedAt": "2025-06-27T06:33:03.010Z"}, "name": "Set variable(s)", "next": "a6fmyMjWUBDwLWVmYRNBS", "properties": {"variables": [{"identifier": "emptyArray", "polyType": "VARIABLE", "properties": {"listOperation": "setList"}, "type": "list", "value": "[]"}]}, "variant": "setVariables"}}, "triggers": {"aaaSBRETXAINncQeK7s6K": {"id": "aaaSBRETXAINncQeK7s6K", "metadata": {"createdAt": "2025-06-27T01:44:34.442Z", "updatedAt": "2025-06-27T01:44:34.442Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Start Schema Tests", "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "aiU5RJCqFTbz7bSvNYIiY": {"description": "", "endingVariables": [], "id": "aiU5RJCqFTbz7bSvNYIiY", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "metadata": {"createdAt": "2025-06-26T07:41:55.033Z", "updatedAt": "2025-07-16T04:06:13.881Z"}, "name": "Trigger Webhook no schema", "start": "a3QAdg0THt65GfjsJjvsw", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.axaNyaPRfi9cn5mgA1rbK"}], "steps": {"a3QAdg0THt65GfjsJjvsw": {"id": "a3QAdg0THt65GfjsJjvsw", "metadata": {"createdAt": "2025-06-26T08:16:59.854Z", "updatedAt": "2025-06-26T08:16:59.854Z"}, "name": "Set variable(s)", "next": "aRmKGORdi2JiLuMhJTTJv", "properties": {"variables": [{"identifier": "jwt", "polyType": "VARIABLE", "type": "text", "value": "\"Bearer \" & {{_workspace_.variables.INTERNAL_API_KEY}}"}]}, "variant": "setVariables"}, "a9ijpcrz43tJrz9xRQkYc": {"id": "a9ijpcrz43tJrz9xRQkYc", "metadata": {"createdAt": "2025-06-26T07:50:49.474Z", "updatedAt": "2025-06-26T07:50:49.474Z"}, "name": "Send an API Request", "next": "", "properties": {"inputs": {"bodyParameters": "{{req_payload}}", "contentType": "application/json", "continueFlowIfApiFails": "true", "headers": [{"key": "Authorization", "value": "{{jwt}}"}, {"key": "Content-Type", "value": "application/json"}], "httpMethod": "POST", "response": [{"httpCode": "200"}], "responseVariableName": "response", "url": "{{_workspace_.variables.CURRENT_HOST}}/ai/api/webhooks/workspaces/{{_workspace_.id}}/flows/afdVCnedfO1dLNUR0laBi"}, "typePrimaryIdentifier": "sendApiRequest"}, "variant": "action"}, "aRmKGORdi2JiLuMhJTTJv": {"id": "aRmKGORdi2JiLuMhJTTJv", "metadata": {"createdAt": "2025-06-26T08:41:33.844Z", "updatedAt": "2025-06-26T08:41:33.844Z"}, "name": "Set variable(s)", "next": "a9ijpcrz43tJrz9xRQkYc", "properties": {"variables": [{"identifier": "req_payload", "polyType": "VARIABLE", "type": "json", "value": "{\n  \"form\": {\n    \"id\": {{form.id}}\n  },\n  \"number_1\": 123,\n  \"text_1\": \"qwe\",\n  \"boolean_1\": true,\n  \"nested\": {\n    \"test\": true,\n    \"shouldBeFiltered\": \"abc\"\n  }\n}"}]}, "variant": "setVariables"}}, "triggers": {"aTzoI8KByiqCeiUhjPrti": {"id": "aTzoI8KByiqCeiUhjPrti", "metadata": {"createdAt": "2025-06-26T07:50:36.496Z", "updatedAt": "2025-06-26T07:50:36.496Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Trigger Webhook no schema", "formConfigurationId": "axaNyaPRfi9cn5mgA1rbK", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "almX7K2eofUb8sx7Ve2jE": {"description": "", "endingVariables": [], "id": "almX7K2eofUb8sx7Ve2jE", "labels": [], "metadata": {"createdAt": "2025-08-21T05:08:43.664Z", "updatedAt": "2025-08-21T05:08:43.664Z"}, "name": "Create Foundation and Form", "start": "aQHjyhCN7SEGHRzuC5IPt", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aQHjyhCN7SEGHRzuC5IPt": {"id": "aQHjyhCN7SEGHRzuC5IPt", "metadata": {"createdAt": "2025-08-21T05:09:07.409Z", "updatedAt": "2025-08-21T05:09:07.409Z"}, "name": "Set variable(s)", "next": "aX78z0736nJmDu14wlA2H", "properties": {"variables": [{"identifier": "NOW", "polyType": "VARIABLE", "type": "text", "value": "$NOW()"}, {"identifier": "newFoundation", "polyType": "VARIABLE", "type": "text", "value": "$CONCAT(\"FND\",{{NOW}})"}, {"identifier": "newForm", "polyType": "VARIABLE", "type": "text", "value": "$CONCAT(\"FRM\",{{NOW}})"}]}, "variant": "setVariables"}, "aX78z0736nJmDu14wlA2H": {"id": "aX78z0736nJmDu14wlA2H", "metadata": {"createdAt": "2025-08-21T05:11:18.558Z", "updatedAt": "2025-08-21T05:11:18.558Z"}, "name": "Select or create a foundation", "next": "ayNhHDokFAWy2rqlaMOa2", "properties": {"inputs": {"foundationConfigurationId": "cli", "foundationVariableName": "foundation__step_aX78z0736nJmDu14wlA2H", "key": "{{newFoundation}}", "name": "{{newFoundation}}", "parentId": "{{form.foundation.id}}"}, "typePrimaryIdentifier": "selectOrCreateFoundation"}, "variant": "action"}, "ayNhHDokFAWy2rqlaMOa2": {"id": "ayNhHDokFAWy2rqlaMOa2", "metadata": {"createdAt": "2025-08-21T05:36:28.920Z", "updatedAt": "2025-08-21T05:36:28.920Z"}, "name": "Select or create a form", "next": "", "properties": {"inputs": {"formConfigurationId": "ahFVQ2Mk6tZ0r1DKHICTG", "formVariableName": "form__step_ayNhHDokFAWy2rqlaMOa2", "foundationId": "{{foundation__step_aX78z0736nJmDu14wlA2H.id}}"}, "typePrimaryIdentifier": "selectOrCreateForm"}, "variant": "action"}}, "triggers": {"a2XeYdFAXISnGsUm7ofCj": {"id": "a2XeYdFAXISnGsUm7ofCj", "metadata": {"createdAt": "2025-08-21T05:08:46.560Z", "updatedAt": "2025-08-21T05:08:46.560Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Create Foundation and Form", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "ap64lRaU6LpqDJfMZ9gNS": {"description": "", "endingVariables": [], "id": "ap64lRaU6LpqDJfMZ9gNS", "labels": [], "metadata": {"createdAt": "2025-08-15T07:11:58.915Z", "updatedAt": "2025-08-15T07:11:58.915Z"}, "name": "set list answer — set item", "start": "aDUQzMutVk2jrcHGM8Mwe", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"a4odGSWKNwwT3tPh2YVNM": {"id": "a4odGSWKNwwT3tPh2YVNM", "metadata": {"createdAt": "2025-08-15T07:12:11.906Z", "updatedAt": "2025-08-15T07:12:11.906Z"}, "name": "set list", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{one_item}}", "itemIndex": "2", "itemSelection": "itemIndex", "listOperation": "setItem", "questionId": "apZe1DTF8B", "questionTypeWithOperation": "list"}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "aDUQzMutVk2jrcHGM8Mwe": {"id": "aDUQzMutVk2jrcHGM8Mwe", "metadata": {"createdAt": "2025-08-15T07:18:19.494Z", "updatedAt": "2025-08-15T07:18:19.494Z"}, "name": "Set variable(s)", "next": "a4odGSWKNwwT3tPh2YVNM", "properties": {"variables": [{"identifier": "one_item", "polyType": "VARIABLE", "type": "text", "value": "\"123\""}]}, "variant": "setVariables"}}, "triggers": {"aPOZZPyj1JUOQYYyNf1Yq": {"id": "aPOZZPyj1JUOQYYyNf1Yq", "metadata": {"createdAt": "2025-08-15T07:12:02.243Z", "updatedAt": "2025-08-15T07:12:02.243Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set list answer — set item", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "apiEKfzR0nU0uBf89ee9T": {"description": "", "endingVariables": [], "id": "apiEKfzR0nU0uBf89ee9T", "labels": [], "metadata": {"createdAt": "2025-08-08T07:18:44.907Z", "updatedAt": "2025-08-08T07:18:44.907Z"}, "name": "set file answers — remove file", "start": "aNJg9aHJ8YPNetktqsOx6", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aNJg9aHJ8YPNetktqsOx6": {"id": "aNJg9aHJ8YPNetktqsOx6", "metadata": {"createdAt": "2025-08-08T07:19:10.037Z", "updatedAt": "2025-08-08T07:19:10.037Z"}, "name": "Set file", "next": "", "properties": {"inputs": {"answers": [{"answer": "", "columnId": "", "fileIndex": "1", "fileOperation": "removeFile", "operation": "", "questionId": "VA7AgyzZST", "questionTypeWithOperation": "file", "rowIndex": "", "rowSelection": ""}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}}, "triggers": {"a0MR7wiFPrtvQ6gt5S4zc": {"id": "a0MR7wiFPrtvQ6gt5S4zc", "metadata": {"createdAt": "2025-08-08T07:18:47.088Z", "updatedAt": "2025-08-08T07:18:47.089Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set file answers — remove file", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "asQV8EOOTL2drFyCBYt6A": {"description": "", "endingVariables": [], "id": "asQV8EOOTL2drFyCBYt6A", "labels": [], "metadata": {"createdAt": "2025-08-08T07:18:44.907Z", "updatedAt": "2025-08-08T07:18:44.907Z"}, "name": "set table answers — whole table", "start": "aNQ80rCznIgs6B2Gov1df", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"a0et89wVmK5mFB7AROoPe": {"id": "a0et89wVmK5mFB7AROoPe", "metadata": {"createdAt": "2025-08-08T07:42:22.001Z", "updatedAt": "2025-08-08T07:42:22.001Z"}, "name": "Set table column", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{table}}", "columnId": "", "operation": "setTable", "questionId": "VEOTahOeLj", "questionTypeWithOperation": "table", "rowIndex": "", "rowSelection": "", "valueRowIdShouldUpdate": "false"}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}, "aNQ80rCznIgs6B2Gov1df": {"id": "aNQ80rCznIgs6B2Gov1df", "metadata": {"createdAt": "2025-08-15T03:08:01.344Z", "updatedAt": "2025-08-15T03:08:01.345Z"}, "name": "Set variable(s)", "next": "aR0quPTur9fJdX4CVR3Og", "properties": {"variables": [{"identifier": "table_row", "polyType": "VARIABLE", "type": "json", "value": "{  {{form.VEOTahOeLj.columns.O1REtNdy50.id}} : \"textFlow_2\",   {{form.VEOTahOeLj.columns.eALIKF2hFv.id}} : 7689 }"}, {"identifier": "table_row_1", "polyType": "VARIABLE", "type": "json", "value": "{  {{form.VEOTahOeLj.columns.O1REtNdy50.id}} : \"textFlow_22\",   {{form.VEOTahOeLj.columns.eALIKF2hFv.id}} : 768922 }"}]}, "variant": "setVariables"}, "aR0quPTur9fJdX4CVR3Og": {"id": "aR0quPTur9fJdX4CVR3Og", "metadata": {"createdAt": "2025-08-08T07:41:59.142Z", "updatedAt": "2025-08-08T07:41:59.142Z"}, "name": "Set variable(s)", "next": "a0et89wVmK5mFB7AROoPe", "properties": {"variables": [{"identifier": "table", "polyType": "VARIABLE", "properties": {"operation": "setTable"}, "type": "table", "value": "{{form.VEOTahOeLj.answer}}"}, {"identifier": "table", "polyType": "VARIABLE", "properties": {"operation": "setRow", "rowIndex": "1"}, "type": "table", "value": "{{table_row}}"}, {"identifier": "table", "polyType": "VARIABLE", "properties": {"operation": "setRow", "rowIndex": "2"}, "type": "table", "value": "{{table_row_1}}"}]}, "variant": "setVariables"}}, "triggers": {"a0MR7wiFPrtvQ6gt5S4zc": {"id": "a0MR7wiFPrtvQ6gt5S4zc", "metadata": {"createdAt": "2025-08-08T07:18:47.088Z", "updatedAt": "2025-08-08T07:18:47.089Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set table answers — whole table", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "avk8XNQp65ZcxIN7QDgwS": {"description": "", "endingVariables": [], "id": "avk8XNQp65ZcxIN7QDgwS", "labels": [], "metadata": {"createdAt": "2025-08-08T07:18:44.907Z", "updatedAt": "2025-08-08T07:18:44.907Z"}, "name": "set file answers — set file", "start": "aHa18wm5V8yZOHr16aSyn", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aHa18wm5V8yZOHr16aSyn": {"id": "aHa18wm5V8yZOHr16aSyn", "metadata": {"createdAt": "2025-08-18T03:10:47.028Z", "updatedAt": "2025-08-18T03:10:47.028Z"}, "name": "Download a file", "next": "aNJg9aHJ8YPNetktqsOx6", "properties": {"inputs": {"fileUrl": "https://fujiframe.com/assets/images/_3000x2000_fit_center-center_85_none/10085/xhs2-fuji-70-300-Amazilia-Hummingbird.webp", "fileVariableName": "sample_file_bird", "outputFilename": "xhs2-fuji-70-300-Amazilia-Hummingbird.webp", "urlSource": "INTERNET"}, "typePrimaryIdentifier": "downloadFile"}, "variant": "action"}, "aNJg9aHJ8YPNetktqsOx6": {"id": "aNJg9aHJ8YPNetktqsOx6", "metadata": {"createdAt": "2025-08-08T07:19:10.037Z", "updatedAt": "2025-08-08T07:19:10.037Z"}, "name": "Set file", "next": "", "properties": {"inputs": {"answers": [{"answer": "{{sample_file_bird}}", "columnId": "", "fileOperation": "setFile", "operation": "", "questionId": "VA7AgyzZST", "questionTypeWithOperation": "file", "rowIndex": "", "rowSelection": ""}], "formConfigurationId": "aqt", "formId": "{{form.id}}"}, "typePrimaryIdentifier": "setMultipleAnswers"}, "variant": "action"}}, "triggers": {"a0MR7wiFPrtvQ6gt5S4zc": {"id": "a0MR7wiFPrtvQ6gt5S4zc", "metadata": {"createdAt": "2025-08-08T07:18:47.088Z", "updatedAt": "2025-08-08T07:18:47.089Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "set file answers — set file", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "conditiona1": {"description": "A condition example", "id": "conditiona1", "labels": [], "metadata": {"createdAt": "2025-01-01T22:55:45.267Z", "updatedAt": "2025-01-01T22:55:45.267Z"}, "name": "condition1", "start": "step1", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"aOje31VdHjyVjR09EGx9T": {"id": "aOje31VdHjyVjR09EGx9T", "metadata": {"createdAt": "2025-05-27T05:28:59.669Z", "updatedAt": "2025-05-27T05:28:59.669Z"}, "name": "Condition", "next": "aoZMfab8njLx1BrQCufHs", "properties": {"branches": [{"condition": {"lhs": "{{form.np9GiUMruw.answer}}", "operator": "<", "rhs": "3"}, "name": "Check if number answer is less than 3", "next": "ahTW8BlNpNBttEdrgTkgr"}]}, "variant": "condition"}, "ahTW8BlNpNBttEdrgTkgr": {"id": "ahTW8BlNpNBttEdrgTkgr", "metadata": {"createdAt": "2025-05-28T04:20:13.488Z", "updatedAt": "2025-05-28T04:20:13.488Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "ConditionResult2", "polyType": "VARIABLE", "type": "text", "value": "\"Number answer is < 3\""}]}, "variant": "setVariables"}, "akQBXMz0DtMF0X5teMfNL": {"id": "akQBXMz0DtMF0X5teMfNL", "metadata": {"createdAt": "2025-05-28T04:19:16.175Z", "updatedAt": "2025-05-28T04:19:16.175Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "ConditionResult1", "polyType": "VARIABLE", "type": "text", "value": "\"Number answer is > 5\""}]}, "variant": "setVariables"}, "aoZMfab8njLx1BrQCufHs": {"id": "aoZMfab8njLx1BrQCufHs", "metadata": {"createdAt": "2025-05-28T04:20:46.132Z", "updatedAt": "2025-05-28T04:20:46.132Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "ConditionResult3", "polyType": "VARIABLE", "type": "text", "value": "\"Number answer is between 3 and 5\""}]}, "variant": "setVariables"}, "step1": {"id": "step1", "name": "Simple Condition", "next": "aOje31VdHjyVjR09EGx9T", "properties": {"branches": [{"condition": {"AND": [{"lhs": "{{form.np9GiUMruw.answer}}", "operator": ">", "rhs": 5}]}, "name": "Check if number answer is greater than 5", "next": "step2"}]}, "variant": "condition"}, "step2": {"id": "step2", "name": "Triggered if condition passes", "next": "akQBXMz0DtMF0X5teMfNL", "properties": {"variables": [{"identifier": "conditionPassed", "polyType": "VARIABLE", "type": "boolean", "value": true}]}, "variant": "setVariables"}}, "triggers": {"trigger1": {"id": "trigger1", "name": "Manual trigger", "properties": {"inputs": {"buttonLabel": "Condition 1", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "iteratoraaggregate": {"description": "", "id": "iteratoraaggregate", "labels": [], "metadata": {"createdAt": "2025-01-02T02:33:55.914Z", "updatedAt": "2025-01-02T02:33:55.914Z"}, "name": "iteratoraaggregate", "start": "kF4WCW2si5HYwWZdXnnFw", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"kF4WCW2si5HYwWZdXnnFw": {"id": "kF4WCW2si5HYwWZdXnnFw", "metadata": {"createdAt": "2025-03-13T06:04:11.116Z", "updatedAt": "2025-03-13T06:04:11.116Z"}, "name": "Aggregate", "next": "", "properties": {"configuration": {"labels": [], "start": "JLtazbmsG3RaymFOm0YWz", "steps": {"JLtazbmsG3RaymFOm0YWz": {"id": "JLtazbmsG3RaymFOm0YWz", "metadata": {"createdAt": "2025-03-13T06:07:14.218Z", "updatedAt": "2025-03-13T06:07:14.218Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "item_total", "polyType": "VARIABLE", "type": "number", "value": "{{item_total}}+{{item.eALIKF2hFv}}"}]}, "variant": "setVariables"}}}, "inputs": {"itemVariableName": "item", "list": "{{form.VEOTahOeLj.answer}}", "resultVariableName": "item_total", "resultVariableType": "number"}, "typePrimaryIdentifier": "iteratorAggregate"}, "variant": "iterator"}}, "triggers": {"igO5lJWkpaau6DN4dHH8l": {"id": "igO5lJWkpaau6DN4dHH8l", "metadata": {"createdAt": "2025-03-13T06:03:39.706Z", "updatedAt": "2025-03-13T06:03:39.706Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Iterator Aggregate", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "iteratorafilter": {"description": "", "id": "iteratorafilter", "labels": [], "metadata": {"createdAt": "2025-03-13T04:10:53.984Z", "updatedAt": "2025-03-13T04:10:53.984Z"}, "name": "iteratorafilter", "start": "a368d0wtXJuSulauJ3AgiY", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"a368d0wtXJuSulauJ3AgiY": {"id": "a368d0wtXJuSulauJ3AgiY", "metadata": {"createdAt": "2025-03-13T04:11:21.058Z", "updatedAt": "2025-03-13T04:11:21.058Z"}, "name": "Filter", "next": "", "properties": {"configuration": {"labels": [], "start": "xaUvKzawHph6fJbP8vTjG", "steps": {"s1rY3yNBh5ipHSDUaaVqJ": {"id": "s1rY3yNBh5ipHSDUaaVqJ", "metadata": {"createdAt": "2025-03-13T04:13:27.073Z", "updatedAt": "2025-03-13T04:13:27.073Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "item_output", "polyType": "VARIABLE", "type": "text", "value": "true"}]}, "variant": "setVariables"}, "xaUvKzawHph6fJbP8vTjG": {"id": "xaUvKzawHph6fJbP8vTjG", "metadata": {"createdAt": "2025-03-13T04:12:31.925Z", "updatedAt": "2025-03-13T04:12:31.925Z"}, "name": "Condition", "next": "", "properties": {"branches": [{"condition": {"lhs": "{{item_index}}", "operator": "<", "rhs": "3"}, "name": "If", "next": "s1rY3yNBh5ipHSDUaaVqJ"}]}, "variant": "condition"}}}, "inputs": {"itemValueType": "json", "itemVariableName": "item", "list": "{{form.VEOTahOeLj.answer}}", "resultVariableName": "filtered"}, "typePrimaryIdentifier": "iteratorFilter"}, "variant": "iterator"}}, "triggers": {"i4Y72MkiLnopCauYTZXs6": {"id": "i4Y72MkiLnopCauYTZXs6", "metadata": {"createdAt": "2025-03-13T04:10:57.060Z", "updatedAt": "2025-03-13T04:10:57.060Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Iterator Filter", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "iteratoraforeach": {"description": "", "id": "iteratoraforeach", "labels": [], "metadata": {"createdAt": "2025-03-14T00:40:25.298Z", "updatedAt": "2025-03-14T00:40:25.298Z"}, "name": "foreach", "start": "Jdp6LxciQWosjRBKT41pW", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.aqt"}], "steps": {"Jdp6LxciQWosjRBKT41pW": {"id": "Jdp6LxciQWosjRBKT41pW", "metadata": {"createdAt": "2025-03-14T00:40:50.530Z", "updatedAt": "2025-03-14T00:40:50.530Z"}, "name": "For each", "next": "", "properties": {"configuration": {"labels": [], "start": "oxvBCoWoUGfdgWMcrNaF4", "steps": {"oxvBCoWoUGfdgWMcrNaF4": {"id": "oxvBCoWoUGfdgWMcrNaF4", "metadata": {"createdAt": "2025-03-14T01:05:34.592Z", "updatedAt": "2025-03-14T01:05:34.592Z"}, "name": "Set variable(s)", "next": "", "properties": {"variables": [{"identifier": "item_output", "polyType": "VARIABLE", "type": "text", "value": "{{item.O1REtNdy50}}"}]}, "variant": "setVariables"}}}, "inputs": {"isReturn": "true", "itemVariableName": "item", "list": "{{form.VEOTahOeLj.answer}}", "resultVariableName": "output", "transformedValueType": "json"}, "typePrimaryIdentifier": "iteratorForEach"}, "variant": "iterator"}}, "triggers": {"nbKgpQl6GlMatQ4KShwGE": {"id": "nbKgpQl6GlMatQ4KShwGE", "metadata": {"createdAt": "2025-03-14T00:40:32.079Z", "updatedAt": "2025-03-14T00:40:32.079Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Iterator For Each", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}, "tablesa1": {"description": "A simple flow that sets values in a table", "id": "tablesa1", "labels": [], "metadata": {"createdAt": "2025-03-14T00:40:25.298Z", "updatedAt": "2025-03-14T00:40:25.298Z"}, "name": "tablesa1", "start": "a9QLLYeSf1fuyrp1vLqar8", "steps": {"a9QLLYeSf1fuyrp1vLqar8": {"id": "a9QLLYeSf1fuyrp1vLqar8", "metadata": {"createdAt": "2025-04-22T05:25:45.665Z", "updatedAt": "2025-04-22T05:25:45.665Z"}, "name": "Set variable(s)", "next": "step1", "properties": {"variables": [{"identifier": "variable_1", "polyType": "VARIABLE", "type": "json", "value": "[\"a\",\"b\",\"c\"]"}]}, "variant": "setVariables"}, "step1": {"id": "step1", "metadata": {"createdAt": "2025-03-24T02:55:30.952Z", "updatedAt": "2025-03-24T02:55:30.952Z"}, "name": "Set form answer", "properties": {"inputs": {"answer": "{{variable_1}}", "columnId": "O1REtNdy50", "formConfigurationId": "aqt", "formId": "{{form.id}}", "operation": "setColumn", "questionId": "VEOTahOeLj", "questionTypeWithOperation": "table", "rowSelection": ""}, "typePrimaryIdentifier": "setAnswer"}, "variant": "action"}}, "triggers": {"triggeratablesa1": {"id": "triggeratablesa1", "metadata": {"createdAt": "2025-03-14T00:40:32.079Z", "updatedAt": "2025-03-14T00:40:32.079Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Tables 1", "formConfigurationId": "aqt", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}}, "order": ["almX7K2eofUb8sx7Ve2jE", "a1872afilteraformulaabasic", "a1487asetatableaanswer", "a1487aflowainaflowasetatableaanswerainaiterator", "a1487asetavariableahelper", "a1487aflowsainaflowsasetavariable", "a1487aflowsainaflowsa3alevelsasetavariable", "tablesa1", "WRXHnwzRNXW46Rk5UjHi5", "RDE2zjfrLkqmDCfa3E4r4", "actiona1", "iteratoraforeach", "iteratorafilter", "iteratoraaggregate", "conditiona1", "a1106aE2Eacreateaformawhenafoundationacreated", "aa411a1078aE2Eaupdateaansweraandaraiseaalert", "a1079aE2Eamanualatriggerafromaformaflow", "a1107aE2Easetavariableastep", "E2Eamanualatriggerafromafoundationaflow", "af9Hjd4d7xd4N7vsCOv1Y", "a35KCpseEhiQyUytboCi1", "a57wFN4WFBLAcFdfZ0ZHE", "aWnxtfz6lEqoeNXN4URpJ", "7IbcClHBHX5lxmmaJVBeg", "aFgSMhUjZabwII3l01NZ6", "aXixKsXjK7dHBBSA6u6Gp", "aPZqzFUt5rIF8B2dZTap3", "2eWIo3r9W0GlPt0o3PIr0", "aFL3kpI953xWTdBO9GWg9", "ahMqR0GgQHdbmYVMYA9F7", "a1r8Uwe87ex93ZQIp45sm", "afdVCnedfO1dLNUR0laBi", "aiU5RJCqFTbz7bSvNYIiY", "a9lAj1wtT4zk8AfSg918f", "a9EUf1iYjsxVIVYxJXZdf", "asQV8EOOTL2drFyCBYt6A", "a0r1puSnYJY9k77SEIjgD", "aYkoyrEM9zcJYE0ycu2w1", "ap64lRaU6LpqDJfMZ9gNS", "aDkYOiGywKRq1o2djZQ3S", "aLztDqfJA2Y3XETTQyp6h", "a0KFaRuWArpdysxEfZppO", "aZhbyE3seSXCZoEZVvFk3", "avk8XNQp65ZcxIN7QDgwS", "aNcNypc8Cf5vwky2ei4DE", "apiEKfzR0nU0uBf89ee9T", "aN1o7Ch9U9ldfqdr0xXGb", "aDSiGV1CGoovzfRYm7lw0", "ab5w1WUyC4H4gjSz4s60U"]}, "forms": {"a0ZjxHUe06Mm0VZSwKfSE": {"content": [{"content": [{"content": [{"description": "", "id": "aYgCq4JRAI", "identifier": "DescribeYourFavouritePet", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Describe your favourite pet", "type": "text"}, {"description": "", "id": "atepF23vts", "identifier": "Pets", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "axhXIrL7sC", "identifier": "Name", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Name", "type": "text"}, {"description": "", "id": "az3aG1NYmI", "identifier": "Type", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "Cat", "value": "cat"}, {"label": "Dog", "value": "dog"}], "placeholder": ""}, "text": "Type", "type": "select"}]}, "text": "Pets", "type": "table"}], "id": "a1g7hQMcgn", "level": 2, "name": "General"}], "id": "aQAhBuAYIo", "level": 1, "name": "Pets"}], "description": "", "foundationId": "aQXFmGHm2w1LUvSVi9bjw", "id": "a0ZjxHUe06Mm0VZSwKfSE", "key": "SIMPLEFORM", "level": 0, "metadata": {"createdAt": "2025-06-02T06:08:52.773Z", "updatedAt": "2025-06-02T06:08:52.774Z"}, "name": "Pet Form", "seriesId": "years"}, "a1132aE2Eaform": {"content": [{"content": [{"content": [{"description": "", "id": "krM9xaFvah", "identifier": "TaxRate", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "Tax rate", "type": "number"}, {"description": "", "id": "O42Cd6cSeZ", "identifier": "TaxableIncome", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "Taxable income", "type": "number"}, {"description": "", "id": "acxx72aoNN", "identifier": "TaxPayable", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "Tax payable", "type": "number"}], "id": "a58abDgftqN", "level": 2, "name": "General"}], "id": "a8iFOZnmMlp", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "emp", "id": "a1132aE2Eaform", "key": "E2EFORM", "level": 0, "metadata": {"createdAt": "2024-12-19T22:46:59.388Z", "updatedAt": "2024-12-19T22:46:59.388Z"}, "name": "1132-E2E-form", "seriesId": "years"}, "a1872aformawithatable": {"content": [{"content": [{"content": [{"description": "", "id": "Io1TX1OJM2", "identifier": "Table", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "bihoQERn53", "identifier": "TableText", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "TableText", "type": "text"}, {"description": "", "id": "Psnak4wzY6", "identifier": "TableNumber", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "TableNumber", "type": "number"}]}, "text": "Table", "type": "table"}], "id": "za8cU4pPfq", "level": 2, "name": "General"}], "id": "RihXLevhKX", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "aQXFmGHm2w1LUvSVi9bjw", "id": "a1872aformawithatable", "key": "FORMWITHTABLE", "level": 0, "metadata": {"createdAt": "2025-04-15T06:03:26.544Z", "updatedAt": "2025-04-15T06:03:26.544Z"}, "name": "1872 1487 form with table", "seriesId": ""}, "ahFVQ2Mk6tZ0r1DKHICTG": {"allowMultiple": true, "content": [{"content": [{"content": [{"description": "", "id": "akpWti4Pft", "identifier": "Untitled", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Untitled", "type": "text"}], "id": "ad6zYrWFNO", "level": 2, "name": "General"}], "id": "a7e6dxwqoK", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "cli", "id": "ahFVQ2Mk6tZ0r1DKHICTG", "key": "CF", "level": 0, "metadata": {"createdAt": "2025-08-21T05:35:59.987Z", "updatedAt": "2025-08-21T05:35:59.987Z"}, "name": "Client Form", "seriesId": ""}, "al": {"content": [{"content": [{"content": [{"id": "a4000", "identifier": "Contacts", "properties": {"columns": [{"id": "aa5000", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"required": true}, "text": "Assignee Name", "type": "text"}, {"id": "aa5010", "identifier": "AssignmentPolicy", "properties": {"options": [{"label": "ST", "value": "ST"}, {"label": "LT", "value": "LT"}, {"label": "Perm", "value": "Perm"}], "required": true}, "text": "Assignment Policy", "type": "select"}, {"id": "aa5020", "identifier": "HomeCountry", "properties": {"required": true}, "text": "Home Country", "type": "text"}, {"id": "a5030", "identifier": "HostCountry", "properties": {"required": true}, "text": "Host Country", "type": "text"}, {"id": "aa5040", "identifier": "AssignmentStart", "properties": {"required": true}, "text": "Assignment Start", "type": "date"}, {"id": "aa5050", "identifier": "AssignmentEnd", "properties": {"required": true}, "text": "Assignment End", "type": "date"}]}, "text": "Contacts", "type": "table"}], "id": "aa3000", "level": 2, "name": "General"}], "id": "aa2000", "level": 1, "name": "Assignment Information"}, {"content": [{"content": [{"id": "aa4010", "identifier": "AuthorisationList", "properties": {"columns": [{"id": "aa5100", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"required": true}, "text": "Assignee Name", "type": "text"}, {"id": "aa5110", "identifier": "AssignmentPolicy", "properties": {"options": [{"label": "ST", "value": "ST"}, {"label": "LT", "value": "LT"}, {"label": "Perm", "value": "Perm"}], "required": true}, "text": "Assignment Policy", "type": "select"}, {"id": "aa5120", "identifier": "PolicyType", "properties": {"options": [{"label": "Short Term Assignment", "value": "Short Term Assignment"}, {"label": "Long Term Assignment", "value": "Long Term Assignment"}, {"label": "Permanent Transfer", "value": "Permanent Transfer"}, {"label": "Rotational Assignment", "value": "Rotational Assignment"}], "required": true}, "text": "Policy Type", "type": "select"}, {"id": "aa5130", "identifier": "AuthorisationTaxReturn", "properties": {"required": true}, "text": "Authorisation - Tax Return", "type": "boolean"}, {"id": "a5140", "identifier": "AuthorisationTaxBriefing", "properties": {"required": true}, "text": "Authorisation - Tax Briefing", "type": "boolean"}, {"id": "a5150", "identifier": "AuthorisationReason", "properties": {"required": true}, "text": "Authorisation Reason", "type": "text"}, {"id": "a5160", "identifier": "SpouseAuthorisation", "properties": {"required": true}, "text": "Spouse Authorisation", "type": "boolean"}]}, "text": "Authorisation List", "type": "table"}], "id": "aa3010", "level": 2, "name": "General"}], "id": "aa2010", "level": 1, "name": "Authorisation List"}], "description": "Authorisation List", "foundationId": "cli", "id": "al", "key": "AL", "level": 0, "metadata": {"createdAt": "2024-11-01T14:00:00Z", "updatedAt": "2024-11-01T14:00:00Z"}, "name": "Authorisation List", "seriesId": "years"}, "aqt": {"content": [{"content": [{"content": [{"description": "", "id": "plBrtlhc3W", "identifier": "Text1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "default", "maxLength": 10, "minLength": 2, "placeholder": "enter 2 to 10 characters"}, "text": "Text", "type": "text"}, {"description": "", "id": "np9GiUMruw", "identifier": "Number", "properties": {"allowReuseAcrossForms": false, "decimalPlaces": 2, "defaultValue": 5.11, "max": 10.12, "min": 0.05, "placeholder": "Enter a number between 0 and 10"}, "text": "Number", "type": "number"}, {"description": "", "id": "mHhmVG6X7z", "identifier": "Select", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "Option1", "value": "1"}, {"label": "Option2", "value": "2"}], "placeholder": "Select something"}, "text": "Select", "type": "select"}, {"description": "", "id": "JQIswpnlvm", "identifier": "Boolean", "properties": {"allowReuseAcrossForms": false, "falseText": "NAH!", "trueText": "YEAH!"}, "text": "Boolean", "type": "boolean"}, {"description": "", "id": "VEOTahOeLj", "identifier": "Table", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "O1REtNdy50", "identifier": "Tabletext", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "TableText1", "type": "text"}, {"description": "", "id": "eALIKF2hFv", "identifier": "Tablenumber", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "TableNumber1", "type": "number"}]}, "text": "Table", "type": "table"}], "id": "PtpLRE4nao", "level": 2, "name": "General"}, {"content": [{"description": "", "id": "r5QWKQYgKe", "identifier": "DateQuestion", "properties": {"allowReuseAcrossForms": false, "defaultValue": ""}, "text": "Date Question", "type": "date"}, {"description": "", "id": "apZe1DTF8B", "identifier": "ListQuestionSimple", "properties": {"allowReuseAcrossForms": false, "items": [{"description": "", "id": "a7uyqsInsG", "identifier": "apZe1DTF8B_items", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Untitled", "type": "text"}]}, "text": "List Question — simple", "type": "list"}], "id": "JpLnfpfkMB", "level": 2, "name": "Nested Section"}], "id": "mQYkqKd53A", "level": 1, "name": "Untitled"}, {"content": [{"content": [{"description": "", "id": "kM03gL62Rj", "identifier": "Number_1", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "Number", "type": "number"}, {"description": "", "id": "RaEwuSTmEd", "identifier": "Accounting", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "type": "accounting"}, "text": "Accounting", "type": "number"}, {"description": "", "id": "a8FSiagy4Sc", "identifier": "Percentage", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "type": "percentage"}, "text": "Percentage", "type": "number"}], "id": "C1vc81cdpH", "level": 2, "name": "General"}], "id": "BvBf8mal3H", "level": 1, "name": "Number Question ypes"}, {"content": [{"content": [{"description": "", "id": "t3Caagn0aZ", "identifier": "Date", "properties": {"allowReuseAcrossForms": false, "defaultValue": ""}, "text": "Date", "type": "date"}, {"description": "", "id": "p4war6g2xR", "identifier": "DateWithRange", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "max": "2030-02-01", "min": "2020-02-01"}, "text": "Date with range", "type": "date"}], "id": "z7KpgNcpZB", "level": 2, "name": "General"}], "id": "NuphYvYJMA", "level": 1, "name": "Date Question Types"}, {"content": [{"content": [{"description": "", "id": "akxUewEj3Q", "identifier": "Select_1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "", "value": "a"}, {"label": "", "value": "b"}, {"label": "", "value": "c"}], "placeholder": ""}, "text": "Select", "type": "select"}, {"description": "", "id": "IROlcRdJSA", "identifier": "MultiSelect", "properties": {"allowReuseAcrossForms": false, "isMultiSelect": true, "options": [{"label": "", "value": "a"}, {"label": "", "value": "b"}, {"label": "", "value": "c"}], "placeholder": ""}, "text": "Multi Select", "type": "multiSelect"}], "id": "fSpJzEdjAx", "level": 2, "name": "General"}], "id": "sUbaxCCmnh", "level": 1, "name": "Select Question Types"}, {"content": [{"content": [{"description": "", "id": "oDI9O0xAKo", "identifier": "Table_1", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "K2mO2ZztGH", "identifier": "Text", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Text", "type": "text"}, {"description": "", "id": "zTHSWvUxu5", "identifier": "Number", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "Number", "type": "number"}, {"description": "", "id": "UYpXBe7SkM", "identifier": "Numberaccounting", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "type": "accounting"}, "text": "Number(Accounting)", "type": "number"}, {"description": "", "id": "UuvVH4Ru70", "identifier": "Numberpercentage", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "Number(Percentage)", "type": "number"}, {"description": "", "id": "PBToKkez0K", "identifier": "Date", "properties": {"allowReuseAcrossForms": false, "defaultValue": ""}, "text": "Date", "type": "date"}, {"description": "", "id": "a7HBVtb1kLz", "identifier": "DateWithRange", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "max": "2030-02-02", "min": "2020-02-01"}, "text": "Date with range", "type": "date"}, {"description": "", "id": "YIFgX4PrMl", "identifier": "Select", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isMultiSelect": false, "options": [{"label": "a", "value": "a"}, {"label": "b", "value": "b"}, {"label": "c", "value": "c"}], "placeholder": ""}, "text": "Select", "type": "select"}, {"description": "", "id": "LXJKuURzuI", "identifier": "MultiSelect", "properties": {"allowReuseAcrossForms": false, "isMultiSelect": true, "options": [{"label": "d", "value": "d"}, {"label": "e", "value": "e"}, {"label": "f", "value": "f"}], "placeholder": ""}, "text": "Multi select", "type": "multiSelect"}, {"description": "", "id": "ckrCdRRoW3", "identifier": "Boolean", "properties": {"allowReuseAcrossForms": false}, "text": "Boolean", "type": "boolean"}]}, "text": "Table", "type": "table"}], "id": "dtEZZ5cilF", "level": 2, "name": "General"}], "id": "a8n6VJUh5x3", "level": 1, "name": "Table with all question types"}, {"content": [{"content": [{"description": "", "id": "a1sUhILky8o", "identifier": "Json", "properties": {"allowReuseAcrossForms": false, "items": [{"description": "", "id": "a7amxY9Mgw3", "identifier": "Text", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Text", "type": "text"}, {"description": "", "id": "v81aMab7he", "identifier": "Number", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "Number", "type": "number"}, {"description": "", "id": "Wx3v4jjJEx", "identifier": "Numberaccounting", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "Number(Accounting)", "type": "number"}, {"description": "", "id": "kRaRD19Evg", "identifier": "Numberpercentage", "properties": {"allowReuseAcrossForms": false, "placeholder": ""}, "text": "Number(Percentage)", "type": "number"}, {"description": "", "id": "smJF6zl1Dl", "identifier": "Date", "properties": {"allowReuseAcrossForms": false, "defaultValue": ""}, "text": "Date", "type": "date"}, {"description": "", "id": "Jr2yvUvarF", "identifier": "DateWithRange", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "max": "2030-02-01", "min": "2020-02-01"}, "text": "Date with range", "type": "date"}, {"description": "", "id": "a0MmBa2Z4m9", "identifier": "Boolean", "properties": {"allowReuseAcrossForms": false}, "text": "Boolean", "type": "boolean"}, {"description": "", "id": "apBNeHKT3P", "identifier": "ListInJson", "properties": {"allowReuseAcrossForms": false, "items": [{"description": "", "id": "aIOhkZt3QS", "identifier": "apBNeHKT3P_items", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Untitled", "type": "text"}]}, "text": "List in Json", "type": "list"}]}, "text": "JSON", "type": "json"}], "id": "uJPfbT8OKA", "level": 2, "name": "General"}], "id": "sFPQVPLMT8", "level": 1, "name": "JSON with all question types"}, {"content": [{"content": [{"description": "", "id": "VA7AgyzZST", "identifier": "Upload", "properties": {"allowReuseAcrossForms": false, "max": 10, "maxFileSizeMB": 100}, "text": "Upload", "type": "files"}], "id": "wgAeQ7agOE", "level": 2, "name": "General"}], "id": "HxyZLPxe6y", "level": 1, "name": "Upload Document"}], "description": "", "foundationId": "aQXFmGHm2w1LUvSVi9bjw", "id": "aqt", "key": "AQT", "level": 0, "metadata": {"createdAt": "2024-12-18T02:18:55.090Z", "updatedAt": "2024-12-18T02:18:55.090Z"}, "name": "All question types", "seriesId": ""}, "axaNyaPRfi9cn5mgA1rbK": {"content": [{"content": [{"content": [{"description": "", "id": "aC2mcecXgh", "identifier": "Tests", "properties": {"allowReuseAcrossForms": false, "columns": [{"description": "", "id": "arRas3fgoi", "identifier": "TestCase", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Test case", "type": "text"}, {"description": "", "id": "aDAq4RmV5s", "identifier": "ActualResult", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Actual Result", "type": "text"}, {"description": "", "id": "aashs2eBrn", "identifier": "Expected<PERSON><PERSON>ult", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "isTextArea": true, "placeholder": ""}, "text": "Expected Result", "type": "text"}, {"description": "", "id": "a1RySJBz6K", "identifier": "TestResult", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Test Result", "type": "text"}]}, "text": "Tests", "type": "table"}], "id": "afzQgcuZRX", "level": 2, "name": "General"}], "id": "agyF5CgniJ", "level": 1, "name": "<PERSON><PERSON><PERSON>"}], "description": "", "foundationId": "aQXFmGHm2w1LUvSVi9bjw", "id": "axaNyaPRfi9cn5mgA1rbK", "key": "AUTOMATEDTEST", "labels": ["a6wNWJx6kAWOgkYPD4wgi"], "level": 0, "metadata": {"createdAt": "2025-06-26T07:46:46.160Z", "updatedAt": "2025-06-26T07:46:46.160Z"}, "name": "Automated Test", "seriesId": ""}, "cp": {"content": [{"content": [{"content": [{"id": "a401", "identifier": "EmployerName", "properties": {"required": true}, "text": "Employer Name", "type": "text"}, {"id": "a402", "identifier": "UEN", "properties": {"required": true}, "text": "UEN", "type": "text"}, {"id": "a403", "identifier": "AddressOfEmployer", "properties": {"isTextArea": true, "required": true}, "text": "Address of Employer", "type": "text"}, {"id": "a410", "identifier": "Contacts", "properties": {"columns": [{"id": "a500", "identifier": "Name", "properties": {"required": true}, "text": "Name", "type": "text"}, {"id": "a501", "identifier": "Designation", "properties": {"required": true}, "text": "Designation", "type": "text"}, {"id": "a502", "identifier": "Email", "properties": {"required": true}, "text": "Email", "type": "text"}]}, "text": "Contacts", "type": "table"}], "id": "a300", "level": 2, "name": "General"}], "id": "a200", "level": 1, "name": "Basic Information"}, {"content": [{"content": [{"id": "a411", "identifier": "AuthorisationPolicy", "properties": {"columns": [{"id": "a504", "identifier": "AssignmentPolicy", "properties": {"options": [{"label": "ST", "value": "ST"}, {"label": "LT", "value": "LT"}, {"label": "Perm", "value": "Perm"}, {"label": "Rotator", "value": "Rotator"}], "required": true}, "text": "Assignment Policy", "type": "select"}, {"id": "a505", "identifier": "PolicyType", "properties": {"options": [{"label": "Short Term Assignment", "value": "Short Term Assignment"}, {"label": "Long Term Assignment", "value": "Long Term Assignment"}, {"label": "Permanent Transfer", "value": "Permanent Transfer"}, {"label": "Rotational Assignment", "value": "Rotational Assignment"}]}, "text": "Policy Type", "type": "select"}, {"id": "a506", "identifier": "Equalised", "properties": {"required": true}, "text": "Equalised", "type": "boolean"}, {"id": "a507", "identifier": "Authorisation", "properties": {"required": true}, "text": "Authorisation", "type": "text"}, {"id": "a508", "identifier": "SpouseAuthorisation", "properties": {"required": true}, "text": "Spouse Authorisation", "type": "text"}]}, "text": "Authorisation Policy", "type": "table"}], "id": "a301", "level": 2, "name": "General"}], "id": "a201", "level": 1, "name": "Authorisation"}, {"content": [{"content": [{"id": "a412", "identifier": "CompensationDataMapping", "properties": {"columns": [{"id": "a509", "identifier": "PayrollDescription", "properties": {"required": true}, "text": "Payroll Description", "type": "text"}, {"id": "a510", "identifier": "StandardMapping", "properties": {"required": true}, "text": "Standard Mapping", "type": "text"}, {"id": "a511", "identifier": "Equalised", "properties": {"falseText": "False", "required": true, "trueText": "True"}, "text": "Equalised", "type": "boolean"}, {"id": "a512", "identifier": "TaxProtected", "properties": {"falseText": "False", "required": true, "trueText": "True"}, "text": "Tax Protected", "type": "boolean"}, {"id": "a513", "identifier": "GrossUp", "properties": {"falseText": "False", "required": true, "trueText": "True"}, "text": "Gross Up", "type": "boolean"}]}, "text": "Compensation Data Mapping", "type": "table"}], "id": "a302", "level": 2, "name": "General"}], "id": "a202", "level": 1, "name": "Compensation Data"}], "description": "Client Positions", "foundationId": "cli", "id": "cp", "key": "CP", "level": 0, "metadata": {"createdAt": "2024-11-01T14:00:00Z", "updatedAt": "2024-11-01T14:00:00Z"}, "name": "Client Positions", "seriesId": "years"}, "ef": {"content": [{"content": [{"content": [{"description": "", "id": "bghZGAFyqa", "identifier": "Untitled", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": ""}, "text": "Untitled", "type": "text"}, {"description": "", "id": "T0zmaImvZa", "identifier": "TotalIncome", "properties": {"allowReuseAcrossForms": false, "placeholder": "", "type": "accounting"}, "text": "What's the employee's total yearly income?", "type": "number"}], "id": "HcaMTY38zl", "level": 2, "name": "General"}], "id": "gnYiNzjJQq", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "emp", "id": "ef", "key": "EF", "level": 0, "metadata": {"createdAt": "2024-12-19T22:46:59.388Z", "updatedAt": "2024-12-19T22:46:59.388Z"}, "name": "Employee Form", "seriesId": "years"}}, "foundations": {"entities": {"aQXFmGHm2w1LUvSVi9bjw": {"id": "aQXFmGHm2w1LUvSVi9bjw", "identifier": "aQXFmGHm2w1LUvSVi9bjw", "metadata": {"createdAt": "2024-12-15T22:15:29.945221Z", "updatedAt": "2024-12-15T22:15:29.945221Z"}, "name": "Workspace", "relationship": "OneToMany"}, "cli": {"description": "Client", "id": "cli", "identifier": "cli", "metadata": {"createdAt": "2024-12-15T22:15:29.922111Z", "updatedAt": "2024-12-15T22:15:29.922111Z"}, "name": "Client", "relationship": "OneToMany"}, "emp": {"description": "Employee", "id": "emp", "identifier": "emp", "metadata": {"createdAt": "2024-12-15T22:15:29.926127Z", "updatedAt": "2024-12-15T22:15:29.926127Z"}, "name": "Employee", "relationship": "OneToMany"}}, "order": ["aQXFmGHm2w1LUvSVi9bjw", "cli", "emp"]}, "id": 3, "key": "PXRSGPIT27", "labels": {"a6wNWJx6kAWOgkYPD4wgi": {"availableTo": ["formConfiguration", "flowConfiguration"], "color": "3", "description": "", "id": "a6wNWJx6kAWOgkYPD4wgi", "metadata": {"createdAt": "2025-07-16T04:05:00.269Z", "updatedAt": "2025-07-16T04:05:00.269Z"}, "name": "Automated Test"}, "aqH1fHtaj65vgqtNIRnb8": {"availableTo": ["formConfiguration", "flowConfiguration"], "color": "5", "description": "", "id": "aqH1fHtaj65vgqtNIRnb8", "metadata": {"createdAt": "2025-09-05T04:04:10.612Z", "updatedAt": "2025-09-05T04:04:17.596Z"}, "name": "Test Content Types"}}, "metadata": {"createdAt": "2024-12-15T22:15:29.931694Z", "updatedAt": "2024-12-15T22:15:29.931696Z"}, "name": "PXR SGPIT 27", "series": {"years": {"description": "", "id": "years", "intervals": {"entities": {"aa2024": {"id": "aa2024", "name": "2024"}, "aa2025": {"id": "aa2025", "name": "2025"}}, "order": ["aa2024", "aa2025"]}, "metadata": {"createdAt": "2024-12-15T22:16:39.908Z", "updatedAt": "2024-12-15T22:16:39.908Z"}, "name": "Years"}}, "type": "WORKSPACE_CONFIGURATION", "variables": {"a4EOmvtpNOZBFfYPIyjtB": {"description": "Please paste in the API KEY from Application Settings here", "id": "a4EOmvtpNOZBFfYPIyjtB", "isSecured": true, "metadata": {"createdAt": "2025-09-05T04:06:58.155Z", "updatedAt": "2025-09-05T04:06:58.155Z"}, "name": "INTERNAL_API_KEY"}, "aoweBWVxKEXKSviDlumCz": {"description": "", "id": "aoweBWVxKEXKSviDlumCz", "metadata": {"createdAt": "2025-09-05T04:03:13.089Z", "updatedAt": "2025-09-05T07:15:22.448Z"}, "name": "test_content_types_url_base", "value": "tools/debug/content-types"}, "auLCM8L6jOkZfKIneuDcf": {"description": "localhost, ot.dev etc", "id": "auLCM8L6jOkZfKIneuDcf", "metadata": {"createdAt": "2025-07-25T00:25:46.806Z", "updatedAt": "2025-07-25T00:29:14.127Z"}, "name": "CURRENT_HOST", "value": "http://localhost:8000"}}}