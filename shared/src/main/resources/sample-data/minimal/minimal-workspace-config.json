{"description": "A minimal workspace configuration", "errors": [], "flows": {"entities": {"aNQirMa8KgWx3NbM97kw9": {"description": "", "endingVariables": [], "id": "aNQirMa8KgWx3NbM97kw9", "labels": [], "metadata": {"createdAt": "2025-09-03T01:45:59.447Z", "updatedAt": "2025-09-03T01:45:59.447Z"}, "name": "Create foundation, form", "start": "a31qLZ96R8TLNlXpponLm", "startingVariables": [{"identifier": "form", "properties": {"required": true}, "type": "form.azmStQ2ZsJTUyw0cQkj4z"}], "status": "active", "steps": {"a31qLZ96R8TLNlXpponLm": {"id": "a31qLZ96R8TLNlXpponLm", "metadata": {"createdAt": "2025-09-03T01:47:44.954Z", "updatedAt": "2025-09-03T01:47:44.954Z"}, "name": "Set variable(s)", "next": "aA4GPZklnwCWxUne1MeMZ", "properties": {"variables": [{"identifier": "NOW", "type": "text", "value": "$NOW()"}, {"identifier": "newFoundation", "type": "text", "value": "$CONCAT(\"FND\", {{NOW}})"}, {"identifier": "newForm", "type": "text", "value": "$CONCAT(\"FRM\", {{NOW}})"}]}, "variant": "setVariables"}, "aA4GPZklnwCWxUne1MeMZ": {"id": "aA4GPZklnwCWxUne1MeMZ", "metadata": {"createdAt": "2025-09-03T01:46:38.256Z", "updatedAt": "2025-09-03T01:46:38.256Z"}, "name": "Select or create a foundation", "next": "ax2RPGiBXXUcgTvCSLw5J", "properties": {"inputs": {"foundationConfigurationId": "a4I9Bu20kpK6ToLM0M238", "foundationVariableName": "foundation__step_aA4GPZklnwCWxUne1MeMZ", "key": "{{newFoundation}}", "name": "{{newFoundation}}", "parentId": "{{form.foundation.id}}"}, "typePrimaryIdentifier": "selectOrCreateFoundation"}, "variant": "action"}, "ax2RPGiBXXUcgTvCSLw5J": {"id": "ax2RPGiBXXUcgTvCSLw5J", "metadata": {"createdAt": "2025-09-03T01:50:59.521Z", "updatedAt": "2025-09-03T01:50:59.521Z"}, "name": "Select or create a form", "next": "", "properties": {"inputs": {"formConfigurationId": "acHzKilsg1WvP1cNpHcKQ", "formVariableName": "form__step_ax2RPGiBXXUcgTvCSLw5J", "foundationId": "{{foundation__step_aA4GPZklnwCWxUne1MeMZ.id}}", "intervalId": "aMCOWsXsf7hnM33Vg5k9w"}, "typePrimaryIdentifier": "selectOrCreateForm"}, "variant": "action"}}, "triggers": {"av8jG3Rh9tzo9FPY8Huaq": {"id": "av8jG3Rh9tzo9FPY8Huaq", "metadata": {"createdAt": "2025-09-03T01:46:03.237Z", "updatedAt": "2025-09-03T01:46:03.237Z"}, "name": "Manually trigger from a form", "next": "", "properties": {"inputs": {"buttonLabel": "Create foundation, form, answer", "formConfigurationId": "azmStQ2ZsJTUyw0cQkj4z", "formVariableName": "form"}, "typePrimaryIdentifier": "manualTriggerFromForm"}, "variant": "trigger"}}}}, "order": ["aNQirMa8KgWx3NbM97kw9"]}, "forms": {"aMRAshsqJRPzNJPRSX4w4": {"allowMultiple": false, "content": [{"content": [{"content": [{"description": "", "id": "aqlOsTP8eB", "identifier": "CompanyQuestion3", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Company Question 3", "type": "text"}], "id": "aX7oRlGkMW", "level": 2, "name": "General"}], "id": "auEdxszAnP", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "a4I9Bu20kpK6ToLM0M238", "id": "aMRAshsqJRPzNJPRSX4w4", "key": "CF2", "labels": [], "level": 0, "metadata": {"createdAt": "2025-09-03T01:45:14.392Z", "updatedAt": "2025-09-03T01:45:14.392Z"}, "name": "Company Form no multiples", "seriesId": ""}, "aZeOjMUey2AnAaAkWlcxZ": {"allowMultiple": true, "content": [], "description": "", "foundationId": "a4I9Bu20kpK6ToLM0M238", "id": "aZeOjMUey2AnAaAkWlcxZ", "key": "CF1", "labels": [], "level": 0, "metadata": {"createdAt": "2025-09-03T01:44:20.424Z", "updatedAt": "2025-09-03T01:44:20.424Z"}, "name": "Company Form without series", "seriesId": ""}, "acHzKilsg1WvP1cNpHcKQ": {"allowMultiple": false, "content": [{"content": [{"content": [{"description": "", "id": "az40NCemWa", "identifier": "CompanyQuestion1", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Company Question 1", "type": "text"}], "id": "aGqlqHzYyA", "level": 2, "name": "General"}], "id": "aYiF5xQ2mP", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "a4I9Bu20kpK6ToLM0M238", "id": "acHzKilsg1WvP1cNpHcKQ", "key": "CF", "labels": [], "level": 0, "metadata": {"createdAt": "2025-09-03T01:43:35.184Z", "updatedAt": "2025-09-03T01:43:35.184Z"}, "name": "Company Form with series", "seriesId": "asXPYB7tZ6IxUgIZMuu2N"}, "azmStQ2ZsJTUyw0cQkj4z": {"allowMultiple": true, "content": [{"content": [{"content": [{"description": "", "id": "aIoM7aR6U7", "identifier": "WorkspaceQuestion", "properties": {"allowReuseAcrossForms": false, "defaultValue": "", "placeholder": "", "required": false}, "text": "Workspace Question", "type": "text"}], "id": "aNnWh1wsn7", "level": 2, "name": "General"}], "id": "aOgJNfFk5H", "level": 1, "name": "Untitled"}], "description": "", "foundationId": "aKFtqODkPiNshJ7zLrMxa", "id": "azmStQ2ZsJTUyw0cQkj4z", "key": "WF", "labels": [], "level": 0, "metadata": {"createdAt": "2025-09-03T01:39:40.999Z", "updatedAt": "2025-09-03T01:39:40.999Z"}, "name": "Workspace Form", "seriesId": ""}}, "foundations": {"entities": {"a4I9Bu20kpK6ToLM0M238": {"description": "", "disableAutoSuggestKey": false, "id": "a4I9Bu20kpK6ToLM0M238", "identifier": "Company", "metadata": {"createdAt": "2025-09-03T01:39:03.099Z", "updatedAt": "2025-09-03T01:39:03.099Z"}, "name": "Company", "relationship": "OneToMany"}, "aKFtqODkPiNshJ7zLrMxa": {"id": "aKFtqODkPiNshJ7zLrMxa", "identifier": "Workspace", "metadata": {"createdAt": "2025-09-03T01:36:15.889696Z", "updatedAt": "2025-09-03T01:36:15.889696Z"}, "name": "Workspace", "relationship": "OneToMany"}}, "order": ["aKFtqODkPiNshJ7zLrMxa", "a4I9Bu20kpK6ToLM0M238"]}, "id": 10, "key": "TE", "labels": {}, "metadata": {"createdAt": "2025-09-03T01:36:15.889905Z", "updatedAt": "2025-09-03T01:36:15.889907Z"}, "name": "Test", "series": {"asXPYB7tZ6IxUgIZMuu2N": {"description": "", "id": "asXPYB7tZ6IxUgIZMuu2N", "intervals": {"entities": {"a54m95KNvN9UiViml6KSu": {"id": "a54m95KNvN9UiViml6KSu", "name": "2021"}, "aMCOWsXsf7hnM33Vg5k9w": {"id": "aMCOWsXsf7hnM33Vg5k9w", "name": "2020"}, "ajjvf19QswFkdITz7XZph": {"id": "ajjvf19QswFkdITz7XZph", "name": "2022"}, "ak0yNPwxaHycTskLUlvqU": {"id": "ak0yNPwxaHycTskLUlvqU", "name": "2025"}, "alE3w5fv6iMTSFRXxNZtU": {"id": "alE3w5fv6iMTSFRXxNZtU", "name": "2023"}, "atBeIpmpLT8rzb5nZs5mi": {"id": "atBeIpmpLT8rzb5nZs5mi", "name": "2024"}}, "order": ["aMCOWsXsf7hnM33Vg5k9w", "a54m95KNvN9UiViml6KSu", "ajjvf19QswFkdITz7XZph", "alE3w5fv6iMTSFRXxNZtU", "atBeIpmpLT8rzb5nZs5mi", "ak0yNPwxaHycTskLUlvqU"]}, "metadata": {"createdAt": "2025-09-03T01:42:29.004Z", "updatedAt": "2025-09-03T01:42:29.004Z"}, "name": "Years"}}, "type": "WORKSPACE_CONFIGURATION", "variables": {}}