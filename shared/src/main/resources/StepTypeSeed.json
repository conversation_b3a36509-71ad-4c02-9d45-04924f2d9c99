[{"primaryIdentifier": "ManualTrigger", "type": "trigger", "name": "Manual trigger", "description": "A trigger that is pressed manually from the user", "properties": {"icon": {"name": "play_circle"}, "isLocked": true, "isHidden": false, "deprecated": {"replacement": "triggerFromAnotherFlow", "deprecatedAt": "2025-03-01T00:00:00Z"}, "configuration": {"userConfiguredStartingVariables": true, "content": [], "subscribeTo": {}}}}, {"primaryIdentifier": "manualTriggerFromFoundation", "name": "Manually trigger from a foundation", "description": "Trigger the flow manually from a button on a foundation in collection", "type": "trigger", "properties": {"icon": {"name": "arrow_selector_tool"}, "category": {"name": "Foundations", "icon": {"name": "roofing"}, "order": 2}, "isLocked": true, "isHidden": false, "configuration": {"userConfiguredStartingVariables": false, "content": [{"text": "Foundation Level to appear on", "type": "select", "identifier": "foundationConfigurationId", "properties": {"required": true, "dynamicOptions": {"tag": "foundationConfigurationId"}}}, {"text": "Button label", "type": "text", "identifier": "buttonLabel", "properties": {"required": true}}, {"text": "Foundation variable name", "type": "text", "identifier": "foundationVariableName", "properties": {"required": false, "defaultValue": "foundation", "regex": "^[a-zA-Z0-9_]*$"}}], "subscribeTo": {"START_flow_manually_from_foundation": {"key": "START_flow_manually_from_foundation", "condition": {"AND": [{"lhs": "{{thisStep.buttonLabel}}", "operator": "=", "rhs": "{{event.eventProperties.buttonLabel}}"}, {"lhs": "{{event.eventProperties.foundation.foundationConfiguration.id}}", "operator": "=", "rhs": "{{thisStep.foundationConfigurationId}}"}]}, "variableMappings": [{"type": "foundation.{{thisStep.foundationConfigurationId}}", "identifier": "{{thisStep.foundationVariableName}}", "value": "{{event.eventProperties.foundation.id}}"}]}}}}}, {"primaryIdentifier": "triggerFromAnotherFlow", "name": "Triggered by another flow", "description": "Trigger the flow from inside another flow", "type": "trigger", "properties": {"category": {"name": "Flows", "icon": {"name": "family_history"}, "order": 3}, "icon": {"name": "conversion_path"}, "isLocked": true, "isHidden": false, "configuration": {"userConfiguredStartingVariables": true, "content": [], "subscribeTo": {}}}}, {"primaryIdentifier": "receiveIncomingWebhookForFlow", "name": "When this flow's webhook is received", "description": "Triggers the flow when a specific webhook received", "type": "trigger", "properties": {"icon": {"name": "webhook"}, "category": {"name": "API", "icon": {"name": "code"}}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Payload variable name", "type": "text", "identifier": "payloadVariableName", "properties": {"type": "text", "required": false, "defaultValue": "incomingPayload", "regex": "^[a-zA-Z0-9_]*$"}}, {"text": "Expected Payload", "type": "schema", "identifier": "expectedPayload", "properties": {"type": "json", "defaultValue": {"sample": {}, "schema": {"id": "expectedPayloadSchema", "identifier": "expectedPayloadSchema", "text": "JSON", "type": "json", "properties": {"items": []}}}}}], "subscribeTo": {"RECEIVE_incoming_webhook_flow": {"key": "RECEIVE_incoming_webhook_flow", "condition": {"AND": [{"lhs": "{{event.eventProperties.flowConfigurationId}}", "operator": "=", "rhs": "{{global.flowConfigurationId}}"}]}, "variableMappings": [{"type": "{{thisStep.expectedPayload.schema.type}}", "identifier": "{{thisStep.payloadVariableName}}", "value": "{{event.eventProperties.payload}}", "properties": {"schema": "{{thisStep.expectedPayload.schema}}", "items": "{{thisStep.expectedPayload.schema.properties.items}}"}}]}}, "documentation": [{"title": "Instructions", "richText": [{"type": "paragraph", "text": "To trigger this flow, you need to send a POST request to the following URL:"}, {"type": "codeBlock", "text": "{{global.tenantOrigin}}/ai/api/webhooks/workspaces/{{global.workspaceId}}/flows/{{global.flowConfigurationId}}"}]}]}}}, {"primaryIdentifier": "receiveIncomingWebhookForWorkspace", "name": "When this workspace's webhook is received", "description": "Triggers the flow when a specific workspace webhook received", "type": "trigger", "properties": {"icon": {"name": "webhook"}, "category": {"name": "API", "icon": {"name": "code"}}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Trigger unique identifier", "type": "text", "identifier": "triggerIdentifier", "properties": {"required": false, "defaultValue": "", "regex": "^[a-zA-Z0-9_]*$"}}, {"text": "Payload variable name", "type": "text", "identifier": "payloadVariableName", "properties": {"required": false, "defaultValue": "incomingPayload", "regex": "^[a-zA-Z0-9_]*$"}}, {"text": "Expected Payload", "type": "schema", "identifier": "expectedPayload", "properties": {"type": "json", "defaultValue": {"sample": {}, "schema": {"id": "expectedPayloadSchema", "identifier": "expectedPayloadSchema", "text": "JSON", "type": "json", "properties": {"items": []}}}}}], "subscribeTo": {"RECEIVE_incoming_webhook_workspace": {"key": "RECEIVE_incoming_webhook_workspace", "condition": {"AND": [{"lhs": "{{event.workspaceId}}", "operator": "=", "rhs": "{{global.workspaceId}}"}, {"lhs": "{{event.eventProperties.triggerIdentifier}}", "operator": "=", "rhs": "{{thisStep.triggerIdentifier}}"}]}, "variableMappings": [{"type": "{{thisStep.expectedPayload.schema.type}}", "identifier": "{{thisStep.payloadVariableName}}", "value": "{{event.eventProperties.payload}}", "properties": {"schema": "{{thisStep.expectedPayload.schema}}", "items": "{{thisStep.expectedPayload.schema.properties.items}}"}}]}}, "documentation": [{"title": "Instructions", "richText": [{"type": "paragraph", "text": "To trigger this flow, you need to send a POST request to the following URL:"}, {"type": "codeBlock", "text": "{{global.tenantOrigin}}/ai/api/webhooks/workspaces/{{global.workspaceId}}", "hidden": {"lhs": "{{thisStep.triggerIdentifier}}", "operator": "!=", "rhs": ""}}, {"type": "codeBlock", "text": "{{global.tenantOrigin}}/ai/api/webhooks/workspaces/{{global.workspaceId}}?tid={{thisStep.triggerIdentifier}}", "hidden": {"lhs": "{{thisStep.triggerIdentifier}}", "operator": "=", "rhs": ""}}]}]}}}, {"primaryIdentifier": "manualTriggerFromForm", "name": "Manually trigger from a form", "description": "Trigger the flow manually from a button on a form in collection", "type": "trigger", "properties": {"icon": {"name": "arrow_selector_tool"}, "category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "isLocked": true, "isHidden": false, "configuration": {"userConfiguredStartingVariables": false, "content": [{"text": "Form configuration to appear on", "type": "select", "identifier": "formConfigurationId", "properties": {"required": true, "dynamicOptions": {"tag": "formConfigurationId"}}}, {"text": "Button label", "type": "text", "identifier": "buttonLabel", "properties": {"required": true}}, {"text": "Form variable name", "type": "text", "identifier": "formVariableName", "properties": {"required": false, "regex": "^[a-zA-Z0-9_]*$", "defaultValue": "form"}}], "subscribeTo": {"START_flow_manually_from_form": {"key": "START_flow_manually_from_form", "condition": {"AND": [{"lhs": "{{thisStep.buttonLabel}}", "operator": "=", "rhs": "{{event.eventProperties.buttonLabel}}"}, {"lhs": "{{event.eventProperties.form.formConfiguration.id}}", "operator": "=", "rhs": "{{thisStep.formConfigurationId}}"}]}, "variableMappings": [{"type": "form.{{thisStep.formConfigurationId}}", "identifier": "{{thisStep.formVariableName}}", "value": "{{event.eventProperties.form.id}}"}]}}}}}, {"primaryIdentifier": "answerChanged", "name": "When answer(s) change in a form", "description": "Trigger the flow when answer(s) are changed", "type": "trigger", "properties": {"icon": {"name": "edit_note"}, "category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Form configuration", "type": "select", "identifier": "formConfigurationId", "properties": {"required": true, "dynamicOptions": {"tag": "formConfigurationId"}}}, {"text": "Question(s)", "type": "multiSelect", "identifier": "questionIds", "properties": {"required": true, "dynamicOptions": {"tag": "formConfigurationId.questionId", "body": {"formConfigurationId": "{{thisStep.formConfigurationId}}"}}}}, {"text": "Form variable name", "type": "text", "identifier": "formVariableName", "properties": {"required": false, "regex": "^[a-zA-Z0-9_]*$", "defaultValue": "form"}}, {"text": "Changes variable name", "type": "text", "identifier": "changesVariableName", "properties": {"required": false, "regex": "^[a-zA-Z0-9_]*$", "defaultValue": "changes"}}], "subscribeTo": {"UPDATE_collection_form_answer": {"key": "UPDATE_collection_form_answer", "condition": {"AND": [{"lhs": "{{event.eventProperties.form.formConfiguration.id}}", "operator": "=", "rhs": "{{thisStep.formConfigurationId}}"}, {"lhs": "$count($filter({{thisStep.questionIds}}, function($v) { $v in {{event.eventProperties.changes.questionIds}} })) > 0", "operator": "=", "rhs": "true"}]}, "variableMappings": [{"type": "form.{{thisStep.formConfigurationId}}", "identifier": "{{thisStep.formVariableName}}", "value": "{{event.eventProperties.form.id}}"}, {"type": "json", "identifier": "{{thisStep.changesVariableName}}", "value": "{{event.eventProperties.changes}}", "properties": {"items": [{"type": "list", "identifier": "questionIds", "properties": {"items": [{"type": "text", "identifier": "questionIds_item"}]}}]}}]}}}}}, {"primaryIdentifier": "foundationCreated", "name": "When a foundation is created", "description": "A trigger that executes when a foundation is created", "type": "trigger", "properties": {"icon": {"name": "add"}, "category": {"name": "Foundations", "icon": {"name": "roofing"}, "order": 2}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Foundation Level", "type": "select", "identifier": "foundationConfigurationId", "properties": {"required": true, "dynamicOptions": {"tag": "foundationConfigurationId"}}}, {"text": "Foundation variable name", "type": "text", "identifier": "foundationVariableName", "properties": {"required": false, "defaultValue": "foundation", "regex": "^[a-zA-Z0-9_]*$"}}], "subscribeTo": {"CREATE_collection_foundation": {"key": "CREATE_collection_foundation", "condition": {"lhs": "{{event.eventProperties.foundation.foundationConfiguration.id}}", "operator": "=", "rhs": "{{thisStep.foundationConfigurationId}}"}, "variableMappings": [{"type": "foundation.{{thisStep.foundationConfigurationId}}", "identifier": "{{thisStep.foundationVariableName}}", "value": "{{event.eventProperties.foundation.id}}"}]}}}}}, {"primaryIdentifier": "formCreated", "name": "When a form is created", "description": "A trigger that executes when a form is created", "type": "trigger", "properties": {"icon": {"name": "add"}, "category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Form configuration", "type": "select", "identifier": "formConfigurationId", "properties": {"required": true, "dynamicOptions": {"tag": "formConfigurationId"}}}, {"text": "Form variable name", "type": "text", "identifier": "formVariableName", "properties": {"required": false, "regex": "^[a-zA-Z0-9_]*$", "defaultValue": "form"}}], "subscribeTo": {"CREATE_collection_form": {"key": "CREATE_collection_form", "condition": {"lhs": "{{event.eventProperties.form.formConfiguration.id}}", "operator": "=", "rhs": "{{thisStep.formConfigurationId}}"}, "variableMappings": [{"type": "form.{{thisStep.formConfigurationId}}", "identifier": "{{thisStep.formVariableName}}", "value": "{{event.eventProperties.form.id}}"}]}}}}}, {"primaryIdentifier": "createForm", "name": "Create a form", "description": "Create a form for a foundation", "type": "action", "properties": {"icon": {"name": "add"}, "category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "isLocked": true, "isHidden": false, "deprecated": {"replacement": "selectOrCreateForm", "deprecatedAt": "2025-06-20T00:00:00Z"}, "configuration": {"content": [{"text": "Foundation ID", "type": "variable", "identifier": "foundationId", "properties": {"type": "number", "required": true}}, {"text": "Form configuration ID", "type": "variable", "identifier": "formConfigurationId", "properties": {"required": true, "type": "select", "properties": {"dynamicOptions": {"tag": "formConfigurationId"}}}}, {"text": "Series interval ID", "type": "variable", "identifier": "intervalId", "properties": {"type": "select", "required": false, "defaultValue": "", "properties": {"dynamicOptions": {"tag": "intervalConfigurationId", "body": {"formConfigurationId": "{{thisStep.formConfigurationId}}"}}}}}, {"text": "Form variable name", "type": "text", "identifier": "formVariableName", "properties": {"required": false, "regex": "^[a-zA-Z0-9_]*$", "defaultValue": "form__step_{{thisStep.id}}"}}], "apiCall": {"url": "/ai/api/workspaces/{{global.workspaceId}}/forms", "internal": true, "method": "POST", "body": {"foundationId": "{{thisStep.foundationId}}", "formConfigurationId": "{{thisStep.formConfigurationId}}", "intervalId": "{{thisStep.intervalId}}"}, "bodyProperties": {"intervalId": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"type": "form.minimal", "identifier": "form"}]}}}, "variableMappings": [{"type": "form.{{thisStep.formConfigurationId}}", "identifier": "{{thisStep.formVariableName}}", "value": "{{thisStep.response.form.id}}"}]}}}, {"primaryIdentifier": "createFoundation", "name": "Create a foundation", "description": "", "type": "action", "properties": {"icon": {"name": "add"}, "category": {"name": "Foundations", "icon": {"name": "roofing"}, "order": 2}, "isLocked": true, "isHidden": false, "deprecated": {"replacement": "selectOrCreateFoundation", "deprecatedAt": "2025-06-20T00:00:00Z"}, "configuration": {"content": [{"text": "Foundation Level ID", "type": "variable", "identifier": "foundationConfigurationId", "properties": {"required": true, "type": "select", "properties": {"dynamicOptions": {"tag": "foundationConfigurationId"}}}}, {"text": "Name", "type": "variable", "identifier": "name", "properties": {"type": "text", "required": true}}, {"text": "Key", "type": "variable", "identifier": "key", "properties": {"type": "text", "required": true, "properties": {"regex": "^([A-Za-z0-9{][A-Za-z0-9{}_.]*)$"}}}, {"text": "Parent foundation ID", "type": "variable", "identifier": "parentId", "properties": {"type": "number", "description": "Required only if the foundation needs to be created", "required": true}}, {"text": "Foundation variable name", "type": "text", "identifier": "foundationVariableName", "properties": {"required": false, "defaultValue": "foundation__step_{{thisStep.id}}", "regex": "^[a-zA-Z0-9_]*$"}}], "apiCall": {"url": "/ai/api/workspaces/{{global.workspaceId}}/foundations", "internal": true, "method": "POST", "body": {"name": "{{thisStep.name}}", "key": "{{thisStep.key}}", "workspaceId": "{{global.workspaceId}}", "foundationConfigurationId": "{{thisStep.foundationConfigurationId}}", "parentId": "{{thisStep.parentId}}"}, "response": {"type": "json", "properties": {"items": [{"type": "foundation.minimal", "identifier": "foundation"}]}}}, "variableMappings": [{"type": "foundation.{{thisStep.foundationConfigurationId}}", "identifier": "{{thisStep.foundationVariableName}}", "value": "{{thisStep.response.foundation.id}}"}]}}}, {"primaryIdentifier": "selectForm", "name": "Select form(s)", "description": "", "type": "action", "properties": {"icon": {"name": "search"}, "category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Foundation ID", "type": "variable", "identifier": "foundationId", "properties": {"type": "number", "required": true}}, {"text": "Form configuration ID", "type": "variable", "identifier": "formConfigurationId", "properties": {"type": "select", "required": true, "properties": {"dynamicOptions": {"tag": "formConfigurationId"}}}}, {"text": "Selection", "type": "select", "identifier": "selection", "properties": {"options": [{"value": "single", "label": "Single", "description": "Most recently created form, if multiple"}, {"value": "multiple", "label": "Multiple", "description": "All matching forms"}], "description": "Choose whether to select the latest form or all forms (depending on the availability)", "defaultValue": "single"}}, {"text": "Interval", "type": "variable", "identifier": "intervalId", "properties": {"type": "select", "required": false, "defaultValue": "", "properties": {"dynamicOptions": {"tag": "intervalConfigurationId", "body": {"formConfigurationId": "{{thisStep.formConfigurationId}}"}}}}}, {"text": "Form variable name", "type": "text", "identifier": "formVariableName", "properties": {"required": false, "regex": "^[a-zA-Z0-9_]*$", "defaultValue": "form__step_{{thisStep.id}}"}}, {"text": "Continue flow if form is not found", "type": "select", "identifier": "continueFlowIfNotFound", "properties": {"required": true, "uiControls": {"displayAs": "radioGroup"}, "options": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}], "defaultValue": "true"}}], "apiCall": {"url": "/ai/api/forms/select-many", "internal": true, "method": "GET", "body": {"workspaceId": "{{global.workspaceId}}", "foundationId": "{{thisStep.foundationId}}", "formConfigurationId": "{{thisStep.formConfigurationId}}", "intervalIdOrName": "{{thisStep.intervalId}}", "allowNull": "{{thisStep.continueFlowIfNotFound}}"}, "bodyProperties": {"intervalIdOrName": {"nullable": true}, "allowNull": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"type": "list", "identifier": "forms", "properties": {"items": [{"type": "form.minimal", "identifier": "form"}]}}]}}}, "variableMappings": [{"type": "form.{{thisStep.formConfigurationId}}", "identifier": "{{thisStep.formVariableName}}", "value": "{{thisStep.response.forms[-1].id}}", "properties": {"hidden": {"lhs": "{{thisStep.selection}}", "operator": "=", "rhs": "multiple"}}}, {"type": "list", "identifier": "{{thisStep.formVariableName}}", "value": "{{thisStep.response.forms[*].id}}", "properties": {"items": [{"type": "form.{{thisStep.formConfigurationId}}", "identifier": "{{thisStep.formVariableName}}_item"}], "hidden": {"lhs": "{{thisStep.selection}}", "operator": "!=", "rhs": "multiple"}, "valueIsExpression": true}}]}}}, {"primaryIdentifier": "selectOrCreateForm", "name": "Select or create a form", "description": "", "type": "action", "properties": {"icon": {"name": "add"}, "category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Foundation ID", "type": "variable", "identifier": "foundationId", "properties": {"type": "number", "required": true}}, {"text": "Form configuration ID", "type": "variable", "identifier": "formConfigurationId", "properties": {"type": "select", "required": true, "properties": {"dynamicOptions": {"tag": "formConfigurationId"}}}}, {"text": "Interval", "type": "variable", "identifier": "intervalId", "properties": {"type": "select", "required": false, "defaultValue": "", "properties": {"dynamicOptions": {"tag": "intervalConfigurationId", "body": {"formConfigurationId": "{{thisStep.formConfigurationId}}"}}}}}, {"text": "Form variable name", "type": "text", "identifier": "formVariableName", "properties": {"required": false, "regex": "^[a-zA-Z0-9_]*$", "defaultValue": "form__step_{{thisStep.id}}"}}], "apiCall": {"url": "/ai/api/forms/select-or-create", "internal": true, "method": "POST", "body": {"workspaceId": "{{global.workspaceId}}", "foundationId": "{{thisStep.foundationId}}", "formConfigurationId": "{{thisStep.formConfigurationId}}", "intervalIdOrName": "{{thisStep.intervalId}}", "allowNull": "true", "createIfNotExists": "true"}, "bodyProperties": {"intervalId": {"nullable": true}, "allowNull": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"type": "form.minimal", "identifier": "form"}]}}}, "variableMappings": [{"type": "form.{{thisStep.formConfigurationId}}", "identifier": "{{thisStep.formVariableName}}", "value": "{{thisStep.response.form.id}}"}]}}}, {"primaryIdentifier": "updateForm", "name": "Update a form", "description": "", "type": "action", "properties": {"icon": {"name": "autorenew"}, "category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Form ID", "type": "variable", "identifier": "formId", "properties": {"type": "number", "required": true}}, {"text": "Form configuration ID (not updatable)", "type": "variable", "identifier": "formConfigurationId", "properties": {"type": "select", "required": true, "properties": {"dynamicOptions": {"tag": "formConfigurationId"}}}}, {"text": "Properties", "type": "table", "identifier": "properties", "properties": {"uiControls": {"displayAs": "tableList"}, "columns": [{"text": "Key", "identifier": "key", "type": "variable", "properties": {"type": "text", "defaultValue": ""}}, {"text": "Value", "identifier": "value", "type": "variable", "properties": {"type": "text", "defaultValue": ""}}]}}, {"text": "Form variable name", "type": "text", "identifier": "formVariableName", "properties": {"required": false, "regex": "^[a-zA-Z0-9_]*$", "defaultValue": "form__step_{{thisStep.id}}"}}], "apiCall": {"url": "/ai/api/forms/{{thisStep.formId}}/properties", "internal": true, "method": "PUT", "body": {"properties": "$reduce({{thisStep.properties}}, function($acc, $property) { $merge([$acc, $not($keys($property)) or $property.key = \"\" ? {} : { $property.key: $property.value }]) }, {})", "workspaceId": "{{global.workspaceId}}"}, "bodyProperties": {"properties": {"isExpression": true}}, "response": {"type": "json", "properties": {"items": [{"type": "form.minimal", "identifier": "form"}]}}}, "variableMappings": [{"type": "form.{{thisStep.formConfigurationId}}", "identifier": "{{thisStep.formVariableName}}", "value": "{{thisStep.response.form.id}}"}]}}}, {"primaryIdentifier": "selectFoundation", "name": "Select foundation(s)", "description": "", "type": "action", "properties": {"icon": {"name": "search"}, "category": {"name": "Foundations", "icon": {"name": "roofing"}, "order": 2}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Foundation Level ID", "type": "variable", "identifier": "foundationConfigurationId", "properties": {"required": true, "type": "select", "properties": {"dynamicOptions": {"tag": "foundationConfigurationId"}}}}, {"text": "Parent foundation ID", "type": "variable", "identifier": "parentId", "properties": {"type": "number"}}, {"text": "Selection", "type": "select", "identifier": "selection", "properties": {"options": [{"value": "single", "label": "Single", "description": "A specific foundation"}, {"value": "multiple", "label": "Multiple", "description": "All foundations under the parent"}], "description": "Choose whether to select a single foundation or multiple foundations", "defaultValue": "single"}}, {"text": "Key", "type": "variable", "identifier": "key", "properties": {"type": "text", "required": true, "properties": {"regex": "^([A-Za-z0-9{][A-Za-z0-9{}_.]*)$"}, "hidden": {"lhs": "{{thisStep.selection}}", "operator": "=", "rhs": "multiple"}}}, {"text": "Continue flow if foundation is not found", "type": "select", "identifier": "continueFlowIfNotFound", "properties": {"required": true, "uiControls": {"displayAs": "radioGroup"}, "options": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}], "defaultValue": "true"}}, {"text": "Foundation variable name", "type": "text", "identifier": "foundationVariableName", "properties": {"required": false, "defaultValue": "foundation__step_{{thisStep.id}}", "regex": "^[a-zA-Z0-9_]*$"}}], "apiCall": {"url": "/ai/api/foundations/select-many", "internal": true, "method": "GET", "body": {"workspaceId": "{{global.workspaceId}}", "foundationConfigurationId": "{{thisStep.foundationConfigurationId}}", "parentId": "{{thisStep.parentId}}", "key": "{{thisStep.key}}", "allowNull": "{{thisStep.continueFlowIfNotFound}}"}, "bodyProperties": {"parentId": {"nullable": true}, "key": {"nullable": true}, "allowNull": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"type": "list", "identifier": "foundations", "properties": {"items": [{"type": "foundation.minimal", "identifier": "foundation"}]}}]}}}, "variableMappings": [{"type": "foundation.{{thisStep.foundationConfigurationId}}", "identifier": "{{thisStep.foundationVariableName}}", "value": "{{thisStep.response.foundations[-1].id}}", "properties": {"hidden": {"lhs": "{{thisStep.selection}}", "operator": "=", "rhs": "multiple"}}}, {"type": "list", "identifier": "{{thisStep.foundationVariableName}}", "value": "{{thisStep.response.foundations[*].id}}", "properties": {"items": [{"type": "foundation.{{thisStep.foundationConfigurationId}}", "identifier": "{{thisStep.foundationVariableName}}_item"}], "hidden": {"lhs": "{{thisStep.selection}}", "operator": "!=", "rhs": "multiple"}, "valueIsExpression": true}}]}}}, {"primaryIdentifier": "selectOrCreateFoundation", "name": "Select or create a foundation", "description": "", "type": "action", "properties": {"icon": {"name": "add"}, "category": {"name": "Foundations", "icon": {"name": "roofing"}, "order": 2}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Foundation Level ID", "type": "variable", "identifier": "foundationConfigurationId", "properties": {"required": true, "type": "select", "properties": {"dynamicOptions": {"tag": "foundationConfigurationId"}}}}, {"text": "Parent foundation ID", "type": "variable", "identifier": "parentId", "properties": {"type": "number", "required": true}}, {"text": "Key", "type": "variable", "identifier": "key", "properties": {"type": "text", "required": true, "properties": {"regex": "^([A-Za-z0-9{][A-Za-z0-9{}_.]*)$"}}}, {"text": "Name", "type": "variable", "identifier": "name", "properties": {"type": "text", "required": false}}, {"text": "Foundation variable name", "type": "text", "identifier": "foundationVariableName", "properties": {"required": false, "defaultValue": "foundation__step_{{thisStep.id}}", "regex": "^[a-zA-Z0-9_]*$"}}], "apiCall": {"url": "/ai/api/foundations/select-or-create", "internal": true, "method": "POST", "body": {"workspaceId": "{{global.workspaceId}}", "foundationConfigurationId": "{{thisStep.foundationConfigurationId}}", "key": "{{thisStep.key}}", "name": "{{thisStep.name}}", "parentId": "{{thisStep.parentId}}", "allowNull": "true", "createIfNotExists": "true"}, "bodyProperties": {"parentId": {"nullable": true}, "allowNull": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"type": "foundation.minimal", "identifier": "foundation"}]}}}, "variableMappings": [{"type": "foundation.{{thisStep.foundationConfigurationId}}", "identifier": "{{thisStep.foundationVariableName}}", "value": "{{thisStep.response.foundation.id}}"}]}}}, {"primaryIdentifier": "updateFoundation", "name": "Update a foundation", "description": "", "type": "action", "properties": {"icon": {"name": "autorenew"}, "category": {"name": "Foundations", "icon": {"name": "roofing"}, "order": 2}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Foundation ID", "type": "variable", "identifier": "foundationId", "properties": {"type": "number", "required": true}}, {"text": "Foundation Level ID (not updatable)", "type": "variable", "identifier": "foundationConfigurationId", "properties": {"type": "select", "required": true, "properties": {"dynamicOptions": {"tag": "foundationConfigurationId"}}}}, {"text": "Properties", "type": "table", "identifier": "properties", "properties": {"uiControls": {"displayAs": "tableList"}, "columns": [{"text": "Key", "identifier": "key", "type": "variable", "properties": {"type": "text", "defaultValue": ""}}, {"text": "Value", "identifier": "value", "type": "variable", "properties": {"type": "text", "defaultValue": ""}}]}}, {"text": "Foundation variable name", "type": "text", "identifier": "foundationVariableName", "properties": {"required": false, "regex": "^[a-zA-Z0-9_]*$", "defaultValue": "foundation__step_{{thisStep.id}}"}}], "apiCall": {"url": "/ai/api/foundations/{{thisStep.foundationId}}/properties", "internal": true, "method": "PUT", "body": {"properties": "$reduce({{thisStep.properties}}, function($acc, $property) { $merge([$acc, $not($keys($property)) or $property.key = \"\" ? {} : { $property.key: $property.value }]) }, {})", "workspaceId": "{{global.workspaceId}}"}, "bodyProperties": {"properties": {"isExpression": true}}, "response": {"type": "json", "properties": {"items": [{"type": "foundation.minimal", "identifier": "foundation"}]}}}, "variableMappings": [{"type": "foundation.{{thisStep.foundationConfigurationId}}", "identifier": "{{thisStep.foundationVariableName}}", "value": "{{thisStep.response.foundation.id}}"}]}}}, {"primaryIdentifier": "setAnswer", "name": "Set form answer", "description": "Set the answer to a question on a form", "type": "action", "properties": {"icon": {"name": "edit"}, "category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "isLocked": true, "isHidden": false, "deprecated": {"replacement": "setMultipleAnswers", "deprecatedAt": "2025-05-25T00:00:00Z"}, "configuration": {"content": [{"text": "Form ID", "type": "variable", "identifier": "formId", "properties": {"type": "number", "required": true}}, {"text": "Form configuration", "type": "select", "identifier": "formConfigurationId", "properties": {"required": false, "dynamicOptions": {"tag": "formConfigurationId"}}}, {"text": "Question ID", "identifier": "questionId", "type": "variable", "properties": {"required": true, "type": "select", "properties": {"dynamicOptions": {"tag": "formConfigurationId.questionId", "body": {"formConfigurationId": "{{thisStep.formConfigurationId}}"}}}}}, {"text": "For table, list or file question?", "identifier": "questionTypeWithOperation", "type": "select", "properties": {"required": true, "options": [{"value": "no", "label": "No"}, {"value": "table", "label": "Table"}, {"value": "list", "label": "List"}, {"value": "file", "label": "File"}], "defaultValue": "no"}}, {"text": "Table operation", "type": "select", "identifier": "operation", "properties": {"required": {"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "=", "rhs": "table"}, "hidden": {"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, "options": [{"value": "setTable", "label": "Set table"}, {"value": "setCell", "label": "Set cell"}, {"value": "setRow", "label": "Set row"}, {"value": "setColumn", "label": "Set column"}], "defaultValue": "setCell"}}, {"text": "Column question ID", "type": "variable", "identifier": "columnId", "properties": {"type": "text", "hidden": {"OR": [{"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setTable"}, {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setRow"}]}, "required": {"OR": [{"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setRow"}, {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setCell"}]}, "defaultValue": ""}}, {"text": "Select row by", "type": "select", "identifier": "rowSelection", "properties": {"hidden": {"OR": [{"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setTable"}, {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setColumn"}]}, "required": {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setRow"}, "options": [{"value": "rowId", "label": "Row ID"}, {"value": "rowIndex", "label": "Row index"}], "defaultValue": "rowId"}}, {"text": "Row ID", "type": "variable", "identifier": "rowId", "properties": {"type": "text", "hidden": {"OR": [{"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.rowSelection}}", "operator": "!=", "rhs": "rowId"}, {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setTable"}, {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setColumn"}]}, "defaultValue": ""}}, {"text": "Row index", "type": "variable", "identifier": "rowIndex", "properties": {"type": "number", "hidden": {"OR": [{"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.rowSelection}}", "operator": "!=", "rhs": "rowIndex"}, {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setTable"}, {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setColumn"}]}, "defaultValue": ""}}, {"text": "Update rows if rowIds match existing data", "type": "select", "identifier": "valueRowIdShouldUpdate", "properties": {"uiControls": {"displayAs": "radioGroup"}, "options": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}], "defaultValue": "true", "hidden": {"OR": [{"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.operation}}", "operator": "!=", "rhs": "setTable"}]}, "required": {"lhs": "{{thisStep.operation}}", "operator": "=", "rhs": "setTable"}}}, {"text": "List operation", "type": "select", "identifier": "listOperation", "properties": {"required": {"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "=", "rhs": "list"}, "hidden": {"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "list"}, "options": [{"value": "setList", "label": "Set list"}, {"value": "setItem", "label": "Set item"}, {"value": "removeItem", "label": "Remove item"}], "defaultValue": "setList"}}, {"text": "Select item by", "type": "select", "identifier": "itemSelection", "properties": {"hidden": {"OR": [{"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "list"}, {"lhs": "{{thisStep.listOperation}}", "operator": "=", "rhs": "setList"}]}, "required": {"OR": [{"lhs": "{{thisStep.listOperation}}", "operator": "=", "rhs": "setItem"}, {"lhs": "{{thisStep.listOperation}}", "operator": "=", "rhs": "removeItem"}]}, "uiControls": {"displayAs": "radioGroup"}, "options": [{"value": "newItem", "label": "Add new item"}, {"value": "itemId", "label": "Item ID"}, {"value": "itemIndex", "label": "Item index"}], "defaultValue": "newItem"}}, {"text": "Item ID", "type": "variable", "identifier": "itemId", "properties": {"type": "text", "hidden": {"OR": [{"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "list"}, {"lhs": "{{thisStep.itemSelection}}", "operator": "!=", "rhs": "itemId"}, {"lhs": "{{thisStep.listOperation}}", "operator": "=", "rhs": "setList"}]}, "defaultValue": ""}}, {"text": "Item index", "type": "variable", "identifier": "itemIndex", "properties": {"type": "number", "hidden": {"OR": [{"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "list"}, {"lhs": "{{thisStep.itemSelection}}", "operator": "!=", "rhs": "itemIndex"}, {"lhs": "{{thisStep.listOperation}}", "operator": "=", "rhs": "setList"}]}, "defaultValue": ""}}, {"text": "File operation", "type": "select", "identifier": "fileOperation", "properties": {"required": {"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "=", "rhs": "file"}, "hidden": {"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "file"}, "options": [{"value": "setFile", "label": "Set files"}, {"value": "addFile", "label": "Add file"}, {"value": "removeFile", "label": "Remove file"}], "defaultValue": "setFile"}}, {"text": "File index", "type": "variable", "identifier": "fileIndex", "properties": {"type": "text", "hidden": {"OR": [{"lhs": "{{thisStep.questionTypeWithOperation}}", "operator": "!=", "rhs": "file"}, {"lhs": "{{thisStep.fileOperation}}", "operator": "!=", "rhs": "removeFile"}]}, "defaultValue": ""}}, {"text": "Answer", "type": "variable", "identifier": "answer", "properties": {"type": "unknown", "hidden": {"OR": [{"lhs": "{{thisStep.listOperation}}", "operator": "=", "rhs": "removeItem"}, {"lhs": "{{thisStep.fileOperation}}", "operator": "=", "rhs": "removeFile"}]}}}], "apiCall": {"url": "/ai/api/forms/{{thisStep.formId}}/answer", "internal": true, "method": "PUT", "body": {"questionId": "{{thisStep.questionId}}", "value": "{{thisStep.answer}}", "operation": "{{thisStep.operation}}", "rowId": "{{thisStep.rowId}}", "rowIndex": "{{thisStep.rowIndex}}", "columnId": "{{thisStep.columnId}}", "valueRowIdShouldUpdate": "{{thisStep.valueRowIdShouldUpdate}}", "listOperation": "{{thisStep.listOperation}}", "itemId": "{{thisStep.itemId}}", "itemIndex": "{{thisStep.itemIndex}}", "fileOperation": "{{thisStep.fileOperation}}", "fileIndex": "{{thisStep.fileIndex}}", "workspaceId": "{{global.workspaceId}}"}, "bodyProperties": {"value": {"nullable": true}, "rowIndex": {"nullable": true}, "rowId": {"nullable": true}, "columnId": {"nullable": true}, "itemId": {"nullable": true}, "itemIndex": {"nullable": true}, "listOperation": {"nullable": true}, "operation": {"nullable": true}, "fileOperation": {"nullable": true}, "fileName": {"nullable": true}, "fileIndex": {"nullable": true}, "valueRowIdShouldUpdate": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"type": "text", "identifier": "documentId"}, {"type": "text", "identifier": "message"}]}}}, "variableMappings": []}}}, {"primaryIdentifier": "<PERSON><PERSON><PERSON><PERSON>", "name": "Set form alert", "description": "Set or remove an alert on a form", "type": "action", "properties": {"icon": {"name": "campaign", "fillStyle": "filled"}, "category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Operation", "type": "select", "identifier": "operation", "properties": {"required": true, "uiControls": {"displayAs": "radioGroup"}, "options": [{"value": "add", "label": "Add alert"}, {"value": "resolve", "label": "Resolve alert"}, {"value": "remove", "label": "Remove alert"}], "defaultValue": "add"}}, {"text": "Form ID", "type": "variable", "identifier": "formId", "properties": {"type": "number", "required": true}}, {"text": "Form configuration", "type": "select", "identifier": "formConfigurationId", "properties": {"required": false, "dynamicOptions": {"tag": "formConfigurationId"}}}, {"text": "Question ID", "identifier": "questionId", "type": "variable", "properties": {"type": "select", "properties": {"dynamicOptions": {"tag": "formConfigurationId.questionId", "body": {"formConfigurationId": "{{thisStep.formConfigurationId}}"}}}}}, {"text": "Row ID", "description": "(for table questions)", "type": "variable", "identifier": "rowId", "properties": {"type": "text", "required": false, "defaultValue": ""}}, {"text": "Column ID", "description": "(for table questions)", "type": "variable", "identifier": "columnId", "properties": {"type": "text", "required": false, "defaultValue": ""}}, {"text": "Group identifier", "type": "variable", "identifier": "groupIdentifier", "properties": {"type": "text", "required": false, "properties": {"regex": "^([A-Za-z0-9{}_ .]+)$"}}}, {"text": "Alert message", "type": "variable", "identifier": "message", "properties": {"type": "text", "required": false}}, {"text": "Alert variant", "identifier": "variant", "type": "variable", "properties": {"type": "select", "properties": {"uiControls": {"displayAs": "radioGroup"}, "required": false, "defaultValue": "warning", "options": [{"value": "blocker", "label": "Blocker"}, {"value": "warning", "label": "Warning"}, {"value": "success", "label": "Success"}, {"value": "info", "label": "Info"}]}}}], "apiCall": {"url": "/ai/api/forms/{{thisStep.formId}}/alert", "internal": true, "method": "PUT", "body": {"questionId": "{{thisStep.questionId}}", "rowId": "{{thisStep.rowId}}", "columnId": "{{thisStep.columnId}}", "operation": "{{thisStep.operation}}", "groupIdentifier": "{{thisStep.groupIdentifier}}", "type": "{{thisStep.variant}}", "message": "{{thisStep.message}}", "workspaceId": "{{global.workspaceId}}"}, "bodyProperties": {"rowId": {"nullable": true}, "columnId": {"nullable": true}, "groupIdentifier": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"type": "text", "identifier": "documentId"}, {"type": "text", "identifier": "message"}]}}}, "variableMappings": []}}}, {"primaryIdentifier": "iteratorForEach", "name": "For each", "description": "", "type": "iterator", "properties": {"icon": {"name": "repeat"}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "List to iterate over", "type": "variable", "identifier": "list", "properties": {"type": "list", "required": true}}, {"text": "List item variable name", "type": "text", "identifier": "itemVariableName", "properties": {"required": true, "defaultValue": "item_{{thisStep.id}}", "regex": "^[a-zA-Z0-9_]*$"}}, {"text": "Output transformed value(s)", "type": "select", "identifier": "isReturn", "properties": {"uiControls": {"displayAs": "radioGroup"}, "type": "boolean", "required": true, "options": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}], "defaultValue": "false"}}, {"text": "List item output type", "type": "variable", "identifier": "transformedValueType", "properties": {"required": true, "type": "select", "hidden": {"lhs": "{{thisStep.isReturn}}", "operator": "=", "rhs": "false"}, "properties": {"dynamicOptions": {"tag": "questionTypes", "body": {"skip": "select,multiSelect,files"}}}, "defaultValue": "json"}}, {"text": "Final output variable name", "type": "text", "identifier": "resultVariableName", "properties": {"required": false, "defaultValue": "output__step_{{thisStep.id}}", "regex": "^[a-zA-Z0-9_]*$", "hidden": {"lhs": "{{thisStep.isReturn}}", "operator": "=", "rhs": "false"}}}], "variableMappings": [{"type": "table", "identifier": "{{thisStep.resultVariableName}}", "value": "{{thisStep.result}}", "properties": {"hidden": {"lhs": "{{thisStep.isReturn}}", "operator": "=", "rhs": "false"}}}]}}}, {"primaryIdentifier": "iteratorFilter", "name": "Filter", "description": "", "type": "iterator", "properties": {"icon": {"name": "filter_alt", "fillStyle": "outlined"}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "List to iterate over", "type": "variable", "identifier": "list", "properties": {"type": "list", "required": true}}, {"text": "List item variable name", "type": "text", "identifier": "itemVariableName", "properties": {"required": true, "defaultValue": "item_{{thisStep.id}}", "regex": "^[a-zA-Z0-9_]*$"}}, {"text": "Transformed value type (for each)", "type": "variable", "identifier": "itemValueType", "properties": {"required": true, "type": "select", "properties": {"dynamicOptions": {"tag": "questionTypes", "body": {"skip": "select,multiSelect,files"}}}, "defaultValue": "json"}}, {"text": "Final filtered output variable name", "type": "text", "identifier": "resultVariableName", "properties": {"required": true, "defaultValue": "filtered__step_{{thisStep.id}}", "regex": "^[a-zA-Z0-9_]*$"}}], "variableMappings": [{"type": "table", "identifier": "{{thisStep.resultVariableName}}", "value": "{{thisStep.result}}"}]}}}, {"primaryIdentifier": "iteratorAggregate", "name": "Aggregate", "description": "", "type": "iterator", "properties": {"icon": {"name": "filter_alt", "fillStyle": "outlined"}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "List to iterate over", "type": "variable", "identifier": "list", "properties": {"type": "list", "required": true}}, {"text": "List item variable name", "type": "text", "identifier": "itemVariableName", "properties": {"required": true, "defaultValue": "item_{{thisStep.id}}", "regex": "^[a-zA-Z0-9_]*$"}}, {"text": "Aggregate output variable name", "type": "text", "identifier": "resultVariableName", "properties": {"required": true, "defaultValue": "aggregate__step_{{thisStep.id}}", "regex": "^[a-zA-Z0-9_]*$"}}, {"text": "Aggregate output variable type", "type": "variable", "identifier": "resultVariableType", "properties": {"required": true, "type": "select", "properties": {"dynamicOptions": {"tag": "questionTypes", "body": {"skip": "select,multiSelect,files"}}}, "defaultValue": "number"}}], "variableMappings": [{"type": "{{thisStep.resultVariableType}}", "identifier": "{{thisStep.resultVariableName}}", "value": "{{thisStep.result}}"}]}}}, {"primaryIdentifier": "generateDocxFromTemplate", "name": "Generate Docx from template", "description": "Use a template from a URL to generate a document with replacements", "type": "action", "properties": {"icon": {"name": "note_add"}, "category": {"name": "Documents", "icon": {"name": "description"}}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Template URL", "type": "variable", "identifier": "templateUrl", "properties": {"type": "text", "required": true}}, {"text": "Input replacements", "type": "variable", "identifier": "replacements", "properties": {"type": "json", "required": true}}, {"text": "Output file name", "type": "variable", "identifier": "outputFilename", "properties": {"type": "text", "required": false}}, {"text": "Generated Docx file variable name", "type": "text", "identifier": "documentVariableName", "properties": {"required": false, "defaultValue": "generatedDocxFile", "regex": "^[a-zA-Z0-9_]*$"}}], "apiCall": {"url": "/ai/api/internal/workspaces/{{global.workspaceId}}/actions/generateFromTemplate", "internal": true, "method": "POST", "body": {"templateUrl": "{{thisStep.templateUrl}}", "replacements": "{{thisStep.replacements}}", "outputFilename": "{{thisStep.outputFilename}}"}, "bodyProperties": {"outputFilename": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"identifier": "name", "type": "text"}, {"identifier": "path", "type": "text"}]}}}, "variableMappings": [{"type": "files", "identifier": "{{thisStep.documentVariableName}}", "value": "{{thisStep.response}}"}]}}}, {"primaryIdentifier": "downloadFile", "name": "Download a file", "description": "Downloads a file from a retrievable location to use in the flow", "type": "action", "properties": {"icon": {"name": "file_save"}, "category": {"name": "Documents", "icon": {"name": "description"}}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "From Location", "type": "select", "identifier": "urlSource", "properties": {"uiControls": {"displayAs": "radioGroup"}, "required": true, "options": [{"value": "INTERNET", "label": "URL"}, {"value": "ONETEAM_STORAGE", "label": "OneTeam"}], "defaultValue": "INTERNET"}}, {"text": "File URL", "type": "variable", "identifier": "fileUrl", "properties": {"type": "text", "required": true}}, {"text": "Output file name", "type": "variable", "identifier": "outputFilename", "properties": {"type": "text", "required": false}}, {"text": "Variable name", "type": "text", "identifier": "fileVariableName", "properties": {"required": false, "defaultValue": "downloadedFile", "regex": "^[a-zA-Z0-9_]*$"}}], "apiCall": {"url": "/ai/api/internal/workspaces/{{global.workspaceId}}/actions/downloadFile", "internal": true, "method": "POST", "body": {"fileUrl": "{{thisStep.fileUrl}}", "outputFilename": "{{thisStep.outputFilename}}", "urlSource": "{{thisStep.urlSource}}"}, "bodyProperties": {"outputFilename": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"identifier": "name", "type": "text"}, {"identifier": "path", "type": "text"}]}}}, "variableMappings": [{"type": "files", "identifier": "{{thisStep.fileVariableName}}", "value": "{{thisStep.response}}"}]}}}, {"primaryIdentifier": "setMultipleAnswers", "name": "Set form answer(s)", "description": "", "type": "action", "properties": {"icon": {"name": "edit_document"}, "category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Form ID", "type": "variable", "identifier": "formId", "properties": {"type": "number", "required": true}}, {"text": "Form configuration", "type": "select", "identifier": "formConfigurationId", "properties": {"required": false, "dynamicOptions": {"tag": "formConfigurationId"}}}, {"text": "Answer", "type": "table", "identifier": "answers", "properties": {"uiControls": {"displayAs": "listOfInputs"}, "columns": [{"text": "Question ID", "identifier": "questionId", "type": "variable", "properties": {"required": true, "type": "select", "properties": {"dynamicOptions": {"tag": "formConfigurationId.questionId", "body": {"formConfigurationId": "{{thisStep.formConfigurationId}}"}}}}}, {"type": "section", "name": "", "description": "", "identifier": "operationSection", "properties": {"uiControls": {"visual": "background"}}, "content": [{"text": "For table, list or file question?", "identifier": "questionTypeWithOperation", "type": "select", "properties": {"required": true, "options": [{"value": "no", "label": "No"}, {"value": "table", "label": "Table"}, {"value": "list", "label": "List"}, {"value": "file", "label": "File"}], "defaultValue": "no"}}, {"text": "Table operation", "type": "select", "identifier": "operation", "properties": {"required": {"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "=", "rhs": "table"}, "hidden": {"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, "options": [{"value": "setTable", "label": "Set table"}, {"value": "setColumn", "label": "Set column"}, {"value": "setRow", "label": "Set row"}, {"value": "setCell", "label": "Set cell"}], "defaultValue": "setCell"}}, {"text": "Column question ID", "type": "variable", "identifier": "columnId", "properties": {"type": "text", "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setTable"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setRow"}]}, "required": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setRow"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setCell"}]}, "defaultValue": ""}}, {"text": "Select row by", "type": "select", "identifier": "rowSelection", "properties": {"uiControls": {"displayAs": "radioGroup"}, "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setTable"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setColumn"}]}, "required": {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setRow"}, "options": [{"value": "newRow", "label": "Add new row"}, {"value": "rowId", "label": "Row ID"}, {"value": "rowIndex", "label": "Row index"}], "defaultValue": "newRow"}}, {"text": "Row ID", "type": "variable", "identifier": "rowId", "properties": {"type": "text", "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].rowSelection}}", "operator": "!=", "rhs": "rowId"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setTable"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setColumn"}]}, "defaultValue": ""}}, {"text": "Row index", "type": "variable", "identifier": "rowIndex", "properties": {"type": "number", "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].rowSelection}}", "operator": "!=", "rhs": "rowIndex"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setTable"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setColumn"}]}, "defaultValue": ""}}, {"text": "List operation", "type": "select", "identifier": "listOperation", "properties": {"required": {"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "=", "rhs": "list"}, "hidden": {"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "list"}, "options": [{"value": "setList", "label": "Set list"}, {"value": "setItem", "label": "Set item"}, {"value": "removeItem", "label": "Remove item"}], "defaultValue": "setList"}}, {"text": "Select item by", "type": "select", "identifier": "itemSelection", "properties": {"uiControls": {"displayAs": "radioGroup"}, "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "list"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].listOperation}}", "operator": "=", "rhs": "setList"}]}, "required": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].listOperation}}", "operator": "=", "rhs": "setItem"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].listOperation}}", "operator": "=", "rhs": "removeItem"}]}, "options": [{"value": "newItem", "label": "Add new item"}, {"value": "itemId", "label": "Item ID"}, {"value": "itemIndex", "label": "Item index"}], "defaultValue": "itemId"}}, {"text": "Item ID", "type": "variable", "identifier": "itemId", "properties": {"type": "text", "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "list"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].itemSelection}}", "operator": "!=", "rhs": "itemId"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].listOperation}}", "operator": "=", "rhs": "setList"}]}, "defaultValue": ""}}, {"text": "Item index", "type": "variable", "identifier": "itemIndex", "properties": {"type": "number", "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "list"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].itemSelection}}", "operator": "!=", "rhs": "itemIndex"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].listOperation}}", "operator": "=", "rhs": "setList"}]}, "defaultValue": ""}}, {"text": "File operation", "type": "select", "identifier": "fileOperation", "properties": {"required": {"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "=", "rhs": "file"}, "hidden": {"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "file"}, "options": [{"value": "setFile", "label": "Set files"}, {"value": "addFile", "label": "Add file"}, {"value": "removeFile", "label": "Remove file"}], "defaultValue": "setFile"}}, {"text": "File index", "type": "variable", "identifier": "fileIndex", "properties": {"type": "text", "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "file"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].fileOperation}}", "operator": "!=", "rhs": "removeFile"}]}, "defaultValue": ""}}, {"text": "Set value by column", "type": "boolean", "identifier": "setValueByColumn", "properties": {"uiControls": {"displayAs": "toggle"}, "defaultValue": false, "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"AND": [{"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "!=", "rhs": "setTable"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "!=", "rhs": "setRow"}]}]}}}, {"text": "Data handling", "type": "select", "identifier": "tableOperationPolicy", "properties": {"options": [{"value": "replaceTable", "label": "Replace entire table", "description": "Resizes table to fit the data"}, {"value": "fillColumns", "label": "Replace specified columns", "description": "Clear specified columns and fill in data top-down"}], "defaultValue": "replaceTable", "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "!=", "rhs": "setTable"}]}}}]}, {"text": "Value", "type": "variable", "identifier": "answer", "properties": {"type": "unknown", "hidden": {"OR": [{"AND": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "=", "rhs": "table"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].setValueByColumn}}", "operator": "=", "rhs": "true"}, {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setTable"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setRow"}]}]}, {"lhs": "{{thisStep.answers[{{thisIndex}}].listOperation}}", "operator": "=", "rhs": "removeItem"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].fileOperation}}", "operator": "=", "rhs": "removeFile"}]}}}, {"text": "Value", "type": "table", "identifier": "answerByColumn", "properties": {"uiControls": {"displayAs": "tableList"}, "columns": [{"text": "Column Id", "identifier": "columnId", "type": "variable", "properties": {"type": "select", "defaultValue": "", "properties": {"dynamicOptions": {"tag": "tableQuestionColumnId", "body": {"formConfigurationId": "{{thisStep.formConfigurationId}}", "questionId": "{{thisStep.answers[{{thisIndex}}].questionId}}"}}}}}, {"text": "Value", "identifier": "value", "type": "variable", "properties": {"type": "unknown", "defaultValue": ""}}], "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].setValueByColumn}}", "operator": "!=", "rhs": "true"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"AND": [{"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "!=", "rhs": "setTable"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "!=", "rhs": "setRow"}]}]}, "required": {"AND": [{"lhs": "{{thisStep.answers[{{thisIndex}}].setValueByColumn}}", "operator": "=", "rhs": "true"}, {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setTable"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setRow"}]}, {"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "=", "rhs": "table"}]}, "defaultValue": ""}}, {"text": "Update rows if row IDs match existing data", "type": "select", "identifier": "valueRowIdShouldUpdate", "properties": {"uiControls": {"displayAs": "radioGroup"}, "options": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}], "defaultValue": "true", "hidden": {"OR": [{"lhs": "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}", "operator": "!=", "rhs": "table"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "!=", "rhs": "setTable"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].setValueByColumn}}", "operator": "=", "rhs": "true"}]}, "required": {"AND": [{"lhs": "{{thisStep.answers[{{thisIndex}}].setValueByColumn}}", "operator": "!=", "rhs": "true"}, {"lhs": "{{thisStep.answers[{{thisIndex}}].operation}}", "operator": "=", "rhs": "setTable"}]}}}]}}], "apiCall": {"url": "/ai/api/forms/{{thisStep.formId}}/answers", "internal": true, "method": "PUT", "body": {"answers": "[ $map({{thisStep.answers}}, function($answer) { {\n    \"value\":\n        $not($exists($answer.setValueByColumn)) or $answer.setValueByColumn != true or $not($answer.questionTypeWithOperation = \"table\") or $not($answer.operation in [\"setTable\", \"setRow\"])\n            ? $answer.answer\n            : $answer.operation in [\"setTable\"]\n                ? (\n                    $maxLen := $max($map($answer.answerByColumn, function($c) { $count($c.value) }));\n                    $map(\n                        $map([0..($maxLen-1)], function($i) { $i }),\n                        function($i) {\n                            $reduce(\n                                $answer.answerByColumn,\n                                function($acc, $col) {\n                                    $merge([\n                                        $acc,\n                                        { ($col.columnId): $exists($col.value[$i]) ? $col.value[$i] : \"\" }\n                                    ])\n                                },\n                                {}\n                            )\n                        }\n                    )\n                )\n                : $reduce(\n                    $answer.answerByColumn,\n                    function($acc, $col) {\n                        $merge([\n                            $acc,\n                            { ($col.columnId): $col.value }\n                        ])\n                    },\n                    {}\n                ),\n    \"questionId\": $answer.questionId,\n    \"operation\": ($answer.operation = \"\" ? null : ($answer.tableOperationPolicy = \"fillColumns\" ? \"fillColumns\" : $answer.operation)),\n    \"rowId\": ($answer.rowSelection != \"rowId\" or $answer.rowId = \"\" ? null : $answer.rowId),\n    \"rowIndex\": ($answer.rowSelection != \"rowIndex\" or $answer.rowIndex = \"\" ? null : $answer.rowIndex),\n    \"columnId\": ($answer.columnId = \"\" ? null : $answer.columnId),\n    \"valueRowIdShouldUpdate\": ($answer.valueRowIdShouldUpdate = \"\" ? null : $answer.valueRowIdShouldUpdate),\n    \"listOperation\": ($answer.listOperation = \"\" ? null : $answer.listOperation),\n    \"itemId\": ($answer.itemSelection != \"itemId\" or $answer.itemId = \"\" ? null : $answer.itemId),\n    \"itemIndex\": ($answer.itemSelection != \"itemIndex\" or $answer.itemIndex = \"\" ? null : $answer.itemIndex),\n    \"fileOperation\": ($answer.fileOperation = \"\" ? null : $answer.fileOperation),\n    \"fileIndex\": ($answer.fileIndex = \"\" ? null : $answer.fileIndex)\n} }) ]", "workspaceId": "{{global.workspaceId}}"}, "bodyProperties": {"answers": {"isExpression": true}}, "response": {"type": "json", "properties": {"items": [{"type": "text", "identifier": "documentId"}, {"type": "text", "identifier": "message"}]}}}, "variableMappings": []}}}, {"primaryIdentifier": "sendApiRequest", "name": "Send an API Request", "description": "", "type": "action", "properties": {"icon": {"name": "api"}, "category": {"name": "API", "icon": {"name": "code"}}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "URL", "type": "variable", "identifier": "url", "properties": {"type": "text", "required": true}}, {"text": "HTTP method", "identifier": "httpMethod", "type": "select", "properties": {"uiControls": {"displayAs": "radioGroup"}, "required": true, "options": [{"value": "GET", "label": "GET"}, {"value": "POST", "label": "POST"}, {"value": "PUT", "label": "PUT"}, {"value": "DELETE", "label": "DELETE"}], "defaultValue": "GET"}}, {"text": "Content Type", "type": "select", "identifier": "contentType", "properties": {"uiControls": {"displayAs": "radioGroup"}, "required": false, "hidden": {"lhs": "{{thisStep.bodyParameters}}", "operator": "=", "rhs": ""}, "options": [{"value": "application/json", "label": "application/json"}, {"value": "application/x-www-form-urlencoded", "label": "application/x-www-form-urlencoded"}, {"value": "multipart/form-data", "label": "multipart/form-data"}], "defaultValue": "application/json"}}, {"text": "Headers", "type": "table", "identifier": "headers", "properties": {"uiControls": {"displayAs": "tableList"}, "columns": [{"text": "Key", "identifier": "key", "type": "variable", "properties": {"type": "text", "defaultValue": ""}}, {"text": "Value", "identifier": "value", "type": "variable", "properties": {"type": "text", "defaultValue": ""}}]}}, {"text": "Body parameters", "type": "variable", "identifier": "bodyParameters", "properties": {"type": "json"}}, {"text": "Response variable name", "type": "text", "identifier": "responseVariableName", "properties": {"required": false, "defaultValue": "response__step_{{thisStep.id}}", "regex": "^[a-zA-Z0-9_]*$"}}, {"text": "Response", "type": "table", "identifier": "response", "properties": {"uiControls": {"displayAs": "listOfInputs"}, "columns": [{"text": "HTTP Code", "identifier": "httpCode", "type": "select", "properties": {"uiControls": {"displayAs": "radioGroup"}, "required": true, "options": [{"value": "200", "label": "200"}, {"value": "404", "label": "404"}, {"value": "500", "label": "500"}], "defaultValue": "200"}}, {"text": "Expected Response", "type": "schema", "identifier": "expectedPayload", "properties": {"type": "json"}}]}}, {"text": "Continue flow if API fails", "type": "select", "identifier": "continueFlowIfApiFails", "properties": {"uiControls": {"displayAs": "radioGroup"}, "type": "boolean", "options": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}], "defaultValue": "false"}}], "apiCall": {"url": "{{thisStep.url}}", "internal": false, "method": "{{thisStep.httpMethod}}", "headers": "$reduce({{thisStep.headers}}, function($acc, $header) { $merge([$acc, $not($keys($header)) or $header.key = \"\" ? {} : { $header.key: $header.value }]) }, {})", "contentType": "{{thisStep.contentType}}", "body": "{{thisStep.bodyParameters}}", "bodyProperties": {"headers": {"isExpression": true}, "body": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"type": "text", "identifier": "status"}, {"type": "json", "identifier": "payload"}, {"type": "text", "identifier": "errors"}]}}}, "variableMappings": [{"type": "json", "identifier": "{{thisStep.responseVariableName}}", "value": "{{thisStep.response}}"}, {"type": "text", "identifier": "httpCode", "value": "{{thisStep.response.httpCode}}"}]}}}, {"primaryIdentifier": "selectSeriesInterval", "name": "Select a series interval", "type": "action", "properties": {"category": {"name": "Forms", "icon": {"name": "article"}, "order": 1}, "icon": {"name": "steppers"}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Series configuration ID", "type": "variable", "identifier": "seriesConfigurationId", "properties": {"type": "select", "required": true, "defaultValue": "", "properties": {"dynamicOptions": {"tag": "seriesConfigurationId"}}}}, {"text": "Interval", "type": "variable", "identifier": "intervalIdOrName", "properties": {"type": "select", "required": true, "defaultValue": "", "properties": {"dynamicOptions": {"tag": "seriesConfigurationId.intervalId", "body": {"seriesConfigurationId": "{{thisStep.seriesConfigurationId}}"}}}}}, {"text": "Series interval variable name", "type": "text", "identifier": "intervalVariableName", "properties": {"required": false, "regex": "^[a-zA-Z0-9_]*$", "defaultValue": "seriesInterval__step_{{thisStep.id}}"}}, {"text": "Continue flow if series interval is not found", "type": "select", "identifier": "continueFlowIfNotFound", "properties": {"uiControls": {"displayAs": "radioGroup"}, "required": true, "options": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}], "defaultValue": "true"}}], "apiCall": {"url": "/ai/api/workspaces/{{global.workspaceId}}/intervals", "internal": true, "method": "POST", "body": {"seriesId": "{{thisStep.seriesConfigurationId}}", "intervalIdOrName": "{{thisStep.intervalIdOrName}}", "allowNull": "{{thisStep.continueFlowIfNotFound}}"}, "bodyProperties": {"seriesId": {"nullable": false}, "intervalIdOrName": {"nullable": false}, "allowNull": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"type": "seriesInterval.minimal", "identifier": "seriesInterval"}]}}}, "variableMappings": [{"type": "seriesInterval.{{thisStep.seriesConfigurationId}}", "identifier": "{{thisStep.intervalVariableName}}", "value": "{{thisStep.response.seriesInterval.id}}"}]}}}, {"primaryIdentifier": "convertFile", "name": "Convert a file", "description": "Converts a file to a different format", "type": "action", "properties": {"icon": {"name": "file_save"}, "category": {"name": "Documents", "icon": {"name": "description"}}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "File", "type": "variable", "identifier": "file", "properties": {"type": "files", "required": true}}, {"text": "Convert to", "type": "select", "identifier": "outputFormat", "properties": {"uiControls": {"displayAs": "radioGroup"}, "required": true, "options": [{"value": "base64", "label": "Base64"}], "defaultValue": "base64"}}, {"text": "Variable name", "type": "text", "identifier": "fileVariableName", "properties": {"required": false, "defaultValue": "convertedFile", "regex": "^[a-zA-Z0-9_]*$"}}], "apiCall": {"url": "/ai/api/internal/workspaces/{{global.workspaceId}}/actions/convertFile", "internal": true, "method": "POST", "body": {"fileUrl": "{{thisStep.file.path}}", "fileName": "{{thisStep.file.name}}", "outputFilename": "{{thisStep.outputFilename}}", "outputFormat": "{{thisStep.outputFormat}}"}, "bodyProperties": {"fileName": {"nullable": true}, "outputFilename": {"nullable": true}}, "response": {"type": "json", "properties": {"items": [{"identifier": "name", "type": "text"}, {"identifier": "path", "type": "text"}, {"identifier": "content", "type": "text"}]}}}, "variableMappings": [{"type": "files", "identifier": "{{thisStep.fileVariableName}}", "value": "{{thisStep.response}}"}]}}}]