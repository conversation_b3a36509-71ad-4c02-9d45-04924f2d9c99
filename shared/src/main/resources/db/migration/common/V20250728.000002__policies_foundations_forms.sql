CREATE POLICY foundation_isolation_policy_all ON foundations
    AS RESTRICTIVE
    FOR ALL
    USING (((CURRENT_SETTING('app.is_system_user'::TEXT, TRUE))::BOOLEAN = TRUE) OR
           (id IN (SELECT subject_resource_permissions.resource_id
                   FROM subject_resource_permissions
                   -- see ResourceType and ResourceTypeIds for details
                   WHERE ((subject_resource_permissions.resource_type = 1) AND
                          (subject_resource_permissions.subject_id = (COALESCE(
                                  NULLIF(CURRENT_SETTING('app.current_principal_id'::TEXT, TRUE), ''::TEXT),
                                  '-1'::TEXT))::INTEGER)))));


CREATE POLICY form_isolation_policy_all ON forms
    AS RESTRICTIVE
    FOR ALL
    USING (((CURRENT_SETTING('app.is_system_user'::TEXT, TRUE))::BOOLEAN = TRUE) OR
           (id IN (SELECT subject_resource_permissions.resource_id
                   FROM subject_resource_permissions
                   -- see ResourceType and ResourceTypeIds for details
                   WHERE ((subject_resource_permissions.resource_type = 2) AND
                          (subject_resource_permissions.subject_id = (COALESCE(
                                  NULLIF(CURRENT_SETTING('app.current_principal_id'::TEXT, TRUE), ''::TEXT),
                                  '-1'::TEXT))::INTEGER)))));

