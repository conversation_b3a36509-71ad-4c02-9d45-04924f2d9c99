-- this is a table to store which resources a subject can see
-- derived from spicedb system
-- used in row leve security to only show resources a subject can see
DROP TABLE IF EXISTS subject_resource_permissions;

CREATE TABLE subject_resource_permissions
(
    tenant_id     BIGINT NOT NULL,
    subject_id    BIGINT NOT NULL,
    resource_id   BIGINT NOT NULL,
    resource_type INT    NOT NULL
);

CREATE INDEX idx_tenant_id_subject_id ON subject_resource_permissions (tenant_id, subject_id);
CREATE INDEX idx_tenant_id_subject_id_resource_type ON subject_resource_permissions (tenant_id, subject_id, resource_type);

