-- add visibility column to foundation table
ALTER TABLE foundations
    ADD COLUMN visibility TEXT NOT NULL DEFAULT 'INHERIT';

CREATE TABLE foundation_members
(
    id            BIGINT GENERATED BY DEFAULT AS IDENTITY
        PRIMARY KEY,
    foundation_id BIGINT    NOT NULL,
    user_id       BIGINT    NOT NULL
        CONSTRAINT fk_foundation_members_user_id
            REFERENCES users
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    status        TEXT      NOT NULL,
    access_level  TEXT      NOT NULL,
    created_at    TIMESTAMP NOT NULL,
    updated_at    TIMESTAMP NOT NULL,
    tenant_id     BIGINT    NOT NULL
        CONSTRAINT fk_foundation_members_tenant_id__id
            REFERENCES tenants
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT foundation_members_unique
        UNIQUE (foundation_id, user_id)
);

-- add visibility column to form table
ALTER TABLE forms
    ADD COLUMN visibility TEXT NOT NULL DEFAULT 'INHERIT';

CREATE TABLE form_members
(
    id           BIGINT GENERATED BY DEFAULT AS IDENTITY
        PRIMARY KEY,
    form_id      BIGINT    NOT NULL,
    user_id      BIGINT    NOT NULL
        CONSTRAINT fk_form_members_user_id
            REFERENCES users
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    status       TEXT      NOT NULL,
    access_level TEXT      NOT NULL,
    created_at   TIMESTAMP NOT NULL,
    updated_at   TIMESTAMP NOT NULL,
    tenant_id    BIGINT    NOT NULL
        CONSTRAINT fk_form_members_tenant_id__id
            REFERENCES tenants
            ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT form_members_unique
        UNIQUE (form_id, user_id)
);