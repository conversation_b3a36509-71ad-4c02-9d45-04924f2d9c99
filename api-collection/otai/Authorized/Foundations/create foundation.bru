meta {
  name: create foundation
  type: http
  seq: 2
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/foundations
  body: json
  auth: none
}

body:json {
  { 
    "name": "UNIQUE NAME",
    "key": "UNIQUE KEY",
    "foundationConfigurationId": "{{foundationConfigurationId}}",
    "workspaceId": "{{workspaceId}}",
    "parentId": "{{parentId}}",
    "visibility": "INHERIT"
  }
}

script:pre-request {
    const { customNanoId } = require("./customNanoId");
    const body = req.getBody();
  
    req.setBody({
      ...body,
      name: customNanoId(3),
      key: customNanoId(5).toUpperCase()
    })
  
}

script:post-response {
  if (res.status==200) {
    let data = res.getBody();
    bru.setVar("foundationId", data.id);
  }
}
