meta {
  name: search foundations
  type: http
  seq: 1
}

get {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/foundations?search=
  body: none
  auth: none
}

script:post-response {
  if (res.status === 200) {
    let data = res.getBody();
    if (data.items.length>0) {
      let foundation = data.items[0];
      bru.setVar("foundationKey", foundation.key);
      bru.setVar("foundationId", foundation.id);
      bru.setVar("parentId", foundation.parentId);
      bru.setVar("foundationConfigurationId", foundation.foundationConfigurationId);
    }
  }
}
