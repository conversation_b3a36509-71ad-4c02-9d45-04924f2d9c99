meta {
  name: search forms
  type: http
  seq: 2
}

get {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/forms?pageSize=50&search=&sort=name,asc&sort=metadata.updatedAt,desc
  body: none
  auth: none
}

params:query {
  pageSize: 50
  search: 
  sort: name,asc
  sort: metadata.updatedAt,desc
}

script:post-response {
  if (res.status === 200) {
    let data = res.getBody();
    if (data.items.length>0) {
      let form = data.items[0];
      bru.setVar("formConfigurationId", form.formConfigurationId);
      bru.setVar("foundationId", form.foundationId);
      bru.setVar("seriesId", form.seriesId);
      bru.setVar("intervalId", form.intervalId);
      bru.setVar("parentId", form.foundation.parentId);
      bru.setVar("foundationConfigurationId", form.foundation.foundationConfigurationId);
    }
  }
}
