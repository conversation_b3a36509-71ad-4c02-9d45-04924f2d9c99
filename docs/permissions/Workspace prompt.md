A workspace is made up of:

# Configuration

* FoundationConfiguration - there is a parent child relationship between FoundationConfigurations. These describe the
  levels of the workspace.
    * An example of a FoundationConfiguration is a "Company" which has "Departments" as children which has "Employees"
      as children.
* FormConfiguration - This defines questions and answers for this FormConfiguration. A FormConfiguration is associated
  with a FoundationConfiguration.
    * An example of a FormConfiguration is "Employee Onboarding" which has questions like "What is your name?" and "What
      is your role?". This FormConfiguration is associated with the "Employee" level of the FoundationConfiguration.
* Series intervals - This defines the intervals at which forms are filled out. For example, a series interval can be set
  to "Monthly" or "Quarterly". This is associated with FormConfigurations.
    * Flows - This defines the steps to process data collected from FormConfigurations. A Flow can take input from a
      FormConfiguration and output to another FormConfiguration or a report.
    * An example of a Flow is "Summarise Yearly Review" which collects data from "Employee Yearly Review"
      FormConfiguration and outputs a summary report.

# Collection

Once the workspace is configured with FoundationConfigurations and FormConfigurations, it can be used to collect data.

* Foundations are instantiated and link to FoundationConfigurations.
    * An example of a Foundation is a specific "ACME Company" instance which has "HR Department" and "Engineering
      Department" as children. It may have "Alice" and "Bob" as employees in the HR Department.
* Forms are instantiated from FormConfigurations and link to FoundationConfigurations.
    * Employee Onboarding forms can be created for "Alice" and "Bob" using the "Employee Onboarding" FormConfiguration.
    * Form can be filled out by users to provide answers to the questions defined in the FormConfiguration.
    * Forms can be linked to series intervals for example "Yearly Review" forms can be created for "Alice" and "Bob"
      using the "Employee Yearly Review" FormConfiguration.

Here is an example workspace structure:

Configuration

```
Company (FoundationConfiguration)
└── Department (FoundationConfiguration)
    └── Employee (FoundationConfiguration)
        └── Yearly Review (FormConfiguration)

Series
└── Interval
    └── Year
        ├── 2021 (Series Interval)
        ├── 2022 (Series Interval)
        └── 2023  (Series Interval)
        
FormConfiguration
└── Employee Onboarding (FormConfiguration)
    ├── What is your name? (Question)
    ├── What is your role? (Question)
    └── Employee (FoundationConfiguration)
└── Yearly Review (FormConfiguration)
    ├── What are your goals for the year? (Question)
    ├── What challenges did you face? (Question)
    └── Employee (FoundationConfiguration)
    
    
Flows
└── Summarise Yearly Review (Flow)
    ├── Input: Employee Yearly Review (FormConfiguration)
    ├── Output: Summary Report (FormConfiguration)
    └── Steps:
        ├── Collect data from Employee Yearly Review
        ├── Process data to generate summary
        └── Output summary report
```    

Collection

``` 
Acme (Foundation)
    ├── HR Department (Foundation)
    │   ├── Alice (Foundation)
    │   │   ├── Employee Onboarding (Form) Series Interval: 2022
    │   │   └── Employee Yearly Review (Form) Series Interval: 2023    
    │   └── Bob (Foundation)
    │       └── Employee Onboarding (Form) Series Interval: 2023
    └── Engineering Department (Foundation)
        ├── Charlie (Foundation)
        │   └── Employee Onboarding (Form) Series Interval: 2023
        └── Dana (Foundation)
            └── Employee Onboarding (Form) Series Interval: 2023
```

# Permissions

Users are allocated permissions to access and modify the workspace. Permissions can be set at different levels:

* Workspace level - This allows users to access the entire workspace and perform actions like creating, modifying, or
  deleting FoundationConfigurations, FormConfigurations, and Series Intervals.
* Foundation level - This allows users to access and modify specific Foundations within the workspace. For example,
  a user may have permission to access the "HR Department" Foundation but not the "Engineering Department" Foundation.
* Form level - This allows users to access and modify specific Forms within the workspace. For example,
  a user may have permission to fill out the "Employee Onboarding" Form but not the "Yearly Review" Form.
* Series level - This allows users to access and modify specific Series Intervals within the workspace. For example,
  a user may have permission to create or modify "Yearly Review" Series Intervals but not "Monthly Review" Series
  Intervals.
