Given a PET entity table

```
create table public.pet
(
    id        bigserial,
    parent_id bigint
);
```

and a user to entity permissions table

```
create table public.user_entity_permissions
(
    user_id          bigint not null,
    entity_id        bigint not null,
    entity_type      text,
    permissions      text,
    stop_inheritance boolean default false
);
```

We can query for a users permissions on a specific entity using the following SQL query:

```sql
WITH RECURSIVE PetPermissions AS (
    -- Base case: Include all pets and their direct permissions (or NULL if no entry exists)
    SELECT p.id                            AS pet_id,
           p.parent_id,
           uep.stop_inheritance            AS user_stop_inheritance,
           COALESCE(uep.permissions, NULL) AS permissions
    FROM public.pet p
             LEFT JOIN
         public.user_entity_permissions uep
         ON
             p.id = uep.entity_id AND uep.entity_type = 'pet' AND uep.user_id = :specific_user_id

    UNION ALL

    -- Recursive case: Inherit permissions from the parent if no direct permissions exist and inheritance is not stopped
    SELECT p.id                                      AS pet_id,
           p.parent_id,
           uep.stop_inheritance                      AS user_stop_inheritance,
           COALESCE(uep.permissions, pp.permissions) AS permissions
    FROM public.pet p
             LEFT JOIN
         public.user_entity_permissions uep
         ON
             p.id = uep.entity_id AND uep.entity_type = 'pet' AND uep.user_id = :specific_user_id
             INNER JOIN
         PetPermissions pp
         ON
             p.parent_id = pp.pet_id
    WHERE NOT user_stop_inheritance
       OR user_stop_inheritance IS NULL)
SELECT pet_id,
       parent_id,
       permissions
FROM PetPermissions
WHERE permissions IS NOT NULL;
```

If we create the following entity graph:

```
+ 1
    + 2
+ 10
    + 11
    + 12
    + 13
        + 14
            + 15
        + 16
            + 17
+ 20
    + 21
        + 22
            + 23
```

We can insert the following permissions:

```
User 1 
 - has permissions on 10 which propagates to 11, 12, 13, 14, and 15

User 2 
 - has permissions on 13 which propagates to 14, 15, and 16, 17
 - has permissions on 16 which does not propagate thereby stopping inheritance to 17

User 3
 - has permissions on 2 which does not propagate

User 4
 - has permissions on 21 which does not propagate
```

We can insert the following data into the tables:

```sql
-- entity graph
INSERT INTO public.pet (id, parent_id)
VALUES (1, NULL),
       (2, 1),
       (10, NULL),
       (11, 10),
       (12, 10),
       (13, 10),
       (14, 13),
       (15, 14),
       (16, 13),
       (17, 16),
       (20, NULL),
       (21, 20),
       (22, 21),
       (23, 22);

-- user permissions
INSERT INTO public.user_entity_permissions (user_id, entity_id, entity_type, permissions, stop_inheritance)
VALUES (1, 10, 'pet', 'read', FALSE),
       (2, 13, 'pet', 'read', FALSE),
       (2, 16, 'pet', 'read', TRUE),
       (3, 2, 'pet', 'read', TRUE),
       (4, 21, 'pet', 'read', TRUE);
```

When we run the permissions query

- for user 1 we expect the following results:

```
    pet_id | parent_id | permissions
    ---------+-----------+------------
    10      | NULL      | read
    11      | 10        | read
    12      | 10        | read
    13      | 10        | read
    14      | 13        | read
    15      | 14        | read
    16      | 13        | read
    17      | 16        | read
```

- for user 2 we expect the following results:

```
    pet_id | parent_id | permissions
    ---------+-----------+------------
    13      | 10        | read
    14      | 13        | read
    15      | 14        | read
    16      | 13        | read
```

- for user 3 we expect the following results:

```
    pet_id | parent_id | permissions
    ---------+-----------+------------
    2       | 1         | read
```

- for user 4 we expect the following results:

```
    pet_id | parent_id | permissions
    ---------+-----------+------------
    21      | 20        | read
```

We can use this query to check permissions for any user on any entity in the `pet` table, allowing us to manage and
verify permissions effectively across a hierarchical entity structure.
We can join to this CTE to filter rows in forms and foundations tables

- allowing us to filter, paginate and sort based on permissions

We can also use this query to check for permissions such as

- share
- edit
- view
- delete
- comment
- run flow

These permissions could be fine grained permissions OR roles OR a mixture of both depending on how we want to use it.

- we could change the permissions in a role without having to update these tables

The permissions table is generic and can be used for any entity type by changing the `entity_type` field in the
`user_entity_permissions` table.

It would be up to the application logic to manage the permissions table inserting and deleting properly.

If someone has access to a top level entity, we don't have to insert permissions if they are inherited from the top
level entity.

Consider a foundation configuration hierarchy

```text
+ Company
    + Department
        + Team
            + User
```

If someone creates a new team,
we would need to insert a new row in the `user_entity_permissions` table
for the team, but we would not need to insert a row for the department or company if they already have permissions on
those entities.

Thoughts

- maybe rename `stop_inheritance` to `propagate`

To optimize the performance of this permissions system for large datasets, we could consider the following strategies:

### 1. **Indexing**

- Add indexes to frequently queried columns like `entity_id`, `user_id`, `entity_type`, and `parent_id` to speed up
  lookups and joins.

```sql
CREATE INDEX idx_user_entity_permissions_user_id ON public.user_entity_permissions (user_id);
CREATE INDEX idx_user_entity_permissions_entity_id ON public.user_entity_permissions (entity_id);
CREATE INDEX idx_pet_parent_id ON public.pet (parent_id);
```

### 2. **Materialized Views**

- Use materialized views to precompute and store permissions for entities, reducing the need for recursive queries at
  runtime. Refresh the view periodically or on-demand.

```sql
CREATE MATERIALIZED VIEW user_permissions_view AS
WITH RECURSIVE PetPermissions AS (
    -- Your recursive query here
)
SELECT *
FROM PetPermissions;
```

### 3. **Caching**

- Cache computed permissions at the application level (e.g., in Redis) to avoid repeated database queries for the same
  user and entity.

### 4. **Batch Processing**

- Precompute and store inherited permissions in a denormalized table during off-peak hours. Update this table
  incrementally when changes occur.

### 5. **Limit Recursive Depth**

- If the hierarchy is very deep, limit the recursion depth in the query to avoid performance degradation.

```sql
OPTION
    (MAXRECURSION 100); -- For SQL Server
```

### 6. **Partitioning**

- Partition large tables like `user_entity_permissions` and `pet` by `entity_type` or `user_id` to improve query
  performance.

### 7. **Optimize Recursive Query**

- Use `NOT EXISTS` or `EXISTS` instead of `LEFT JOIN` where applicable to reduce unnecessary joins.
- Avoid `COALESCE` in recursive queries if default values can be handled elsewhere.

### 8. **Database Tuning**

- Analyze and tune your database configuration (e.g., memory allocation, query planner settings) for optimal
  performance.

### 9. **Asynchronous Updates**

- For real-time systems, asynchronously update permissions in the background instead of recalculating them on every
  query.

### 10. **Query Simplification**

- Simplify the recursive query by breaking it into smaller, more manageable steps or using temporary tables for
  intermediate results.

### Best Practices for Managing Permissions in a Large Application

1. **Role-Based Access Control (RBAC)**
    - Group permissions into roles (e.g., Admin, Editor, Viewer) to simplify management.
    - Assign roles to users instead of individual permissions.

2. **Hierarchical Permissions**
    - Use inheritance to propagate permissions through entity hierarchies, reducing redundancy.
    - Allow overrides and stop inheritance where necessary.

3. **Fine-Grained Permissions**
    - Support both coarse-grained (e.g., role-based) and fine-grained (e.g., per-entity) permissions for flexibility.

4. **Centralized Permission Management**
    - Store permissions in a centralized system or service to ensure consistency across the application.

5. **Caching**
    - Cache computed permissions for frequently accessed entities to reduce database load.

6. **Audit Logging**
    - Log permission changes and access attempts for auditing and debugging purposes.

7. **Least Privilege Principle**
    - Grant users the minimum permissions required to perform their tasks.

8. **Dynamic Permission Evaluation**
    - Use dynamic queries or rules to evaluate permissions at runtime for complex scenarios.

9. **Scalability**
    - Use indexing, partitioning, and materialized views to optimize performance for large datasets.

10. **Testing and Validation**
    - Regularly test permission rules to ensure they work as expected.
    - Validate data to prevent incorrect or inconsistent permission assignments.

11. **Separation of Concerns**
    - Keep permission logic separate from business logic to improve maintainability.

12. **Documentation**
    - Document the permission model, including roles, rules, and inheritance behavior, for clarity and ease of use.