package services.oneteam.ai.app

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier
import com.auth0.jwt.algorithms.Algorithm
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.security.KeyFactory
import java.security.interfaces.RSAPublicKey
import java.security.spec.X509EncodedKeySpec
import java.util.*
import kotlin.time.Clock
import kotlin.time.Duration.Companion.days
import kotlin.time.ExperimentalTime
import kotlin.time.toJavaDuration
import kotlin.time.toJavaInstant

class JwtConfigurer(publicKey: String) {
    val verifier: JWTVerifier

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    init {
        val publicKeyContent = publicKey
            .replace("-----BEGIN PUBLIC KEY-----", "")
            .replace("-----END PUBLIC KEY-----", "")
            .replace("\n", "")
            .replace("\\s+".toRegex(), "")
        val keyBytes = Base64.getDecoder().decode(publicKeyContent)
        val keyFactory = KeyFactory.getInstance("RSA")
        val processedPublicKey = keyFactory.generatePublic(X509EncodedKeySpec(keyBytes)) as RSAPublicKey
        val algorithm = Algorithm.RSA256(processedPublicKey, null)
        verifier = JWT.require(algorithm).build()
    }


    @OptIn(ExperimentalTime::class)
    fun validateJWT(token: String) {
        try {
            val expiresAt = verifier.verify(token).expiresAt
            if (expiresAt == null) {
                logger.warn("Service token does not have an expiry")
            } else if (expiresAt.toInstant().minus(7.days.toJavaDuration()) < Clock.System.now().toJavaInstant()) {
                logger.warn("***** Service token expires within 7 days at $expiresAt")
                logger.warn("***** Service token expires within 7 days at $expiresAt")
                logger.warn("***** Service token expires within 7 days at $expiresAt")
            } else {
                logger.info("Service token is valid until $expiresAt")
            }
        } catch (e: Exception) {
            logger.error("Error verifying JWT token: ${e.message}")
        }
    }
}