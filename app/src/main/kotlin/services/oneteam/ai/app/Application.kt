package services.oneteam.ai.app

import db.migration.common.V20250727_1__spicedb_schema
import io.ktor.serialization.kotlinx.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.config.*
import io.ktor.server.netty.*
import io.ktor.server.plugins.cors.routing.*
import io.ktor.server.plugins.requestvalidation.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.resources.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.sessions.*
import io.ktor.server.websocket.*
import io.ktor.util.*
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.instrumentation.ktor.v3_0.KtorServerTelemetry
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.jetbrains.exposed.sql.transactions.transaction
import org.koin.ktor.ext.inject
import org.koin.ktor.plugin.Koin
import org.slf4j.LoggerFactory
import services.oneteam.ai.app.domains.actions.internalActionEndpoints
import services.oneteam.ai.app.domains.auth.AuthService
import services.oneteam.ai.app.domains.auth.authenticatedEndpoints
import services.oneteam.ai.app.domains.auth.tenantApiKeyEndpoints
import services.oneteam.ai.app.domains.auth.unauthenticatedEndpoints
import services.oneteam.ai.app.domains.collection.*
import services.oneteam.ai.app.domains.document.documentEndpoints
import services.oneteam.ai.app.domains.document.documentEventsEndpoints
import services.oneteam.ai.app.domains.event.eventEndpoints
import services.oneteam.ai.app.domains.flow.*
import services.oneteam.ai.app.domains.flowStepTypeConfiguration.flowStepTypeConfigurationEndpoints
import services.oneteam.ai.app.domains.passVault.toFactoryOptions
import services.oneteam.ai.app.domains.user.userEndpoints
import services.oneteam.ai.app.domains.webhook.externalWebhookEndpoints
import services.oneteam.ai.app.domains.websocket.webSocketEndpoints
import services.oneteam.ai.app.domains.workspace.*
import services.oneteam.ai.app.internal.InternalProxyService
import services.oneteam.ai.app.middlewares.*
import services.oneteam.ai.flow.event.TriggerFlowEventListener
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.pubsub.PubSubService
import services.oneteam.ai.permissions.rebac.LocalRLSIndexSyncService
import services.oneteam.ai.permissions.rebac.SubjectResourcePermissionsRepository
import services.oneteam.ai.permissions.rebac.spicedb.SpiceDbChannelBuilder
import services.oneteam.ai.permissions.rebac.spicedb.SpiceDbRepository
import services.oneteam.ai.shared.*
import services.oneteam.ai.shared.database.DatabaseLive
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.actions.OTStorageService
import services.oneteam.ai.shared.domains.auth.ApiKey
import services.oneteam.ai.shared.domains.auth.ApiKeyRepository
import services.oneteam.ai.shared.domains.auth.ApiKeyService
import services.oneteam.ai.shared.domains.collection.form.BlobService
import services.oneteam.ai.shared.domains.collection.form.FormRepository
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.event.EventDispatcher
import services.oneteam.ai.shared.domains.event.EventService
import services.oneteam.ai.shared.domains.event.InMemoryEventQueue
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationEntity
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationRepository
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.passvault.CachedKeyVaultService
import services.oneteam.ai.shared.domains.passvault.KeyVaultServiceFactory
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.tenant.TenantRepository
import services.oneteam.ai.shared.domains.tenant.TenantService
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.domains.user.UserService
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariableRepository
import services.oneteam.ai.shared.domains.workspace.variable.WorkspaceVariableService
import services.oneteam.ai.shared.domains.workspace.variable.secured.WorkspaceSecuredValueRepository
import services.oneteam.ai.shared.domains.workspace.variable.secured.WorkspaceSecuredValueService
import services.oneteam.ai.shared.permissions.*
import java.sql.Connection
import java.util.*
import javax.sql.DataSource
import kotlin.time.Clock
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import kotlin.time.ExperimentalTime
import kotlin.time.Instant

fun main(args: Array<String>) {
    io.ktor.server.netty.EngineMain.main(args)
}

@OptIn(ExperimentalSerializationApi::class, ExperimentalTime::class)
fun Application.module() {

    // TODO: convert the rest of the configuration over to the new 3.2 way separately
    val debugConfig = property<DebugConfig>("debug")
    val spiceDbConfig = property<SpiceDbConfig>("spicedb")

    val logger = LoggerFactory.getLogger("services.oneteam.ai.app.Application")
    install(Koin) {
        ApplicationConfig("application.yaml")
        modules(appModule)
    }
    setupConfig()
    val appConfig by inject<AppConfig>()

    val flowDispatcher = CustomDispatcher(
        "flow-runner", appConfig.flows.numberOfThreads, appConfig.flows.maxConcurrentCoroutines
    )

    val spiceDbChannel =
        SpiceDbChannelBuilder(spiceDbConfig.token, spiceDbConfig.target, spiceDbConfig.usePlaintext).build()

    this.monitor.subscribe(ApplicationStopped) {
        safeShutdown({ spiceDbChannel.close() }, "Error closing SpiceDB channel", logger)
        safeShutdown({ flowDispatcher.shutdown() }, "Error shutting down flow dispatcher", logger)
        logger.info("Application stopped for ${appConfig.websiteName}")
    }

    install(Resources)
    install(io.ktor.server.plugins.contentnegotiation.ContentNegotiation) {
        json(Json {
            encodeDefaults = true
        })
    }

    if (appConfig.telemetry.enabled) {
        install(KtorServerTelemetry) {
            setOpenTelemetry(GlobalOpenTelemetry.get())
        }
    }

    val constraintMapping =
        Json.decodeFromStream<Array<ConstraintMapping>>(this::class.java.getResourceAsStream("/database_constraint_to_localization_mapping.json")!!)

    install(StatusPages) {
        configureStatusPages(constraintMapping)
    }

    val database = DatabaseLive(appConfig.databaseConfig)
    val dictionary = ResourceBundle.getBundle("dictionary")


    val blobService =
        BlobService(appConfig.storage.accountName, appConfig.storage.accessKey, appConfig.storage.uploadContainerName)

    val checks = Checks()

    val subjectResourcePermissionsRepository =
        SubjectResourcePermissionsRepository { TransactionManager.current().connection.connection as Connection }

    val spiceDbRepository = SpiceDbRepository(
        spiceDbConfig.token,
        spiceDbChannel.channel
    )

    val localRLSIndexSyncService = LocalRLSIndexSyncService(
        spiceDbRepository,
        subjectResourcePermissionsRepository,
        ResourceType.entries.filter { it == ResourceType.FOUNDATION || it == ResourceType.FORM || it == ResourceType.WORKSPACE }
            .associate { it.key to it.id }
    )

    val internalProxyService = InternalProxyService(this, appConfig.otaiServiceAccount.token)
    val tenantRepository = TenantRepository()
    val tenantService = TenantService(tenantRepository)
    val userRepository = UserRepository(checks)
    val authService = AuthService(userRepository)
    val relationshipRepository = RelationshipRepository(spiceDbRepository)
    val permissionsService =
        PermissionsService(
            subjectResourcePermissionsRepository,
            relationshipRepository,
            localRLSIndexSyncService
        )
    val foundationRepository = FoundationRepository(permissionsService)

    val formRepository = FormRepository(checks, permissionsService)
    val workspaceRepository = WorkspaceRepository(checks, permissionsService)
    val flowExecutionRepository = FlowExecutionRepository()
    val flowStepTypeConfigurationRepository = FlowStepTypeConfigurationRepository()
    val flowStepTypeConfigurationService = FlowStepTypeConfigurationService(flowStepTypeConfigurationRepository)

    val proxyService = ExternalProxyService(appConfig.otaiServiceAccount.token)

    val otStorageService = OTStorageService(
        OTStorageService.OTStorageServiceConfig(
            storageName = appConfig.oneTeamStorage.storageName,
            containerName = appConfig.oneTeamStorage.containerName,
            sasToken = appConfig.oneTeamStorage.sasToken
        )
    )

    val filePressService = FilePressService(
        appConfig.filePress.url, proxyService, blobService, otStorageService
    )

    val apiDocumentService = ApiDocumentService(
        proxyService, flowStepTypeConfigurationService
    )

    install(EventMiddleware(proxyService))

    val keyVaultService = CachedKeyVaultService(
        KeyVaultServiceFactory(
            appConfig.passVault.keyVaultConfig.toFactoryOptions()
        ).create()
    )
    val workspaceSecuredValueRepository = WorkspaceSecuredValueRepository()
    val workspaceSecuredValueService = WorkspaceSecuredValueService(keyVaultService, workspaceSecuredValueRepository)
    val workspaceVariableRepository = WorkspaceVariableRepository()
    val workspaceVariableService = WorkspaceVariableService(workspaceSecuredValueService, workspaceVariableRepository)
    val apiKeyRepository = ApiKeyRepository()
    val apiKeyService = ApiKeyService(keyVaultService, apiKeyRepository)

    val workspaceVersionRepository = WorkspaceVersionRepository()

    val workspaceVersionService =
        WorkspaceVersionService(
            workspaceRepository,
            apiDocumentService,
            workspaceVersionRepository,
            workspaceVariableService,
            checks
        )
    val userService = UserService(userRepository)
    val foundationService = FoundationService(foundationRepository, workspaceVersionService, checks)
    val formService = FormService(
        formRepository,
        workspaceRepository,
        apiDocumentService,
        workspaceVersionRepository,
        blobService,
        foundationService,
        workspaceVersionService,
        checks
    )
    val foundationConfigurationService = FoundationConfigurationService(
        checks,
        dictionary,
        foundationRepository,
    )

    val workspaceUserRepository =
        WorkspaceUserRepository(checks, workspaceRepository, userRepository, permissionsService, foundationRepository)
    val workspaceUserService = WorkspaceUserService(workspaceUserRepository)

    val workspaceService =
        WorkspaceService(
            workspaceRepository,
            foundationService,
            apiDocumentService,
            foundationConfigurationService,
            workspaceVersionService,
            formService,
            workspaceUserService,
            checks,
        )

    // flows
    val flowExecutionService = FlowExecutionService(
        flowExecutionRepository,
        workspaceVariableService,
        apiDocumentService,
        proxyService,
        internalProxyService,
        filePressService,
        workspaceVersionService,
        formService,
        foundationService,
        flowStepTypeConfigurationService,
        flowDispatcher,
        appConfig.toggles.useExecutionStepFactoryV1,
        appConfig.flows.includeLogging,
        appConfig.flows.skipStepUpdates,
        appConfig.flows.skipVariableUpdates,
        appConfig.flows.skipSubFlowFlowUpdates,
        appConfig.flows.jsonataTimeoutMs,
        appConfig.flows.jsonataMaxRecursionDepth,
        DynamicFEDRepository(
            appConfig.flows.fedStorageType,
            automergeFEDRepository = AutomergeFEDRepository(apiDocumentService),
            blobStorageFEDRepository = BlobStorageFEDRepository(blobService)
        ),
        workspaceService,
    )

    val syncWorkspace = SyncWorkspace(
        relationshipRepository,
        formRepository,
        workspaceUserRepository,
        foundationRepository
    )
    val syncUser = SyncUser(
        localRLSIndexSyncService = localRLSIndexSyncService,
        userRepository = userRepository
    )

    val syncTenant = SyncTenant(
        workspaceRepository = workspaceRepository,
        foundationRepository = foundationRepository,
        syncWorkspace = syncWorkspace,
        syncUser = syncUser,
        tenantRepository = tenantRepository
    )

    // connect to the database - this will also initialize the database if it does not exist in the case of tests using H2
    // since it is the privileged connection that initializes H2. See app/src/test/resources/application.yaml
    val datasource: DataSource = database.privileged.dataSource
    datasource.connection.use { conn ->
        // check if the database is up and running
        conn.prepareStatement("SELECT 1").use { statement ->
            statement.executeQuery().use { resultSet ->
                if (resultSet.next()) {
                    logger.info("Database connection is successful.")
                } else {
                    logger.error("Database connection failed.")
                }
            }
        }
    }

    // java migrations use exposed so we need to make sure it's initialized here
    ExposedInitializer().init(appConfig.databaseConfig.audit, database)

    if (appConfig.flyway.enabled) {

        // configure kotlin migrations
        val javaMigrations = if (spiceDbConfig.enabled) listOf(
            V20250727_1__spicedb_schema(spiceDbRepository),
        ) else emptyList()

        // run migrations
        runBlocking {
            val flywayMigration = FlywayMigration()

            // When switching branches in development you may need to repair migrations
            // Add -DFLYWAY_REPAIR=true to your run configuration in the vm options field
            if (System.getProperty("FLYWAY_REPAIR") == "true") {
                logger.warn("Running flyway repair")
                flywayMigration.repair(appConfig.flyway, database.privileged.dataSource, *javaMigrations.toTypedArray())
            }

            flywayMigration.migrate(appConfig.flyway, database.privileged.dataSource, *javaMigrations.toTypedArray())
        }
    }

    // apply row level security policies AFTER migrations in case migrations have changes needed to implement RLS!
    if (appConfig.databaseConfig.privileged.rls) {
        DatabaseVerification("public", database).ensureTenantIsolation()
    }


    // Event Queue
    val eventDispatcher = EventDispatcher(InMemoryEventQueue(), Dispatchers.Default)
    val eventListener = TriggerFlowEventListener(
        workspaceVersionService,
        flowStepTypeConfigurationService,
        flowExecutionService,
        appConfig.flows.runExecutionImmediately,
        timeoutMins = appConfig.flows.timeoutMins
    )
    eventDispatcher.register(eventListener)
    val eventService = EventService(eventDispatcher)

    PubSubService.initialize(
        appConfig.azureWebPubSub.connectionString,
        appConfig.azureWebPubSub.hubName,
        appConfig.azureWebPubSub.tokenExpirySeconds
    )

    eventDispatcher.register(eventListener)

    // seed
    if (appConfig.seedData) {
        //you can chain these seed calls to seed multiple things
        transaction(database.connectSuperUser()) {
            DataSeeder(database).seed<FlowStepType, FlowStepTypeConfigurationEntity>("/StepTypeSeed.json", true)
        }
    }

    install(CORS) {
        runBlocking {
            configureByHosts(
                tenantService.findAll().flatMap { listOf(it.originUrl, it.internalUrl) })
        }
    }
    install(DevToolsPlugin) {
        applicationConfig = appConfig
    }
    install(CurrentTenantPlugin) {
        tenantServiceConfig = tenantService
        applicationConfig = appConfig
    }
    install(IgnoreTrailingSlash)
    install(RequestValidation) {
        configureRequestValidation()
    }
    install(Sessions) {
        val secretEncryptKey = hex(appConfig.cookie.secretEncryptKey)
        val secretSignKey = hex(appConfig.cookie.secretSignKey)
        cookie<UserSession>(appConfig.cookie.name) {
            cookie.path = "/ai"
            cookie.secure = !appConfig.development
            cookie.maxAge = appConfig.cookie.timeoutDuration
            transform(SessionTransportTransformerEncrypt(secretEncryptKey, secretSignKey))
        }
    }

    val jwtConfigurer = JwtConfigurer(appConfig.jwt.publicKey)
    install(Authentication) {
        bearer(AuthenticationType.BEARER.value) {
            authenticate { tokenCredential ->
                try {
                    authService.login(tokenCredential.token)
                } catch (_: Exception) {
                    null
                }
            }
        }
        bearer(AuthenticationType.API_KEY.value) {
            authenticate { tokenCredential ->
                try {
                    apiKeyService.login(ApiKey.RevealedApiKey(tokenCredential.token))
                } catch (_: Exception) {
                    null
                }
            }
        }
        session<UserSession>(AuthenticationType.SESSION.value) {
            validate { session ->
                logger.debug("session: {}", session)
                //check timeout
                if (Instant.fromEpochMilliseconds(session.maxTime) >= Clock.System.now()) {
                    session
                } else {
                    null
                }
            }
        }
        jwt(AuthenticationType.JWT.value) {
            verifier(jwtConfigurer.verifier)
            validate { credential ->
                JWTPrincipal(credential.payload)
            }
        }
    }

    install(WebSockets) {
        contentConverter = KotlinxWebsocketSerializationConverter(Json)
        pingPeriod = 1.minutes
        timeout = 15.seconds
        maxFrameSize = Long.MAX_VALUE
        masking = false
    }

    routing {
        get(Regex("((ai(/(api)?)?)?)?")) {
            call.respondRedirect(url = "/ai/api/", permanent = true)
        }
        route("/ai/api") {

            defaultEndpoints()
            withTenant {
                withRequestContext {
                    authenticate(AuthenticationType.BEARER.value) {
                        unauthenticatedEndpoints()
                    }
                    authenticate(
                        AuthenticationType.SESSION.value,
                        AuthenticationType.JWT.value,
                        AuthenticationType.API_KEY.value
                    ) {
                        withPrincipal {
                            twoTypeAuthFormEndpoints(formService, workspaceVersionService)
                            twoTypeAuthFoundationEndpoints(foundationService, workspaceVersionService)
                            twoTypeAuthFlowExecutionEndpoints(flowExecutionService)
                            externalWebhookEndpoints(workspaceVersionService)

                            if (debugConfig.enableEndpoints) {
                                debugPermissionsEndpoints(
                                    localRLSIndexSyncService,
                                    tenantService,
                                    syncTenant,
                                    spiceDbRepository
                                )
                                healthEndpoints(
                                    listOf(
                                        SpiceDBHealthCheck(spiceDbRepository),
                                        DatabaseHealthCheck(database.standard.dataSource),
                                        AutoMergeHealthCheck(apiDocumentService)
                                    )
                                )
                            }
                        }
                    }
                    authenticate(AuthenticationType.JWT.value) {
                        withPrincipal {
                            systemEndpoints(apiDocumentService)
                            eventEndpoints(eventService)
                            internalFlowExecutionEndpoints(flowExecutionService)
                            if (appConfig.development) {
                                internalFlowTestEndpoints()
                            }
                            documentEventsEndpoints(formService, workspaceVersionService)
                            internalFoundationEndpoints(foundationService, workspaceVersionService)
                            internalFormEndpoints(formService, workspaceVersionService)
                            internalActionEndpoints(filePressService)
                            internalWorkspaceEndpoints(workspaceService)
                        }
                    }
                    authenticate(AuthenticationType.SESSION.value, AuthenticationType.API_KEY.value) {
                        withPrincipal {
                            workspaceUserEndpoints(workspaceUserService)
                            workspaceEndpoints(workspaceService)
                            workspaceVersionEndpoints(workspaceVersionService)
                            workspaceVariablesEndpoints(workspaceVariableService)
                            foundationEndpoints(
                                foundationService, workspaceVersionService, foundationConfigurationService
                            )
                            formEndpoints(formService, workspaceVersionService, proxyService)
                            flowEndpoints(formService, flowExecutionService, foundationService, workspaceVersionService)
                            flowStepTypeConfigurationEndpoints(flowStepTypeConfigurationService)
                            publicFlowExecutionEndpoints(flowExecutionService)
                            userEndpoints(userService)
                        }
                    }
                    authenticate(AuthenticationType.SESSION.value) {
                        withPrincipal {
                            authenticatedEndpoints()
                            documentEndpoints(apiDocumentService)
                            tenantApiKeyEndpoints(apiKeyService)
                            sessionAuthWorkspaceEndpoints(workspaceService)
                            sessionAuthWorkspaceVariablesEndpoints(workspaceVariableService)
                            webSocketEndpoints()
                            if (appConfig.loadSampleData) {
                                sampleDataEndpoints(
                                    workspaceService,
                                    workspaceVersionService,
                                    foundationRepository,
                                    foundationService,
                                    formService,
                                    requestTimeoutMs = appConfig.sampleDataRequestTimeoutMs
                                )
                            }
                        }
                    }
                }

            }
        }
    }

    if (engine is NettyApplicationEngine) {
        val engine = this.engine as NettyApplicationEngine
        logger.info("Running Netty with configuration: enableHttp2 = ${engine.configuration.enableHttp2} connectionGroupSize = ${engine.configuration.connectionGroupSize} workerGroupSize = ${engine.configuration.workerGroupSize} callGroupSize = ${engine.configuration.callGroupSize}")
    }

    jwtConfigurer.validateJWT(appConfig.otaiServiceAccount.token)

    logger.info("Application started for ${appConfig.websiteName}")

}


fun safeShutdown(action: () -> Unit, errorMessage: String, logger: org.slf4j.Logger) {
    runCatching { action() }
        .onFailure { e -> logger.error("$errorMessage: ${e.message}") }
}
