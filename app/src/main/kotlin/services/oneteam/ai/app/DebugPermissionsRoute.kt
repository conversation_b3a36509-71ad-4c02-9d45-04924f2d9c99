package services.oneteam.ai.app

//import io.ktor.server.routing.*
import io.ktor.resources.*
import io.ktor.server.request.*
import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import org.slf4j.LoggerFactory
import services.oneteam.ai.app.DebugPermissions.Sync
import services.oneteam.ai.permissions.rebac.LocalRLSIndexSyncService
import services.oneteam.ai.permissions.rebac.spicedb.SpiceDbRepository
import services.oneteam.ai.shared.domains.tenant.TenantService
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.permissions.ResourceType
import services.oneteam.ai.shared.permissions.SyncTenant
import services.oneteam.ai.shared.withTenantTransactionScope

// Routes

@Resource("/tools/debug/permissions")
class DebugPermissions {
    @Resource("/sync")
    class Sync(val parent: DebugPermissions = DebugPermissions()) {
        @Resource("/by-subject")
        class BySubject(val parent: Sync = Sync())

        @Resource("/by-resource")
        class ByResource(val parent: Sync = Sync())

        @Resource("/everything")
        class Everything(val parent: Sync = Sync())
    }

    @Resource("/validate")
    class Validate(val parent: DebugPermissions = DebugPermissions()) {
        @Resource("/by-subject")
        class BySubject(val parent: Validate = Validate())

        @Resource("/by-resource")
        class ByResource(val parent: Validate = Validate())

    }

    @Resource("/schema")
    class Schema(val parent: DebugPermissions = DebugPermissions())

}

// Body payloads

@Serializable
data class PermissionsSyncSubjectRequest(
    val tenantId: Long,
    val subjectId: Long,
)

@Serializable
class PermissionsSyncResourceRequest(
    val tenantId: Long,
    val resourceId: Long,
    val resourceType: ResourceType,
)

@Serializable
data class PermissionsValidateSubjectRequest(
    val tenantId: Long,
    val subjectId: Long
)

fun Route.debugPermissionsEndpoints(
    localRLSIndexSyncService: LocalRLSIndexSyncService,
    tenantService: TenantService,
    syncTenant: SyncTenant,
    spiceDbRepository: SpiceDbRepository
) {

    val logger = LoggerFactory.getLogger("debugPermissionsEndpoints")

    get<DebugPermissions.Validate.BySubject> {
        val requestParams = call.receive<PermissionsValidateSubjectRequest>()

        withContext(RequestContext(tenant = tenantService.getById(requestParams.tenantId))) {
            withTenantTransactionScope {
                call.respond(
                    localRLSIndexSyncService.validateViewBySubject(
                        requestParams.tenantId,
                        requestParams.subjectId,
                    )
                )
            }
        }
    }

    /**
     * Copies the permissions from the rebac service into the local index table for a given subject.
     * This is useful for debugging and testing purposes.
     */
    post<Sync.BySubject> {
        val requestParams = call.receive<PermissionsSyncSubjectRequest>()

        withContext(RequestContext(tenant = tenantService.getById(requestParams.tenantId))) {
            withTenantTransactionScope {
                call.respond(
                    localRLSIndexSyncService.syncViewBySubject(
                        requestParams.tenantId,
                        requestParams.subjectId
                    )
                )
            }
        }
    }

    /**
     * Copies the permissions from the rebac service into the local index table for a given resource.
     * This is useful for debugging and testing purposes.
     */
    post<Sync.ByResource> {
        val requestParams = call.receive<PermissionsSyncResourceRequest>()

        withContext(RequestContext(tenant = tenantService.getById(requestParams.tenantId))) {
            withTenantTransactionScope {
                call.respond(
                    localRLSIndexSyncService.syncViewByResource(
                        requestParams.tenantId,
                        requestParams.resourceId,
                        requestParams.resourceType.key,
                    )
                )
            }
        }
    }

    post<Sync.Everything> {
        syncTenant.sync().also {
            logger.info("Permissions sync completed for all tenants")
            call.respondText("Permissions sync completed for all tenants")
        }
    }

    put<DebugPermissions.Schema> {
        spiceDbRepository.updateSchema().also {
            call.respondText("Schema updated")
        }
    }

    get<DebugPermissions.Schema> {
        spiceDbRepository.readSchema().also {
            call.respondText(it)
        }
    }
}
