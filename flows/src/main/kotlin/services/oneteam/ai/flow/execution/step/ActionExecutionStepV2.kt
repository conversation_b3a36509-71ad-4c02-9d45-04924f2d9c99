package services.oneteam.ai.flow.execution.step

import io.ktor.http.ContentType
import kotlinx.serialization.json.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.payload.filter.ProxyResponseFilter
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.flow.expression.conditional.Condition
import services.oneteam.ai.flow.expression.conditional.Expression
import services.oneteam.ai.flow.expression.conditional.mapOperator
import services.oneteam.ai.flow.support.FlowMDC
import services.oneteam.ai.shared.common.OTAIHeaders
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.proxy.includeInternalServiceAccount
import services.oneteam.ai.shared.helpers.getContent
import services.oneteam.ai.shared.otSerializer

/**
 * This class is responsible for executing an action step in the flow execution. Actions are calls to APIs. The response
 * will be put into the flow context and can be used in the next steps. Variable mappings allow for extracting values from
 * the response and putting them into the flow context variables.
 *
 * Sub-steps:
 *
 * 1a. inputs - replace placeholders and add to the context in `thisStep`.
 * 1b. apiCall - replace placeholders in the step type configuration template
 * 2.  execute the api call
 * 3.  process the response - add response to the context in `thisStep`
 * 4.  process variable mappings - add to the context as variables
 *
 * An action step configuration looks like this:
 *
 * ```json
 *   {
 *     "id": "1cwpKek19oNQTJSRCiTvl",
 *     "name": "Select form",
 *     "next": "gbRQdKuuGBgMU896mni-l",
 *     "properties": {
 *       "inputs": {
 *         // never jsonata expressions
 *         "continueFlowIfNotFound": "true",
 *         "formConfigurationId": "gqqgE6trl8d5GQsL4e8b4",
 *         "formVariableName": "CPForm",
 *         "foundationId": "{{foundation.id}}"
 *       },
 *       "typePrimaryIdentifier": "selectForm"
 *     },
 *     "variant": "action"
 *   }
 *   ```
 *
 * Step type configuration comes from the database, matching the `typePrimaryIdentifier` in the step configuration:
 * ```json
 *   {
 *     "primaryIdentifier": "selectForm",
 *     "name": "Select form",
 *     "description": "",
 *     "type": "action",
 *     "properties": {
 *       "icon": {
 *         "name": "search"
 *       },
 *       "isLocked": true,
 *       "isHidden": false,
 *       "configuration": {
 *         "content": [
 *           {
 *             "text": "Foundation ID",
 *             "type": "variable",
 *             "identifier": "foundationId",
 *             "properties": {
 *               "type": "text",
 *               "required": true
 *             }
 *           },
 *           {
 *             "text": "Form configuration ID",
 *             "type": "variable",
 *             "identifier": "formConfigurationId",
 *             "properties": {
 *               "type": "select",
 *               "required": true,
 *               "properties": {
 *                 "dynamicOptions": {
 *                   "tag": "formConfigurationId"
 *                 }
 *               }
 *             }
 *           },
 *           {
 *             "text": "Series interval ID",
 *             "type": "variable",
 *             "identifier": "intervalId",
 *             "properties": {
 *               "type": "select",
 *               "required": false,
 *               "defaultValue": "",
 *               "properties": {
 *                 "dynamicOptions": {
 *                   "tag": "intervalConfigurationId",
 *                   "body": {
 *                     "formConfigurationId": "{{thisStep.formConfigurationId}}"
 *                   }
 *                 }
 *               }
 *             }
 *           },
 *           {
 *             "text": "Form variable name",
 *             "type": "variable",
 *             "identifier": "formVariableName",
 *             "properties": {
 *               "type": "text",
 *               "required": false,
 *               "properties": {
 *                 "regex": "^[a-zA-Z0-9_]*$",
 *                 "defaultValue": "form__step_{{thisStep.id}}"
 *               }
 *             }
 *           },
 *           {
 *             "text": "Continue flow if form is not found",
 *             "type": "select",
 *             "identifier": "continueFlowIfNotFound",
 *             "properties": {
 *               "required": true,
 *               "options": [
 *                 {
 *                   "value": "true",
 *                   "label": "Yes"
 *                 },
 *                 {
 *                   "value": "false",
 *                   "label": "No"
 *                 }
 *               ],
 *               "defaultValue": "true"
 *             }
 *           }
 *         ],
 *         "apiCall": {
 *           "url": "/ai/api/forms/select-many",
 *           "internal": true,
 *           "method": "GET",
 *           "body": {
 *             "workspaceId": "{{global.workspaceId}}",
 *             "foundationId": "{{thisStep.foundationId}}",
 *             "formConfigurationId": "{{thisStep.formConfigurationId}}",
 *             "intervalId": "{{thisStep.intervalId}}",
 *             "allowNull": "{{thisStep.continueFlowIfNotFound}}"
 *           },
 *           "response": {
 *               "type": "json",
 *               "properties": {
 *               "items": [
 *                  {
 *                  "type": "list",
 *                  "identifier": "forms",
 *                  "properties": {
 *                      "items": [
 *                      {
 *                          "type": "form.minimal",
 *                          "identifier": "form"
 *                      }
 *                      ]
 *                  }
 *                  }
 *              ]
 *              }
 *           }
 *         },
 *         "variableMappings": [
 *           {
 *             "type": "form.{{thisStep.formConfigurationId}}",
 *             "identifier": "{{thisStep.formVariableName}}",
 *             "value": "{{thisStep.response.form.id}}"
 *           }
 *         ]
 *       }
 *     }
 *   }
 * ```
 */
class ActionExecutionStepV2(
    val step: FlowExecution.Step,
    val stepTypeConfiguration: FlowStepTypeConfiguration,
    val contextToJsonObjectBuilder: ContextToJsonObjectBuilder,
    val proxyService: ProxyService,
    val internalProxyService: ProxyService,
    val filePressService: FilePressService,
    val expressionEvaluator: JsonataExpressionEvaluator
) : ExecutionStep {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun populate(context: FlowContextWithLocalStep) {
        logger.trace("Populating action step")
        processInputs(context)
        processApiCall(context)
    }

    private suspend fun processApiCall(context: FlowContextWithLocalStep) {

        val valueProvider = ContextPlaceholderValueProvider(contextToJsonObjectBuilder, context)

        // Get the original API call
        val originalApiCall = stepTypeConfiguration.properties!!.configuration!!.apiCall!!
        val apiCallJson = otSerializer.encodeToJsonElement(originalApiCall)
        val bodyProperties = apiCallJson.jsonObject["bodyProperties"]?.takeIf { it is JsonObject }?.jsonObject
        val result = processJsonElement(null, apiCallJson, valueProvider, bodyProperties, context) as JsonObject

        // Store the result in the flow execution document
        logger.trace("Resolved api call to {}", result)
        step.properties.apiCall = result
    }

    /**
     * Recursively processes a JsonElement, replacing placeholders and evaluating JSONata expressions.
     */
    private suspend fun processJsonElement(
        key: String? = null,
        element: JsonElement,
        valueProvider: ContextPlaceholderValueProvider,
        bodyProperties: JsonObject?,
        context: FlowContextWithLocalStep
    ): JsonElement {
        return when (element) {
            is JsonObject -> {
                val processedMap = element.entries.associate { (key, value) ->
                    key to processJsonElement(key, value, valueProvider, bodyProperties, context)
                }
                JsonObject(processedMap)
            }

            is JsonArray -> {
                val processedList = element.map {
                    processJsonElement(
                        element = it, valueProvider = valueProvider, bodyProperties = bodyProperties, context = context
                    )
                }
                JsonArray(processedList)
            }

            is JsonPrimitive -> {
                // Check if this is a JSONata expression
                val isExpression =
                    key != null && bodyProperties != null && bodyProperties.containsKey(key) && bodyProperties[key]?.takeIf { it is JsonObject }?.jsonObject?.get(
                        "isExpression"
                    )?.jsonPrimitive?.boolean == true

                if (isExpression) {
                    try {
//                        val replacedValueStr = JsonataPlaceHolderReplacer().replacePlaceholders(element.content, valueProvider)
                        val expression = element.content
                        val stepJsonataResolver =
                            StepJsonataResolver(context, contextToJsonObjectBuilder, expressionEvaluator)
                        val result = stepJsonataResolver.resolve(expression)
                        // Evaluate the JSONata expression
//                        val result = JsonataExpressionEvaluator().evaluate(replacedValueStr, emptyMap<String, String>())
                        TypeToJsonElementConverter.toJsonElement(result)
                    } catch (e: Exception) {
                        logger.error("Failed to evaluate JSONata expression: $element", e)
                        otSerializer.parseToJsonElement(
                            StringPlaceholderReplacer().replacePlaceholders(
                                otSerializer.encodeToString(
                                    element
                                ), valueProvider
                            )
                        )
                    }
                } else {
                    otSerializer.parseToJsonElement(
                        StringPlaceholderReplacer().replacePlaceholders(
                            otSerializer.encodeToString(element), valueProvider
                        )
                    )
                }
            }
        }
    }

    private suspend fun processInputs(context: FlowContextWithLocalStep) {
        processInputs(context, contextToJsonObjectBuilder, step, stepTypeConfiguration)
    }

    /**
     *
     * If body is jsonObject, then check the properties for each key
     * If a property is marked as nullable, and the value is an empty string, set it to null.
     *
     * If body is not a jsonObject, return it as is.
     * We need to allow all other types of payload, to allow integration with external systems
     */
    fun getBody(context: JsonElement?, properties: JsonObject?): JsonElement? {
        return when {
            context is JsonObject -> {
                context.let {
                    val mutableContext = context.toMutableMap()
                    properties?.forEach { (key, value) ->
                        value.jsonObject["nullable"]?.jsonPrimitive?.boolean?.takeIf { it }?.let {
                            mutableContext[key]?.takeIf { it is JsonPrimitive && it.jsonPrimitive.isString && it.jsonPrimitive.content == "" }
                                ?.let {
                                    mutableContext[key] = JsonNull
                                }
                        }
                    }
                    JsonObject(mutableContext)
                }
            }

            else -> {
                context
            }
        }
    }

    override suspend fun execute(context: FlowContextWithLocalStep): NextStepId? {

        val apiCall = stepTypeConfiguration.properties?.configuration?.apiCall
            ?: throw IllegalArgumentException("No apiCall found for action step in ${stepTypeConfiguration.primaryIdentifier}")

        // call api
        val response = executeApiCall(if (apiCall.internal) internalProxyService else proxyService, context)

        // handle response
        val continueFlowIfApiFails: Boolean = context.thisStep["continueFlowIfApiFails"]?.getContent() == "true"
        if (!continueFlowIfApiFails && response.status != ProxyService.ProxyEndpointResponseStatus.SUCCESS) {
            if (!apiCall.internal) {
                setResponse(response, context, step, stepTypeConfiguration)
            }
            logger.warn("Proxy Service call did not succeed")
            throw FlowRunnerException("Proxy Service call did not succeed: Error - ${response.error}, - Response ${response.response}")
        }
        setResponse(response, context, step, stepTypeConfiguration)
        return step.next
    }

    private suspend fun setResponse(
        response: ProxyService.ProxyEndpointResponse,
        context: FlowContextWithLocalStep,
        step: FlowExecution.Step,
        stepTypeConfiguration: FlowStepTypeConfiguration
    ) {
        val responseAsJsonElement = otSerializer.parseToJsonElement(response.response!!)
        context.setThisStep("response", responseAsJsonElement)

        // Store the response in the flow execution document
        step.properties.apiCall = JsonObject(step.properties.apiCall!! + ("response" to responseAsJsonElement))

        // Process variable mappings
        processVariableMappings(stepTypeConfiguration.properties!!.configuration, context)
    }

    private suspend fun processVariableMappings(
        configuration: FlowStepType.Properties.Configuration?, context: FlowContextWithLocalStep
    ) {

        val valueProvider = ContextPlaceholderValueProvider(
            contextToJsonObjectBuilder.copy(cacheKey = contextToJsonObjectBuilder.cacheKey + ".after"), context
        )

        // evaluate variable mappings after we have the response from the api call
        val resolvedVariableMappingsString = StringPlaceholderReplacer().replacePlaceholders(
            otSerializer.encodeToString(configuration!!.variableMappings), valueProvider
        )
        val variableMappings: List<FlowStepType.Properties.Configuration.TriggerEventSubscription.VariableMapping> =
            Json.decodeFromString(resolvedVariableMappingsString)

        var variables: List<VariableInstance.Variable> = emptyList()

        variableMappings.forEach { item ->
            // if the variable is a jsonata expression, we need to resolve it
            val hidden = item.properties?.get("hidden")
            if (hidden != null && hidden.getContent() != "false") {
                // Run the condition to check if the variable is hidden
                if (hidden.getContent() == "true") {
                    return@forEach
                }

                // If hidden is typeof Condition
                if (hidden is JsonObject && hidden.containsKey("lhs") && hidden.containsKey("operator")) {
                    // as string
                    val left = hidden["lhs"]?.toString()
                        ?: throw IllegalArgumentException("Condition must have a left hand side (lhs) value")
                    val operator = mapOperator(hidden["operator"]?.jsonPrimitive?.content as String)
                    val right = hidden["rhs"]?.toString()

                    val condition = Condition(
                        Expression(left), operator, right?.let { listOf(Expression(it)) })
                    val expression = condition.toExpression()
                    val isHidden = expressionEvaluator.evaluate(expression, mapOf<Any, Any>())
                    if (isHidden == true) {
                        return@forEach
                    }
                }
            }

            val valueIsExpression = item.properties?.get("valueIsExpression")?.jsonPrimitive?.boolean == true
            val updatedProperties =
                Json.encodeToJsonElement(item.properties?.filter { entry -> entry.key != "hidden" && entry.key != "valueIsExpression" })

            val variable = VariableInstance.Variable(
                type = VariableDataType.fromString(item.type),
                identifier = item.identifier,
                value = item.value,
                properties = if (updatedProperties is JsonObject) updatedProperties.let {
                    otSerializer.decodeFromJsonElement(it)
                } else null)

            if (valueIsExpression && variable.get() is JsonPrimitive && (variable.get() as JsonPrimitive).isString) {
                // resolve the jsonata expression
                val valueAsString = (variable.get() as JsonPrimitive).content
                val result = expressionEvaluator.evaluate(valueAsString, mapOf<Any, Any>())
                variables = variables.plus(variable.copy(value = TypeToJsonElementConverter.toJsonElement(result)))
            } else {
                // if the variable is not a jsonata expression
                variables = variables.plus(variable.copy(value = TypeToJsonElementConverter.toJsonElement(item.value)))
            }
        }

        // add variable mapping to context
        variables.forEach { context.flowContext.set(it) }
    }

    private suspend fun executeApiCall(
        proxy: ProxyService, context: FlowContextWithLocalStep
    ): ProxyService.ProxyEndpointResponse {
        val resolvedApiCall = step.properties.apiCall!!
        val isInternal = resolvedApiCall["internal"]?.jsonPrimitive?.boolean == true

        val contentTypeString = resolvedApiCall["contentType"]?.getContent()
        val contentType: ContentType = contentTypeString?.takeIf { it.isNotBlank() }
            ?.let {
                runCatching { ContentType.parse(it) }.onFailure { e ->
                    logger.warn("Invalid content type string: {}. Defaulting to application/json", it, e)
                }.getOrNull()
            }
            ?: ContentType.Application.Json

        val isMultipart = contentType.match(ContentType.MultiPart.FormData)

        val workspaceId = context.flowContext.workspace.id.value
        val flowConfigId = context.flowContext.global.flowConfigurationId.value
        val executionId = FlowMDC.getFlowExecutionId()
        val headers: MutableMap<String, String> =
            resolvedApiCall["headers"]?.takeIf { it is JsonObject }?.jsonObject?.mapValues { it.value.jsonPrimitive.content }
                ?.toMutableMap() ?: mutableMapOf()
        // Content-Type in headers will not be used, it will be overridden in ExternalProxyService
        headers[OTAIHeaders.X_OTAI_FLOW] = "${workspaceId}:${flowConfigId}:${step.id.value}:${executionId}"

        val requestBody = if (isMultipart) {
            multipartCall(resolvedApiCall, headers)
        } else {
            jsonOrFormCall(isInternal, resolvedApiCall, headers, contentType)
        }

        logger.trace("Request to proxy service is {}", requestBody)
        // TODO: make proxy timeout env variable
        val timeoutMillis = 60_000L
        val response = proxy.call(requestBody, !isInternal, timeoutMillis = timeoutMillis)
        logger.trace("Response from proxy service is {}", response)
        return ProxyResponseFilter().filterProxyResponseByStatusCode(
            context.thisStep["response"] as? JsonArray?, response
        )
    }

    private suspend fun jsonOrFormCall(
        isInternal: Boolean,
        apiInfoObject: JsonObject,
        headers: Map<String, String>,
        contentType: ContentType
    ): ProxyService.ProxyEndpointBody {

        val method = apiInfoObject.getValue("method").jsonPrimitive.content

        val bodyElement = if (method == "GET" && apiInfoObject["body"] == JsonPrimitive("")) {
            null
        } else {
            getBody(apiInfoObject["body"], apiInfoObject["bodyProperties"]?.takeIf { it is JsonObject }?.jsonObject)
        }

        // Content-Type in headers will not be used, it will be overridden in ExternalProxyService
        // We build the body based on the given content type
        // We override the content type in ExternalProxyService based on the body we generated
        val finalContentType = resolveContentType(contentType, bodyElement)

        val proxyBody = buildProxyRequestBody(finalContentType, bodyElement)

        return if (isInternal) {
            ProxyService.ProxyEndpointBody(
                url = ProxyService.buildInternalTenantUrl(apiInfoObject.getValue("url").jsonPrimitive.content),
                method = method,
                headers = headers,
                contentType = finalContentType,
                body = proxyBody,
                queryParams = apiInfoObject["queryParams"].takeIf { it is JsonObject }?.jsonObject?.map { it.key to it.value.jsonPrimitive.content }
                    ?.toMap(),
            ).includeInternalServiceAccount()
        } else {
            ProxyService.ProxyEndpointBody(
                url = apiInfoObject.getValue("url").jsonPrimitive.content,
                method = method,
                headers = headers,
                contentType = finalContentType,
                body = proxyBody,
                queryParams = apiInfoObject["queryParams"]?.takeIf { it is JsonObject }?.jsonObject?.map { it.key to it.value.jsonPrimitive.content }
                    ?.toMap(),
            )
        }
    }

    private fun resolveContentType(contentType: ContentType, bodyElement: JsonElement?): ContentType? {
        return when (contentType) {
            ContentType.Application.Json,
            ContentType.Application.FormUrlEncoded -> contentType

            else -> {
                if (bodyElement == null || bodyElement is JsonNull || (bodyElement is JsonPrimitive && bodyElement.content.isBlank())) {
                    null
                } else {
                    ContentType.Application.Json
                }
            }
        }
    }

    private fun buildProxyRequestBody(
        contentType: ContentType?,
        bodyElement: JsonElement?
    ): ProxyService.ProxyRequestBody? {
        return when (contentType) {
            ContentType.Application.Json -> bodyElement?.let { ProxyService.ProxyRequestBody.Json(it as JsonObject) }
            ContentType.Application.FormUrlEncoded -> bodyElement?.let {
                ProxyService.ProxyRequestBody.FormUrlEncoded((it as JsonObject).mapValues { entry -> entry.value.getContent() })
            }

            else -> null
        }
    }

    private fun multipartCall(apiInfoObject: JsonObject, headers: Map<String, String>): ProxyService.ProxyEndpointBody {

        val body =
            apiInfoObject["body"]?.jsonObject ?: throw IllegalArgumentException("No body found for multipart API call")

        val fileInfos: MutableList<ProxyService.FileInfo> = mutableListOf()

        body["files"]?.takeIf { it is JsonObject }?.jsonObject?.forEach { file ->

            val path = file.value.jsonObject["path"]?.jsonPrimitive?.content
                ?: throw IllegalArgumentException("No file path found for multipart API call")

            val fileName = file.value.jsonObject["name"]?.jsonPrimitive?.content
                ?: throw IllegalArgumentException("No file name found for multipart API call")

            fileInfos += ProxyService.FileInfo(
                fileBytes = filePressService.getFileBytes(path), fileName = fileName, fileKey = file.key
            )
        }

        return ProxyService.ProxyEndpointBody(
            url = apiInfoObject.getValue("url").jsonPrimitive.content,
            method = apiInfoObject.getValue("method").jsonPrimitive.content,
            headers = headers,
            contentType = ContentType.MultiPart.FormData,
            body = ProxyService.ProxyRequestBody.Multipart(fileInfos)
        )
    }
}