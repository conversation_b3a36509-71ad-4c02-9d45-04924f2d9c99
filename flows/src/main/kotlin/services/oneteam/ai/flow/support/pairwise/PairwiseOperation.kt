package services.oneteam.ai.flow.support.pairwise

import services.oneteam.ai.shared.extensions.allDates
import services.oneteam.ai.shared.extensions.allDatesRecursive
import services.oneteam.ai.shared.extensions.allNumbers
import services.oneteam.ai.shared.extensions.allNumbersRecursive
import services.oneteam.ai.shared.extensions.anyDateRecursive
import services.oneteam.ai.shared.extensions.anyNumericRecursive
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import services.oneteam.ai.shared.extensions.maybeParseDate
import services.oneteam.ai.shared.extensions.toNumber

/**
 * Configuration for default values in pairwise operations.
 * see [PairwiseOperation]
 * - [defaultValue]: The value to use as a default.
 * - [test]: A lambda function that takes an array of inputs and returns true if this default value should be used.
 */
data class PairwiseDefaultValueConfig(
    val defaultValue: Any,
    val test: (Array<out Any?>) -> Boolean = { true },
)

enum class DefaultValueInflateOrigin {
    ZERO,
    CONFIG
}

/**
 * Enum representing various pairwise operations that can be performed on inputs.
 * Each operation includes:
 * - [defaultValueConfig]: A list of configurations for default values to use when inputs are missing or of different lengths.
 * - [test]: A lambda function that checks if the operation can be applied to the given inputs.
 * - [function]: The actual operation to perform on the inputs.
 * - [defaultValueInflateOrigin]: Indicates whether the default value for inflating arrays comes from zero or the config.
 *
 * [defaultValueConfig] and [defaultValueInflateOrigin] help determine how to handle missing or mismatched input lengths.
 * These are required because some operations (like MIN and MAX) need different default values based on input types.
 * For MIN and MAX, default value for inflation comes from config for dates, and is 0 for numbers
 */
enum class PairwiseOperation(
    val defaultValueConfig: List<PairwiseDefaultValueConfig>,
    val test: (Array<out Any?>) -> Boolean,
    val function: (Array<out Any?>) -> Any?,
    val defaultValueInflateOrigin: Any? = DefaultValueInflateOrigin.ZERO,
) {

    SUM(
        listOf(PairwiseDefaultValueConfig(0)),
        allNumbers,
        { inputs: Array<out Any?> ->
            toNumber(inputs).reduce { acc, num -> if (acc != null && num != null) acc + num else null }
                ?.convertToNiceBigDecimal()
        }
    ),
    SUBTRACT(
        listOf(PairwiseDefaultValueConfig(0)),
        allNumbers,
        { inputs: Array<out Any?> ->
            toNumber(inputs).reduce { acc, num -> if (acc != null && num != null) acc - num else null }
                ?.convertToNiceBigDecimal()
        }
    ),
    MULTIPLY(
        listOf(PairwiseDefaultValueConfig(1)),
        allNumbers,
        { inputs: Array<out Any?> ->
            toNumber(inputs).reduce { acc, num -> if (acc != null && num != null) acc * num else null }
                ?.convertToNiceBigDecimal()
        }
    ),
    DIVIDE(
        listOf(PairwiseDefaultValueConfig(1)),
        allNumbers,
        { inputs: Array<out Any?> ->
            toNumber(inputs).reduce { acc, num -> if (acc != null && num != null && num.signum() != 0) acc / num else null }
                ?.convertToNiceBigDecimal()
        }
    ),
    MIN(
        // For MIN and MAX, default value for inflation comes from config for dates, and is 0 for numbers
        listOf(
            PairwiseDefaultValueConfig(0) { inputs -> allNumbersRecursive(inputs) },
            PairwiseDefaultValueConfig("") { inputs -> allDatesRecursive(inputs) }
        ),
        { inputs -> allDates(inputs) || allNumbers(inputs) },
        { inputs: Array<out Any?> ->
            when {
                allNumbers(inputs) || anyNumericRecursive(inputs) -> {
                    toNumber(inputs).filterNotNull().minOf { it }.convertToNiceBigDecimal()
                }

                allDates(inputs) || anyDateRecursive(inputs) -> {
                    val dates = inputs.mapNotNull { maybeParseDate(it) }
                    dates.minOrNull()?.toString() ?: ""
                }

                else -> throw IllegalArgumentException("Unsupported input types for pairwise ${MIN.name}")
            }
        },
        DefaultValueInflateOrigin.CONFIG
    ),
    MAX(
        listOf(
            PairwiseDefaultValueConfig(0) { inputs -> allNumbersRecursive(inputs) },
            PairwiseDefaultValueConfig("") { inputs -> allDatesRecursive(inputs) }
        ),
        { inputs -> allDates(inputs) || allNumbers(inputs) },
        { inputs: Array<out Any?> ->
            when {
                allNumbers(inputs) || anyNumericRecursive(inputs) -> {
                    toNumber(inputs).filterNotNull().maxOf { it }.convertToNiceBigDecimal()
                }

                allDates(inputs) || anyDateRecursive(inputs) -> {
                    val dates = inputs.mapNotNull { maybeParseDate(it) }
                    dates.maxOrNull()?.toString() ?: ""
                }

                else -> throw IllegalArgumentException("Unsupported input types for pairwise ${MAX.name}")
            }
        },
        DefaultValueInflateOrigin.CONFIG
    ),
    CONCATENATE(
        listOf(PairwiseDefaultValueConfig("")),
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> -> inputs.joinToString("") }
    ),
    EQ(
        listOf(PairwiseDefaultValueConfig(0)),
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> -> inputs.reduce { acc, num -> acc == num } }
    ),
    GT(
        listOf(PairwiseDefaultValueConfig(0)),
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> ->
            inputs.reduce { acc, num ->
                if (acc is Comparable<*> && num is Comparable<*>) {
                    @Suppress("UNCHECKED_CAST")
                    return@reduce (acc as Comparable<Any>) > (num as Comparable<Any>)
                }
                return@reduce false
            }
        }
    ),
    GTE(
        listOf(PairwiseDefaultValueConfig(0)),
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> ->
            inputs.reduce { acc, num ->
                if (acc is Comparable<*> && num is Comparable<*>) {
                    @Suppress("UNCHECKED_CAST")
                    return@reduce (acc as Comparable<Any>) >= (num as Comparable<Any>)
                }
                return@reduce false
            }
        }
    ),
    LT(
        listOf(PairwiseDefaultValueConfig(0)),
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> ->
            inputs.reduce { acc, num ->
                if (acc is Comparable<*> && num is Comparable<*>) {
                    @Suppress("UNCHECKED_CAST")
                    return@reduce (acc as Comparable<Any>) < (num as Comparable<Any>)
                }
                return@reduce false
            }
        }
    ),
    LTE(
        listOf(PairwiseDefaultValueConfig(0)),
        { inputs: Array<out Any?> -> inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> ->
            inputs.reduce { acc, num ->
                if (acc is Comparable<*> && num is Comparable<*>) {
                    @Suppress("UNCHECKED_CAST")
                    return@reduce (acc as Comparable<Any>) <= (num as Comparable<Any>)
                }
                return@reduce false
            }
        }
    ),
    EOMONTH(
        listOf(
            PairwiseDefaultValueConfig(0) { inputs -> allNumbersRecursive(inputs) },
            PairwiseDefaultValueConfig("") { inputs -> allDatesRecursive(inputs) },
            PairwiseDefaultValueConfig("") { true } // fallback, since EOMonth works only with dates as output
        ),
        { inputs: Array<out Any?> -> inputs.size == 2 && inputs.all { it !is List<*> } },
        { inputs: Array<out Any?> ->
            val start = inputs.getOrNull(0)
            val months = inputs.getOrNull(1)
            val date = when (start) {
                is String -> maybeParseDate(start)

                else -> null
            }
            val monthsInt = when (months) {
                is Number -> months.toDouble().toInt()
                is String -> months.toDoubleOrNull()?.toInt()
                else -> null
            }
            if (date == null || monthsInt == null) {
                ""
            } else {
                val targetMonth = java.time.YearMonth.of(date.year, date.month).plusMonths(monthsInt.toLong())
                targetMonth.atEndOfMonth().toString()
            }
        },
        DefaultValueInflateOrigin.CONFIG
    ),
}
