package services.oneteam.ai.flow.expression.functions

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import java.math.BigDecimal

class MaxPairwiseTest {

    private val maxPairwise = Pairwise(PairwiseOperation.MAX, BigDecimal.ZERO)

    @Nested
    inner class NonPairwiseCases {
        @Test
        fun `max returns the valid date string, ignoring invalid ones`() {
            val result = maxPairwise.perform(listOf("2023-01-01", "invalid-date", "2023-03-01"))
            result shouldBe "2023-03-01"
        }
    }

    @Nested
    inner class MixedTypeCases {
        @Test
        fun `max should handle lists of different lengths with mixed dates and numbers`() {
            /*
            => MAX(["asd", 200], [100])
            => MAX(["asd", 200], [100, 0])
            => [100, 200]
             */
            val result = maxPairwise.perform(
                listOf(
                    listOf("asd", 200),
                    listOf(100)
                )
            )
            result shouldBe listOf(BigDecimal(100), BigDecimal(200))
        }

        @Test
        fun `max should handle lists of different lengths containing dates and invalid strings`() {
            /*
            => MAX(["2023-01-01", "asd"], ["2023-02-01"])
            => MAX(["2023-01-01", "asd"], ["2023-02-01", ""])
            => ["2023-02-01", ""]
             */
            val result = maxPairwise.perform(
                listOf(
                    listOf("2023-02-01", "asd"),
                    listOf("2023-01-01")
                )
            )
            result shouldBe listOf("2023-02-01", "")
        }

        @Test
        fun `max should handle mixed date and number types`() {
            val result = maxPairwise.perform(listOf(listOf(1, "2023-01-01"), listOf(2, "2022-01-01")))
            result shouldBe listOf(BigDecimal(2), "2023-01-01")
        }
    }

    @Nested
    inner class DateInputCases {
        @Test
        fun `max should handle date lists of different lengths`() {
            /*
            => MAX(["2023-01-01", "2023-03-15"], ["2023-02-01"])
            => MAX(["2023-01-01", "2023-03-15"], ["2023-02-01", ""])
            => ["2023-02-01", "2023-03-15"]
             */
            val result = maxPairwise.perform(
                listOf(
                    listOf("2023-01-01", "2023-03-15"),
                    listOf("2023-02-01")
                )
            )
            result shouldBe listOf("2023-02-01", "2023-03-15")
        }

        @Test
        fun `max should handle scalar and list of dates`() {
            val result = maxPairwise.perform(listOf("2023-01-01", listOf("2022-01-01", "2024-01-01")))
            result shouldBe listOf("2023-01-01", "2024-01-01")
        }
    }

    @Nested
    inner class NestedStructureCases {
        @Test
        fun `max should handle nested arrays and dates`() {
            val result = maxPairwise.perform(
                listOf(
                    listOf(listOf("2023-01-01", "2023-02-01")),
                    listOf(listOf("2023-03-01", "2023-01-05"))
                )
            )
            result shouldBe listOf(listOf("2023-03-01", "2023-02-01"))
        }

        @Test
        fun `max should handle deeply nested arrays`() {
            val result = maxPairwise.perform(
                listOf(
                    listOf(listOf(listOf(1, 2), listOf(3, 4))),
                    listOf(listOf(listOf(4, 1), listOf(2, 5)))
                )
            )
            result shouldBe listOf(listOf(listOf(BigDecimal(4), BigDecimal(2)), listOf(BigDecimal(3), BigDecimal(5))))
        }
    }

    @Nested
    inner class EdgeCases {
        @Test
        fun `max should handle empty lists`() {
            val result = maxPairwise.perform(listOf(emptyList<Int>(), emptyList<Int>()))
            result shouldBe emptyList<Int>()
        }
    }
}