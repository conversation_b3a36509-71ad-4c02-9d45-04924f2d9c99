package services.oneteam.ai.flow.expression.functions

import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MaxTest {
    private val fn = Max

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName([])", BigDecimal(0), null, "Returns 0 for an empty array"),
            Spec("$$functionName(2, 3)", BigDecimal(3), null, "Returns the maximum of two numbers"),
            Spec("$$functionName(3, 2, 'a')", BigDecimal(3), null, "Ignores non-numeric values"),
            Spec(
                "$$functionName(-1, \"1\")",
                BigDecimal(1),
                null,
                "Handles numbers as strings and returns the maximum"
            ),
            Spec("$$functionName([1, 2, 3, 4, 5])", BigDec<PERSON><PERSON>(5), null, "Returns the maximum value in an array"),
            Spec(
                "$$functionName([1, 2, 'a', 4, 5])",
                BigDecimal(5),
                null,
                "Returns the maximum value in an array, ignoring non-numeric values"
            ),
            Spec(
                "$$functionName([\"1\", 2, \"3\", 4, 5])",
                BigDecimal(5),
                null,
                "Handles mixed types in an array and returns the maximum"
            ),
            Spec(
                "$$functionName([1, 2, 3], -1)",
                listOf(BigDecimal(1), BigDecimal(2), BigDecimal(3)),
                null,
                "Performs pairwise comparison with a larger number"
            ),
            Spec(
                "$$functionName([1, 2, 3], 4)",
                listOf(BigDecimal(4), BigDecimal(4), BigDecimal(4)),
                null,
                "Performs pairwise comparison with a smaller number"
            ),
            Spec(
                "$$functionName([4, 5], [[1, 2, 3], [5, 4, 3]])",
                listOf(
                    listOf(BigDecimal(4), BigDecimal(5), BigDecimal(3)),
                    listOf(BigDecimal(5), BigDecimal(5), BigDecimal(3))
                ), null,
                "Handles nested arrays and returns the pairwise maximum"
            ),
            Spec("$$functionName(0.1, 0.2)", BigDecimal("0.2"), null, "Returns the maximum of two fractional numbers"),
            Spec(
                "$$functionName('', \"2026-01-01\")",
                "2026-01-01",
                null,
                "Ignores empty string and returns the date string"
            ),
            Spec(
                "$$functionName(['not a date', '2023-01-01'])",
                "2023-01-01",
                null,
                "Returns the valid date string, ignoring invalid ones"
            ),
            Spec(
                "$$functionName([\"2026-01-01\", \"2025-01-01\", \"2024-01-01\"])",
                "2026-01-01",
                null,
                "Returns the maximum date from a list of date strings"
            ),
            Spec(
                "$$functionName(\"2026-01-01\", \"2025-01-01\", \"2024-01-01\")",
                "2026-01-01",
                null,
                "Returns the maximum date from a list of date strings"
            ),
            Spec(
                "$$functionName(\"2026-01-01\", 100, 90)",
                "2026-01-01",
                null,
                "Ignores numbers when finding the maximum of mixed types"
            ),
            Spec(
                "$$functionName(['not-a-date', 'also-bad'])",
                BigDecimal(0),
                null,
                "Returns 0 for all invalid date strings"
            ),
            Spec(
                "$$functionName([true, false, {foo: 'bar'}, 7])",
                BigDecimal(7),
                null,
                "Ignores non-string, non-number types"
            ),
            Spec(
                "$$functionName(['2023-01-01T12:00:00', '2023-01-01T13:00:00'])",
                BigDecimal(0),
                null,
                "Returns 0 for all invalid date strings"
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        functionTest(fn, spec)
    }

}