package services.oneteam.ai.flow.expression.functions

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import java.math.BigDecimal

class MinPairwiseTest {

    private val minPairwise = Pairwise(PairwiseOperation.MIN, BigDecimal.ZERO)

    @Nested
    inner class NumberInputCases {
        @Test
        fun `match should always return true`() {
            minPairwise.match(listOf(listOf(1, 2, 3), listOf(4, 5, 6))) shouldBe true
        }

        @Test
        fun `min should handle single number input`() {
            val result = minPairwise.perform(listOf(1))
            result shouldBe 1
        }

        @Test
        fun `min should handle list of number inputs`() {
            val result = minPairwise.perform(listOf(1, 2, 3))
            result shouldBe BigDecimal(1)
        }

        @Test
        fun `min should handle single list input`() {
            val result = minPairwise.perform(listOf(listOf(1, 2, 3)))
            result shouldBe listOf(1, 2, 3)
        }

        @Test
        fun `min should return pairwise min of two lists`() {
            val result = minPairwise.perform(listOf(listOf(1, 2, 3), listOf(4, -5, 6)))
            result shouldBe listOf(BigDecimal(1), BigDecimal(-5), BigDecimal(3))
        }

        @Test
        fun `min should return pairwise min of two lists with numbers as strings`() {
            val result = minPairwise.perform(listOf(listOf("1", "2", 3), listOf(4, "-5", "6")))
            result shouldBe listOf(BigDecimal(1), BigDecimal(-5), BigDecimal(3))
        }

        @Test
        fun `min should handle lists of different lengths`() {
            /*
            => MIN([1, 2], [3, 4, 5])
            => MIN([1, 2, 0], [3, 4, 5])
            => [1, 2, 0]
             */
            val result = minPairwise.perform(listOf(listOf(1, 2), listOf(3, 4, 5)))
            result shouldBe listOf(BigDecimal(1), BigDecimal(2), BigDecimal(0))
        }
    }

    @Nested
    inner class NestedStructureCases {
        @Test
        fun `min should handle nested lists`() {
            val result = minPairwise.perform(
                listOf(
                    listOf(listOf(1, 2), listOf(7, 4)),
                    listOf(listOf(5, 6), listOf(3, 8))
                )
            )
            result shouldBe listOf(listOf(BigDecimal(1), BigDecimal(2)), listOf(BigDecimal(3), BigDecimal(4)))
        }

        @Test
        fun `min should handle different dimension lists`() {
            /*
            => [[1, 2], [2, 1]] + [-3, 4, -5]
            => [[1, 2], [2, 1]] + [[-3, 4, -5], [-3, 4, -5]]
            => [[1, 2] + [-3, 4, -5], [2, 1] + [-3, 4, -5]],
            => [[1, 2, 0] + [-3, 4, -5], [2, 1, 0] + [-3, 4, -5]],
            => [[-2, 6, -5], [-1, 5, -5]],
             */
            val result = minPairwise.perform(
                listOf(
                    listOf(
                        listOf(1, 2),
                        listOf(2, 1)
                    ),
                    listOf(-3, 4, -5)
                )
            )
            result shouldBe
                    listOf(
                        listOf(BigDecimal(-3), BigDecimal(2), BigDecimal(-5)),
                        listOf(BigDecimal(-3), BigDecimal(1), -BigDecimal(5))
                    )
        }
    }

    @Nested
    inner class DateInputCases {
        @Test
        fun `min should return pairwise min of two date lists`() {
            val result = minPairwise.perform(
                listOf(
                    listOf("2023-01-01", "2023-03-15"),
                    listOf("2023-02-01", "2023-01-20")
                )
            )
            result shouldBe listOf("2023-01-01", "2023-01-20")
        }

        @Test
        fun `min should return pairwise min of arrays of arrays of dates`() {
            val result = minPairwise.perform(
                listOf(
                    listOf(listOf("2023-01-01", "2023-02-01")),
                    listOf(listOf("2023-03-15", "2023-01-05"))
                )
            )
            result shouldBe listOf(listOf("2023-01-01", "2023-01-05"))
        }

        @Test
        fun `min should handle date list and single date`() {
            val result = minPairwise.perform(
                listOf(
                    listOf("2023-01-01", "2023-03-15"),
                    "2023-02-01"
                )
            )
            result shouldBe listOf("2023-01-01", "2023-02-01")
        }

        @Test
        fun `min should handle date lists of different lengths`() {
            val result = minPairwise.perform(
                listOf(
                    listOf("2023-01-01", "2023-03-15", "2023-01-01"),
                    listOf("2023-02-01")
                )
            )
            result shouldBe listOf("2023-01-01", "2023-03-15", "2023-01-01")
        }

        @Test
        fun `min should handle different dimension lists for dates`() {
            /*
            => [["2023-01-01", "2023-03-15"], ["2023-02-01", "2023-04-01"]] MIN ["2023-02-01"]
            => [["2023-01-01", "2023-03-15"], ["2023-02-01", "2023-04-01"]] MIN [["2023-02-01"], ["2023-02-01"]]
            => [["2023-01-01", "2023-03-15"] MIN ["2023-02-01"], ["2023-02-01", "2023-04-01"] MIN ["2023-02-01"]]
            => [["2023-01-01", "2023-03-15"] MIN ["2023-02-01", ""], ["2023-02-01", "2023-04-01"] MIN ["2023-02-01", ""]]
            => [["2023-01-01", "2023-03-15"], ["2023-02-01", "2023-04-01"]]
             */
            val result = minPairwise.perform(
                listOf(
                    listOf(
                        listOf("2023-01-01", "2023-03-15"),
                        listOf("2023-02-01", "2023-04-01")
                    ),
                    listOf("2023-02-01")
                )
            )
            result shouldBe
                    listOf(
                        listOf("2023-01-01", "2023-03-15"),
                        listOf("2023-02-01", "2023-04-01")
                    )
        }

        @Test
        fun `min should handle different dimension lists for dates with some invalid data`() {
            /*
            => [["2023-01-01", "not a date"], ["2023-02-01", "2023-04-01"]] MIN ["2023-02-01"]
            => [["2023-01-01", "not a date"], ["2023-02-01", "2023-04-01"]] MIN [["2023-02-01"], ["2023-02-01"]]
            => [["2023-01-01", "not a date"] MIN ["2023-02-01"], ["2023-02-01", "2023-04-01"] MIN ["2023-02-01"]]
            => [["2023-01-01", "not a date"] MIN ["2023-02-01", ""], ["2023-02-01", "2023-04-01"] MIN ["2023-02-01", ""]]
            => [["2023-01-01", ""], ["2023-02-01", "2023-04-01"]]
             */
            val result = minPairwise.perform(
                listOf(
                    listOf(
                        listOf("2023-01-01", "not a date"),
                        listOf("2023-02-01", "2023-04-01")
                    ),
                    listOf("2023-02-01")
                )
            )
            result shouldBe
                    listOf(
                        listOf("2023-01-01", ""),
                        listOf("2023-02-01", "2023-04-01")
                    )
        }
    }

    @Nested
    inner class EdgeCases {
        @Test
        fun `min should handle empty lists`() {
            val result = minPairwise.perform(listOf(emptyList<Int>(), emptyList<Int>()))
            result shouldBe emptyList<Int>()
        }

        @Test
        fun `min should handle one empty and one non-empty list`() {
            /*
            => MIN([], [5])
            => MIN([5])
            => [5]
            */
            val result = minPairwise.perform(listOf(emptyList<Int>(), listOf(5)))
            result shouldBe listOf(BigDecimal(5))
        }

        /**
         * This works in jsonata and flow see [MinTest]
         *
         * MIN([null], [null])
         * MIN([0], [0])
         * [0] but it's giving [[0]] when calling minPairwise.perform
         * works in jsonata and in flow since the context resolves it to "null" in string form
         * and not actual null value, as seen in the next test
         * [MinTest] also has tests for empty values or unresolved values
         */
//        @Test
//        fun `min should return zero on all nil values`() {
//            val result = minPairwise.perform(listOf(listOf(null), listOf(null)))
//            result shouldBe listOf(0)
//        }

        @Test
        fun `min should return zero on all null string values`() {
            val result = minPairwise.perform(listOf(listOf("null"), listOf("null")))
            result shouldBe listOf(0)
        }

        @Test
        fun `min should return empty string on all empty string values`() {
            val result = minPairwise.perform(listOf(listOf(""), listOf("")))
            result shouldBe listOf("")
        }

        @Test
        fun `min should ignore empty strings when mixed with numbers`() {
            val result = minPairwise.perform(listOf(listOf("", 1), listOf(5, "")))
            result shouldBe listOf(BigDecimal(5), BigDecimal(1))
        }

        @Test
        fun `min should ignore invalid date strings`() {
            /*
            => MIN(["2023-01-01", "not-a-date"], ["2022-01-01", "2024-01-01"])
            => MIN(["2023-01-01", ""], ["2022-01-01", "2024-01-01"])
            => [ MIN("2023-01-01", "2022-01-01"), MIN("", "2024-01-01") ]
            => [ "2022-01-01", "2024-01-01" ]
             */
            val result =
                minPairwise.perform(listOf(listOf("2023-01-01", "not-a-date"), listOf("2022-01-01", "2024-01-01")))
            result shouldBe listOf("2022-01-01", "2024-01-01")
        }

        @Test
        fun `min should return the valid date when one valid and one invalid date string`() {
            val result = minPairwise.perform(listOf("2023-01-01", "notadate"))
            result shouldBe "2023-01-01"
        }

        @Test
        fun `min should return zero on date-time strings`() {
            val result = minPairwise.perform(
                listOf(
                    listOf("2023-01-01T12:00:00", "2023-01-01T13:00:00"),
                    listOf("2023-01-01T11:00:00", "2023-01-01T14:00:00")
                )
            )
            result shouldBe listOf(0, 0)
        }
    }

    @Nested
    inner class MixedTypeCases {
        @Test
        fun `min should handle mixed date and number types`() {
            val result = minPairwise.perform(listOf(listOf(1, "2023-01-01"), listOf(2, "2022-01-01")))
            result shouldBe listOf(BigDecimal(1), "2022-01-01")
        }

        @Test
        fun `min should handle scalar and list of dates`() {
            val result = minPairwise.perform(listOf("2023-01-01", listOf("2022-01-01", "2024-01-01")))
            result shouldBe listOf("2022-01-01", "2023-01-01")
        }

        @Test
        fun `min should handle nested arrays and dates`() {
            val result = minPairwise.perform(
                listOf(
                    listOf(listOf("2023-01-01", "2023-02-01")),
                    listOf(listOf("2023-03-01", "2023-01-05"))
                )
            )
            result shouldBe listOf(listOf("2023-01-01", "2023-01-05"))
        }

        @Test
        fun `min should handle deeply nested arrays`() {
            val result = minPairwise.perform(
                listOf(
                    listOf(listOf(listOf(1, 2), listOf(3, 4))),
                    listOf(listOf(listOf(4, 1), listOf(2, 5)))
                )
            )
            result shouldBe listOf(listOf(listOf(BigDecimal(1), BigDecimal(1)), listOf(BigDecimal(2), BigDecimal(4))))
        }
    }

    @Nested
    inner class InvalidDataEdgeCases {
        @Test
        fun `min should treat lists with only whitespace strings as empty or invalid`() {
            val result = minPairwise.perform(listOf(listOf(" ", "   "), listOf(" ", "   ")))
            result shouldBe listOf(0, 0)
        }

        @Test
        fun `min should handle lists with booleans or objects`() {
            val result =
                minPairwise.perform(listOf(listOf(true, false, mapOf("a" to 1)), listOf(true, false, mapOf("a" to 1))))
            result shouldBe listOf(0, 0, 0)
        }

        @Test
        fun `min should handle lists with only empty lists`() {
            val result = minPairwise.perform(listOf(emptyList<Any>(), emptyList<Any>()))
            result shouldBe emptyList<Any>()
        }

        @Test
        fun `min should handle deeply nested empty or invalid values`() {
            val result = minPairwise.perform(listOf(listOf(listOf("asd")), listOf(listOf("fgh"))))
            result shouldBe listOf(listOf(0))
        }
    }
}