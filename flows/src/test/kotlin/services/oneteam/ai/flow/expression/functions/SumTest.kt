package services.oneteam.ai.flow.expression.functions

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SumTest {

    private val fn = Sum

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName([])", BigDecimal(0)),
            Spec("$$functionName(2, 3)", BigDecimal(5)),
            Spec("$$functionName(2, '3')", BigDecimal(5)),
            Spec("$$functionName(2, '3', 'a')", BigDecimal(5)),
            Spec("$$functionName([1,2,3,4,5])", <PERSON>Decimal(15)),
            Spec("$$functionName([[1,2,3]])", listOf(listOf(1, 2, 3))),
            Spec("$$functionName([\"1\",2,\"3\",4,5])", BigDecimal(15)),
            Spec("$$functionName([\"1\",2,\"3\",4,5,'a'])", BigDecimal(15)),
            Spec("$$functionName([1,2,3],'4')", listOf(BigDecimal(5), BigDecimal(6), BigDecimal(7))),
            Spec("$$functionName([1,2,3],4)", listOf(BigDecimal(5), BigDecimal(6), BigDecimal(7))),
            Spec(
                "$$functionName([1, 2], [[3, 4, 5], [6, 7, 8]])", listOf(
                    listOf(BigDecimal(4), BigDecimal(6), BigDecimal(5)),
                    listOf(
                        BigDecimal(7), BigDecimal(9),
                        BigDecimal(8)
                    )
                )
            ),
            Spec(
                "$$functionName([0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1], [0.1])",
                listOf(BigDecimal(1))
            ),
            Spec("$$functionName([1, null], [null, 1])", listOf(BigDecimal(1), BigDecimal(1))),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `function test`(spec: Spec) {
        functionTest(fn, spec)
    }

    @Test
    fun `primitive rounding error`() {
        // This is a known issue with floating point arithmetic in Java
        // The result of adding 0.1 ten times is not exactly 1.0 due to precision issues
        // This is because 0.1 cannot be represented exactly in binary floating point
        // And why we should use BigDecimal for precise calculations
        assertThat(0.1 + 0.1 + 0.1 + 0.1 + 0.1 + 0.1 + 0.1 + 0.1 + 0.1 + 0.1).isEqualTo(0.9999999999999999) // 0.9999999999999999
    }

    @Test
    fun `binary number representation problem`() {
        // This is because 0.1 cannot be represented exactly in binary floating point
        // And why we should use BigDecimal for precise calculations
        //        Expected :0.1
        //        Actual   :0.1000000000000000055511151231257827021181583404541015625
        assertThat(BigDecimal(0.1)).isNotEqualTo(BigDecimal("0.1")) // 0.9999999999999999
        assertThat(BigDecimal(0.1)).isEqualTo(BigDecimal("0.1000000000000000055511151231257827021181583404541015625")) // 0.9999999999999999

    }
}