package services.oneteam.ai.flow.expression.functions

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.extensions.isNumeric
import services.oneteam.ai.shared.extensions.isParsableDate
import java.time.LocalDate

class ListExtensionsTest {

    @Test
    fun `test isParseableDate utility function`() {
        isParsableDate("2023-01-01") shouldBe true
        isParsableDate("not-a-date") shouldBe false
        isParsableDate("2023-01-01T12:00:00") shouldBe false
        isParsableDate("") shouldBe true
        isParsableDate(" ") shouldBe false
        isParsableDate(LocalDate.MIN.toString()) shouldBe true
        isParsableDate(LocalDate.MAX.toString()) shouldBe true
    }

    @Test
    fun `test isNumeric utility function`() {
        isNumeric(123) shouldBe true
        isNumeric(123.45) shouldBe true
        isNumeric("123") shouldBe true
        isNumeric("123.45") shouldBe true
        isNumeric("not-a-number") shouldBe false
        isNumeric("") shouldBe false
        isNumeric(" ") shouldBe false
        isNumeric(null) shouldBe false
    }
}