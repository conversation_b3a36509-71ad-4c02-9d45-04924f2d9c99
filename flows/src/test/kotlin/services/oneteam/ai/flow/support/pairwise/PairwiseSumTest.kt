package services.oneteam.ai.flow.support.pairwise

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.math.BigDecimal

/**
 * Test class for PairwiseAdd.
 *
 * SEE https://docs.google.com/spreadsheets/d/19UGbngFmR0wV6hE_J1NakaTiO9TKTDdbB-FmIrerbKQ/edit?gid=0#gid=0
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PairwiseSumTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    data class Scenario(
        val name: String, val values: List<PairwiseValue>, val expected: PairwiseValue
    )

    fun provider(): List<Scenario> {
        return listOf(
            Scenario(
                "case 0a: Property Expenses [[100, 200, 300], [400, 500, 600]] + [100, 200, 300, 400, 500] = [[200, 400, 600, 400, 500], [500, 700, 900, 400, 500]]",
                listOf(
                    PairwiseValue.of(listOf(listOf(100, 200, 300), listOf(400, 500, 600))),
                    PairwiseValue.of(listOf(100, 200, 300, 400, 500))
                ),
                PairwiseValue.of(
                    listOf(
                        listOf(
                            BigDecimal("200"),
                            BigDecimal("400"),
                            BigDecimal("600"),
                            BigDecimal("400"),
                            BigDecimal("500")
                        ),
                        listOf(
                            BigDecimal("500"),
                            BigDecimal("700"),
                            BigDecimal("900"),
                            BigDecimal("400"),
                            BigDecimal("500")
                        )
                    )
                )
            ),
            Scenario(
                "case 0b: [[1, 2]] + 4 = [[5, 6]]",
                listOf(PairwiseValue.of(listOf(listOf(1, 2))), PairwiseValue.of(4)),
                PairwiseValue.of(listOf(listOf(BigDecimal("5"), BigDecimal("6"))))
            ),
            Scenario(
                "case 1: [1, 2] + 5 = [6, 7]",
                listOf(PairwiseValue.of(listOf(1, 2)), PairwiseValue.of(5)),
                PairwiseValue.of(listOf(BigDecimal("6"), BigDecimal("7")))
            ),
            Scenario(
                "case 2: [[1, 2], [3, 4]] + 5 = [[6, 7], [8, 9]]",
                listOf(PairwiseValue.of(listOf(listOf(1, 2), listOf(3, 4))), PairwiseValue.of(5)),
                PairwiseValue.of(
                    listOf(
                        listOf(BigDecimal("6"), BigDecimal("7")),
                        listOf(BigDecimal("8"), BigDecimal("9"))
                    )
                )
            ),
            Scenario(
                "case 3: [1, 2, 3] + 4 = [5, 6, 7]",
                listOf(PairwiseValue.of(listOf(1, 2, 3)), PairwiseValue.of(4)),
                PairwiseValue.of(listOf(BigDecimal("5"), BigDecimal("6"), BigDecimal("7")))
            ),
            Scenario(
                "case 4b: [1, 2, 3] + 4 + [[5, 6], [7, 8]] = [[10, 12, 7], [12, 14, 7]]",
                listOf(
                    PairwiseValue.of(listOf(1, 2, 3)),
                    PairwiseValue.of(4),
                    PairwiseValue.of(listOf(listOf(5, 6), listOf(7, 8))),
                ),
                PairwiseValue.of(
                    listOf(
                        listOf(BigDecimal("10"), BigDecimal("12"), BigDecimal("7")),
                        listOf(BigDecimal("12"), BigDecimal("14"), BigDecimal("7"))
                    )
                )
            ),
            Scenario(
                "case 5: 5 + [5, 6] = [10, 11]",
                listOf(
                    PairwiseValue.of(5),
                    PairwiseValue.of(listOf(5, 6)),
                ),
                PairwiseValue.of(listOf(BigDecimal("10"), BigDecimal("11")))
            ),
            Scenario(
                "case 6: [5, 0] + [5, 6] = [10, 6]",
                listOf(
                    PairwiseValue.of(listOf(5, 0)),
                    PairwiseValue.of(listOf(5, 6)),
                ),
                PairwiseValue.of(listOf(BigDecimal("10"), BigDecimal("6")))
            ),
            Scenario(
                "case 7a:  4 + [[5, 6], [7, 8]] = [[9, 10], [11, 12]]",
                listOf(
                    PairwiseValue.of(4),
                    PairwiseValue.of(listOf(listOf(5, 6), listOf(7, 8))),
                ),
                PairwiseValue.of(
                    listOf(
                        listOf(BigDecimal("9"), BigDecimal("10")),
                        listOf(BigDecimal("11"), BigDecimal("12"))
                    )
                )
            ),
            Scenario(
                "case 7b:  4 + [[5, 6asd], [7, 8]] = [[9, 4], [11, 12]]",
                listOf(
                    PairwiseValue.of(4),
                    PairwiseValue.of(listOf(listOf(5, "6asd"), listOf(7, 8))),
                ),
                PairwiseValue.of(
                    listOf(
                        listOf(BigDecimal("9"), BigDecimal("4")),
                        listOf(BigDecimal("11"), BigDecimal("12"))
                    )
                )
            ),
            Scenario(
                "case 8a: Manually Padded [1, 2, 0] + [[3, 4, 5], [6, 7, 8]] = [[4, 6, 5], [7, 9, 8]]",
                listOf(
                    PairwiseValue.of(listOf(1, 2, 0)),
                    PairwiseValue.of(listOf(listOf(3, 4, 5), listOf(6, 7, 8))),
                ),
                PairwiseValue.of(
                    listOf(
                        listOf(BigDecimal("4"), BigDecimal("6"), BigDecimal("5")),
                        listOf(BigDecimal("7"), BigDecimal("9"), BigDecimal("8"))
                    )
                )
            ),
            Scenario(
                "case 8b: No Padding [1, 2] + [[3, 4, 5], [6, 7, 8]] = [[4, 6, 5], [7, 9, 8]]",
                listOf(
                    PairwiseValue.of(listOf(1, 2)),
                    PairwiseValue.of(listOf(listOf(3, 4, 5), listOf(6, 7, 8))),
                ),
                PairwiseValue.of(
                    listOf(
                        listOf(BigDecimal("4"), BigDecimal("6"), BigDecimal("5")),
                        listOf(BigDecimal("7"), BigDecimal("9"), BigDecimal("8"))
                    )
                )
            ),
            Scenario(
                "case 8c: Ragged dimension 1: [1, 2] + [[3, 4, 5], [6, 7]] = [[4, 6, 5], [7, 9]]",
                listOf(
                    PairwiseValue.of(listOf(1, 2)),
                    PairwiseValue.of(listOf(listOf(3, 4, 5), listOf(6, 7))),
                ),
                PairwiseValue.of(
                    listOf(
                        listOf(BigDecimal("4"), BigDecimal("6"), BigDecimal("5")),
                        listOf(BigDecimal("7"), BigDecimal("9"))
                    )
                )
            ),
            Scenario(
                "case 8d: Ragged dimension 1: [1, 2, 3, 4, 5, 6, 7] + [[3, 4, 5], [6, 7]] = [[4, 6, 8, 4, 5, 6, 7], [7, 9, 3, 4, 5, 6, 7]]",
                listOf(
                    PairwiseValue.of(listOf(1, 2, 3, 4, 5, 6, 7)),
                    PairwiseValue.of(listOf(listOf(3, 4, 5), listOf(6, 7))),
                ),
                PairwiseValue.of(
                    listOf(
                        listOf(
                            BigDecimal("4"),
                            BigDecimal("6"),
                            BigDecimal("8"),
                            BigDecimal("4"),
                            BigDecimal("5"),
                            BigDecimal("6"),
                            BigDecimal("7")
                        ),
                        listOf(
                            BigDecimal("7"),
                            BigDecimal("9"),
                            BigDecimal("3"),
                            BigDecimal("4"),
                            BigDecimal("5"),
                            BigDecimal("6"),
                            BigDecimal("7")
                        )
                    )
                )
            ),
            Scenario(
                "case 9: [1, null] + [null, 1] = [1, 1]",
                listOf(
                    PairwiseValue.of(listOf(1, null)),
                    PairwiseValue.of(listOf(null, 1)),
                ),
                PairwiseValue.of(listOf(BigDecimal("1"), BigDecimal("1")))
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `scenario `(scenario: Scenario) {

        logger.debug("Scenario: {}", scenario.name)
        logger.debug("Values: {}", scenario.values)
        logger.debug("Expected: {}", scenario.expected)
        Pairwise(PairwiseOperation.SUM).pairwise(scenario.values).let {
            logger.debug("Pairwise result: {}", it)
            logger.debug("{} = {}", scenario.values.joinToString(" + ") { it.value.toString() }, it.value)
            assertThat(it).isEqualTo(scenario.expected)
        }
    }

}