package services.oneteam.ai.flow.execution.step

import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.proxy.ProxyService.ProxyEndpointResponse
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.request.HttpRequestData
import io.ktor.client.request.HttpResponseData
import io.ktor.client.engine.mock.MockRequestHandleScope
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.kotlinx.json.json

object ProxyFixtures {

    fun mockForSuccess(response: String): ProxyService {
        val httpResponse = ProxyEndpointResponse(
            response = response,
            status = ProxyService.ProxyEndpointResponseStatus.SUCCESS,
            error = null
        )
        val proxyService = mockk<ProxyService>()
        coEvery { proxyService.call(any(), any(), any()) } returns httpResponse
        mockkObject(ProxyService)
        coEvery { ProxyService.buildInternalTenantUrl(any()) } returns "http://localhost:8080/ai/api/sync/form/1/answer"
        return proxyService
    }

    /**
     * Create an ExternalProxyService with a mock HttpClient using the provided response handler.
     * The response handler allows customizing the mock responses based on the incoming requests.
     * @param responseHandler A suspend function that takes an HttpRequestData and returns an HttpResponseData.
     * @return An instance of ExternalProxyService with the mock HttpClient.
     * [MockEngine] is used to simulate HTTP responses for testing purposes.
     */
    fun externalProxyWithMockClient(
        responseHandler: suspend MockRequestHandleScope.(HttpRequestData) -> HttpResponseData
    ): ExternalProxyService {
        val mockEngine = MockEngine(responseHandler)
        val httpClient = HttpClient(mockEngine) {
            install(ContentNegotiation) { json(); }
            install(HttpTimeout)
        }
        return ExternalProxyService("token", httpClient)
    }
}