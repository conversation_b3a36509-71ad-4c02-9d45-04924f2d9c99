package services.oneteam.ai.flow.execution

import io.kotest.common.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonObject
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.mapBuilders.FormJsonMapBuilder
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.collection.Visibility
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.otSerializer
import kotlin.test.assertEquals

class FormJsonMapBuilderTest {

    companion object {
        val logger: Logger = LoggerFactory.getLogger(javaClass)

        val formService: FormService = mock(FormService::class.java)
        val foundationService: FoundationService = mock(FoundationService::class.java)
        val documentService: IDocumentService = mock(IDocumentService::class.java)
        val workspaceId = Workspace.Id(1)
        const val COOKIE = "cookie"

        val workspace = otSerializer.decodeFromString<Workspace.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )

        val formConfiguration = workspace.findForm(FormConfiguration.Id("cp"))

        val foundation = Foundation.ForApi(
            Foundation.Id(4),
            Foundation.Name("foundationName"),
            Foundation.Key("foundationKey"),
            FoundationConfiguration.Id("emp"),
            workspaceId,
            Foundation.Id(5),
            EntityMetadata.now(),
            otSerializer.decodeFromString<JsonObject>("""{"prop": "value"}"""),
            visibility = Visibility.INHERIT,
        )
    }

    @BeforeEach
    fun setup(): Unit = runBlocking {
        `when`(foundationService.get(foundation.id)).thenReturn(foundation)

        `when`(
            documentService.show<FormAnswer.ForJson>(
                "documentId", COOKIE, FormAnswer.ForJson::class, true
            )
        ).thenReturn(
            // empty document
            FormAnswer.ForJson(
                Form.Id(1),
                mutableMapOf(),
            )
        )
    }

    @Test
    fun `should build map`() = runTest {
        val formConfiguration = workspace.findForm(FormConfiguration.Id("aqt"))
        `when`(formService.get(Form.Id(1))).thenReturn(
            Form.ForApi(
                Form.Id(1),
                FormConfiguration.Id(formConfiguration.id),
                foundation.id,
                if (!formConfiguration.seriesId.isNullOrBlank()) SeriesConfiguration.Id(formConfiguration.seriesId!!) else null,
                if (!formConfiguration.seriesId.isNullOrBlank()) IntervalId("2024") else null,
                Visibility.INHERIT,
                Form.DocumentId("documentId"),
                Form.AnnotationDocumentId("annotationDocumentId"),
                workspaceId,
                EntityMetadata.now(),
                foundation,
                otSerializer.decodeFromString<JsonObject>("""{"prop": "value"}""")
            )
        )

        val formJsonMapBuilder = FormJsonMapBuilder(
            formService, foundationService, documentService, COOKIE, workspace
        )
        val formJsonMap = formJsonMapBuilder.handle(
            VariableInstance.Variable(
                JsonPrimitive(1), VariableDataType.fromString("form.aqt"), "formA"
            )
        )
        assertNotNull(formJsonMap)

        logger.trace(otSerializer.encodeToString(formJsonMap))

        // using structure below we can evaluate json path
        // {{formA,foundation.name}} -> foundationName

        val expected: JsonElement = otSerializer.decodeFromString<JsonElement>(
            """
{
  "id": 1,
  "formConfigurationId": "aqt",
  "foundationId": 4,
  "foundation": {
    "id": 4,
    "name": "foundationName",
    "key": "foundationKey",
    "foundationConfigurationId": "emp",
    "foundationConfiguration": {
      "id": "emp",
      "name": "Employee",
      "relationship": "OneToMany",
      "description": "Employee",
      "identifier": "emp",
      "disableAutoSuggestKey": false
    },
    "parentId": 5,
    "properties": {
      "prop": "value"
    }
  },
  "properties": {
    "prop": "value"
  },
  "seriesId": null,
  "intervalId": null,
  "documentId": "documentId",
  "annotationDocumentId": "annotationDocumentId",
  "formConfiguration": {
    "id": "aqt",
    "key": "AQT",
    "name": "All question types",
    "seriesId": ""
  },
  "plBrtlhc3W": {
    "id": "plBrtlhc3W",
    "type": "text",
    "text": "Text",
    "identifier": "Text1",
    "description": "",
    "answer": null
  },
  "np9GiUMruw": {
    "id": "np9GiUMruw",
    "type": "number",
    "text": "Number",
    "identifier": "Number",
    "description": "",
    "answer": null
  },
  "mHhmVG6X7z": {
    "id": "mHhmVG6X7z",
    "type": "select",
    "text": "Select",
    "identifier": "Select",
    "description": "",
    "answer": null
  },
  "JQIswpnlvm": {
    "id": "JQIswpnlvm",
    "type": "boolean",
    "text": "Boolean",
    "identifier": "Boolean",
    "description": "",
    "answer": false
  },
  "VEOTahOeLj": {
    "id": "VEOTahOeLj",
    "type": "table",
    "text": "Table",
    "answer": [],
    "columns": {
      "O1REtNdy50": {
        "id": "O1REtNdy50",
        "type": "text",
        "text": "TableText1",
        "answer": []
      },
      "eALIKF2hFv": {
        "id": "eALIKF2hFv",
        "type": "number",
        "text": "TableNumber1",
        "answer": []
      },
      "ids": [
        "O1REtNdy50",
        "eALIKF2hFv"
      ]
    }
  },
  "r5QWKQYgKe": {
    "id": "r5QWKQYgKe",
    "type": "date",
    "text": "Date Question",
    "identifier": "DateQuestion",
    "description": "",
    "answer": null
  },
  "apZe1DTF8B": {
    "id": "apZe1DTF8B",
    "type": "list",
    "text": "List Question — simple",
    "identifier": "ListQuestionSimple",
    "description": "",
    "answer": []
  },
  "kM03gL62Rj": {
    "id": "kM03gL62Rj",
    "type": "number",
    "text": "Number",
    "identifier": "Number_1",
    "description": "",
    "answer": null
  },
  "RaEwuSTmEd": {
    "id": "RaEwuSTmEd",
    "type": "number",
    "text": "Accounting",
    "identifier": "Accounting",
    "description": "",
    "answer": null
  },
  "a8FSiagy4Sc": {
    "id": "a8FSiagy4Sc",
    "type": "number",
    "text": "Percentage",
    "identifier": "Percentage",
    "description": "",
    "answer": null
  },
  "t3Caagn0aZ": {
    "id": "t3Caagn0aZ",
    "type": "date",
    "text": "Date",
    "identifier": "Date",
    "description": "",
    "answer": null
  },
  "p4war6g2xR": {
    "id": "p4war6g2xR",
    "type": "date",
    "text": "Date with range",
    "identifier": "DateWithRange",
    "description": "",
    "answer": null
  },
  "akxUewEj3Q": {
    "id": "akxUewEj3Q",
    "type": "select",
    "text": "Select",
    "identifier": "Select_1",
    "description": "",
    "answer": null
  },
  "IROlcRdJSA": {
    "id": "IROlcRdJSA",
    "type": "multiSelect",
    "text": "Multi Select",
    "identifier": "MultiSelect",
    "description": "",
    "answer": []
  },
  "oDI9O0xAKo": {
    "id": "oDI9O0xAKo",
    "type": "table",
    "text": "Table",
    "answer": [],
    "columns": {
      "K2mO2ZztGH": {
        "id": "K2mO2ZztGH",
        "type": "text",
        "text": "Text",
        "answer": []
      },
      "zTHSWvUxu5": {
        "id": "zTHSWvUxu5",
        "type": "number",
        "text": "Number",
        "answer": []
      },
      "UYpXBe7SkM": {
        "id": "UYpXBe7SkM",
        "type": "number",
        "text": "Number(Accounting)",
        "answer": []
      },
      "UuvVH4Ru70": {
        "id": "UuvVH4Ru70",
        "type": "number",
        "text": "Number(Percentage)",
        "answer": []
      },
      "PBToKkez0K": {
        "id": "PBToKkez0K",
        "type": "date",
        "text": "Date",
        "answer": []
      },
      "a7HBVtb1kLz": {
        "id": "a7HBVtb1kLz",
        "type": "date",
        "text": "Date with range",
        "answer": []
      },
      "YIFgX4PrMl": {
        "id": "YIFgX4PrMl",
        "type": "select",
        "text": "Select",
        "answer": []
      },
      "LXJKuURzuI": {
        "id": "LXJKuURzuI",
        "type": "multiSelect",
        "text": "Multi select",
        "answer": []
      },
      "ckrCdRRoW3": {
        "id": "ckrCdRRoW3",
        "type": "boolean",
        "text": "Boolean",
        "answer": []
      },
      "ids": [
        "K2mO2ZztGH",
        "zTHSWvUxu5",
        "UYpXBe7SkM",
        "UuvVH4Ru70",
        "PBToKkez0K",
        "a7HBVtb1kLz",
        "YIFgX4PrMl",
        "LXJKuURzuI",
        "ckrCdRRoW3"
      ]
    }
  },
  "a1sUhILky8o": {
    "id": "a1sUhILky8o",
    "type": "json",
    "text": "JSON",
    "identifier": "Json",
    "description": "",
    "answer": {}
  },
  "VA7AgyzZST": {
    "id": "VA7AgyzZST",
    "type": "files",
    "text": "Upload",
    "identifier": "Upload",
    "description": "",
    "answer": []
  }
}                
            """.trimIndent()
        )

        assertEquals(expected, formJsonMap)
    }

    @Test
    fun `should build map with seriesInterval and get valid next`() = runTest {
        `when`(formService.get(Form.Id(1))).thenReturn(
            Form.ForApi(
                Form.Id(1),
                FormConfiguration.Id(formConfiguration.id),
                foundation.id,
                SeriesConfiguration.Id(formConfiguration.seriesId!!),
                IntervalId("aa2024"),
                Visibility.INHERIT,
                Form.DocumentId("documentId"),
                Form.AnnotationDocumentId("annotationDocumentId"),
                workspaceId,
                EntityMetadata.now(),
                foundation
            )
        )

        val formJsonMapBuilder = FormJsonMapBuilder(
            formService, foundationService, documentService, COOKIE, workspace
        )
        val formJsonMap = formJsonMapBuilder.handle(
            VariableInstance.Variable(
                JsonPrimitive(1), VariableDataType.fromString("form.aqt"), "formA"
            )
        )


        val expectedSeriesInterval = otSerializer.decodeFromString<JsonElement>(
            """
                {
                  "id": "aa2024",
                  "name": "2024",
                  "previous": {
                      "id": null,
                      "name": null
                  },
                  "next": { 
                    "id": "aa2025",
                    "name": "2025"
                  },
                  "seriesConfiguration": {
                    "id": "years",
                    "name": "Years",
                   "intervals": [
                        {
                            "id": "aa2024",
                            "name": "2024"
                        },
                        {
                            "id": "aa2025",
                            "name": "2025"
                        }
                    ]
                  }
                }   
            """.trimIndent()
        )

        logger.trace(otSerializer.encodeToString(formJsonMap))

        assertNotNull(formJsonMap)
        assertNotNull(formJsonMap.jsonObject["seriesInterval"])
        assertNotNull(formJsonMap.jsonObject["seriesInterval"]!!.jsonObject["next"])
        assertEquals(expectedSeriesInterval, formJsonMap.jsonObject["seriesInterval"])
        assertEquals(
            "\"aa2025\"", formJsonMap.jsonObject["seriesInterval"]!!.jsonObject["next"]!!.jsonObject["id"].toString()
        )
    }

    @Test
    fun `should build map with seriesInterval and get valid previous`() = runTest {
        `when`(formService.get(Form.Id(1))).thenReturn(
            Form.ForApi(
                Form.Id(1),
                FormConfiguration.Id(formConfiguration.id),
                foundation.id,
                SeriesConfiguration.Id(formConfiguration.seriesId!!),
                IntervalId("aa2025"),
                Visibility.INHERIT,
                Form.DocumentId("documentId"),
                Form.AnnotationDocumentId("annotationDocumentId"),
                workspaceId,
                EntityMetadata.now(),
                foundation
            )
        )

        val formJsonMapBuilder = FormJsonMapBuilder(
            formService, foundationService, documentService, COOKIE, workspace
        )
        val formJsonMap = formJsonMapBuilder.handle(
            VariableInstance.Variable(
                JsonPrimitive(1), VariableDataType.fromString("form.aqt"), "formA"
            )
        )


        val expectedSeriesInterval = otSerializer.decodeFromString<JsonElement>(
            """
                { 
                    "id": "aa2025",
                    "name": "2025",
                    "previous": { 
                        "id": "aa2024",
                        "name": "2024"
                    },
                    "next": {
                        "id": null,
                        "name": null
                    },
                    "seriesConfiguration": {
                        "id": "years",
                        "name": "Years",
                        "intervals": [
                            {
                                "id": "aa2024",
                                "name": "2024"
                            },
                            {
                                "id": "aa2025",
                                "name": "2025"
                            }
                        ]
                    }         
                }     
            """.trimIndent()
        )

        logger.trace(otSerializer.encodeToString(formJsonMap))

        assertNotNull(formJsonMap)
        assertNotNull(formJsonMap.jsonObject["seriesInterval"])
        assertEquals(expectedSeriesInterval, formJsonMap.jsonObject["seriesInterval"])
        assertEquals(
            "\"aa2024\"",
            formJsonMap.jsonObject["seriesInterval"]!!.jsonObject["previous"]!!.jsonObject["id"].toString()
        )
    }
}