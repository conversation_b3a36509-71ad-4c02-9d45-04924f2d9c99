package services.oneteam.ai.flow.execution

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class JsonPlaceholderReplacerTest {

    private val replacer = JsonPlaceholderReplacer()

    @Test
    fun `should replace placeholders in a simple JSON object`() = runTest {
        val json = JsonObject(mapOf("key" to JsonPrimitive("{{value}}")))
        val values = JsonObject(mapOf("value" to JsonPrimitive("replaced \"my\" value")))

        val result = replacer.replacePlaceholders(json, StaticPlaceholderValueProvider(values))

        assertEquals(JsonObject(mapOf("key" to JsonPrimitive("replaced \"my\" value"))), result)
    }

    @Test
    fun `should replace placeholders in a nested JSON object`() = runTest {
        val json = JsonObject(
            mapOf(
                "key1" to JsonPrimitive("{{value1}}"),
                "nested" to JsonObject(mapOf("key2" to JsonPrimitive("{{value2}}")))
            )
        )
        val values = JsonObject(
            mapOf(
                "value1" to JsonPrimitive("replaced1"),
                "value2" to JsonPrimitive("replaced2")
            )
        )

        val result = replacer.replacePlaceholders(json, StaticPlaceholderValueProvider(values))

        val expected = JsonObject(
            mapOf(
                "key1" to JsonPrimitive("replaced1"),
                "nested" to JsonObject(mapOf("key2" to JsonPrimitive("replaced2")))
            )
        )
        assertEquals(expected, result)
    }

    @Test
    fun `should not replace placeholders if no match is found`() = runTest {
        val json = JsonObject(mapOf("key" to JsonPrimitive("{{value}}")))
        val values = JsonObject(mapOf("otherValue" to JsonPrimitive("replaced")))

        val result = replacer.replacePlaceholders(json, StaticPlaceholderValueProvider(values))

        val expected = JsonObject(mapOf("key" to JsonPrimitive("")))
        assertEquals(expected, result)
    }

    @Test
    fun `should replace missing variable with empty string`() = runTest {
        val json = JsonObject(mapOf("key" to JsonPrimitive("{{value}}")))
        val values = JsonObject(emptyMap())

        val result = replacer.replacePlaceholders(json, StaticPlaceholderValueProvider(values))
        val expected = JsonObject(mapOf("key" to JsonPrimitive("")))

        assertEquals(expected, result)
    }
}