package services.oneteam.ai.flow.execution

import io.kotest.common.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonObject
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.mapBuilders.SeriesIntervalMapBuilder
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.collection.Visibility
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.collection.form.FormRepository
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.otSerializer
import kotlin.test.assertEquals

class SeriesIntervalMapBuilderTest {

    companion object {
        val logger: Logger = LoggerFactory.getLogger(javaClass)

        val formRepository: FormRepository = mock(FormRepository::class.java)
        val foundationService: FoundationService = mock(FoundationService::class.java)
        val documentService: IDocumentService = mock(IDocumentService::class.java)
        val workspaceId = Workspace.Id(1)
        const val COOKIE = "cookie"

        val workspace = otSerializer.decodeFromString<Workspace.ForJson>(
            this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
        )

        val formConfiguration = workspace.findForm(FormConfiguration.Id("cp"))

        val foundation = Foundation.ForApi(
            Foundation.Id(4),
            Foundation.Name("foundationName"),
            Foundation.Key("foundationKey"),
            FoundationConfiguration.Id("emp"),
            workspaceId,
            Foundation.Id(5),
            EntityMetadata.now(),
            visibility = Visibility.INHERIT,
        )
    }

    @BeforeEach
    fun setup(): Unit = runBlocking {
        `when`(foundationService.get(foundation.id)).thenReturn(foundation)

        `when`(
            documentService.show<FormAnswer.ForJson>(
                "documentId", COOKIE, FormAnswer.ForJson::class, true
            )
        ).thenReturn(
            // empty document
            FormAnswer.ForJson(
                Form.Id(1),
                mutableMapOf(),
            )
        )
    }

    @Test
    fun `should build map`() = runTest {
        `when`(formRepository.getById(1)).thenReturn(
            Form.ForApi(
                Form.Id(1),
                FormConfiguration.Id(formConfiguration.id),
                foundation.id,
                SeriesConfiguration.Id(formConfiguration.seriesId!!),
                IntervalId("aa2024"),
                Visibility.INHERIT,
                Form.DocumentId("documentId"),
                Form.AnnotationDocumentId("annotationDocumentId"),
                workspaceId,
                EntityMetadata.now(),
                foundation
            )
        )

        val seriesIntervalMapBuilder = SeriesIntervalMapBuilder(workspace)

        val seriesIntervalMap = seriesIntervalMapBuilder.handle(
            (VariableInstance.Variable(
                JsonPrimitive("aa2024"), VariableDataType.fromString("seriesInterval.years"), "seriesInterval"
            ))
        )

        val expectedSeriesInterval = otSerializer.decodeFromString<JsonElement>(
            """
                { 
                    "id": "aa2024",
                    "name": "2024",
                    "previous": {
                        "id": null,
                        "name": null
                    },
                    "next": { 
                        "id": "aa2025",
                        "name": "2025"
                    },
                    "seriesConfiguration": {
                        "id": "years",
                        "name": "Years",
                        "intervals": [
                            {
                                "id": "aa2024",
                                "name": "2024"
                            },
                            {
                                "id": "aa2025",
                                "name": "2025"
                            }
                        ]
                    }         
                }     
            """.trimIndent()
        )

        assertNotNull(seriesIntervalMap)
        assertEquals(expectedSeriesInterval.jsonObject, seriesIntervalMap.jsonObject)
    }
}