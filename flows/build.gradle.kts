plugins {
    id("project.conventions")
}

group = "services.oneteam.ai.flows"
version = "0.0.1"


dependencies {
    implementation(project(":shared"))
    implementation(libs.gson)
    implementation(libs.jsonata)
    implementation(libs.commons.text)

    testImplementation(kotlin("test"))
    testImplementation(libs.bundles.kotest)
    testImplementation(libs.mockito)
    testImplementation(libs.mockk)
    testImplementation(libs.assertj.core)
    testImplementation(libs.junit.jupiter.params)
    testImplementation(ktorLibs.client.mock)

}
