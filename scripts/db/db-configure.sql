---- users and roles
-- create role if not exists https://stackoverflow.com/a/55954480
DO
$$
    BEGIN
        CREATE ROLE azure_pg_admin;
    EXCEPTION
        WHEN duplicate_object THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
    END
$$;

CREATE USER "otai_superuser" WITH LOGIN SUPERUSER CREATEROLE CREATEDB ENCRYPTED PASSWORD 'otai_superuser';
CREATE USER otai_admin WITH ENCRYPTED PASSWORD 'otai_admin';

---- setup up otai_superuser
-- otai_superuser will be running migrations and seeding data
GRANT azure_pg_admin TO otai_superuser;
GRANT ALL PRIVILEGES ON DATABASE "otai-dev-database" TO otai_superuser;

---- setup up otai_admin
-- otai_admin will be running the application

-- allow otai_admin to access all tables in the public schema that will be created in the future by otai_superuser
-- https://dba.stackexchange.com/a/294976
ALTER DEFAULT PRIVILEGES FOR USER otai_superuser IN SCHEMA public GRANT SELECT, UPDATE, INSERT, DELETE ON TABLES TO otai_admin;

CREATE SCHEMA IF NOT EXISTS spicedb;