#!/usr/bin/env zx
import fs from 'node:fs';
// <reference 'zx/globals' />

const SCRIPT_DIR = __dirname;

if (argv.help) {
    console.log(`
    Usage: env.mjs [--help]
    
    Write .env.local file at the top level of the project. 
    The .env.local file won't be in source control to avoid affecting CI/CD.

    Options:
    --help                 Display this help message
  `);
    process.exit(0);
}

const content = `

# KEYS

# random uuid without dashes
# same as SESSION_ENCRYPT on sync server
SESSION_ENCRYPT=<???>
# random uuid without dashes
# same as SESSION_SIGN on sync server
SESSION_SIGN=<???>

STORAGE_ACCESS_KEY=<???>

# "credential" from 1Password entry "OTAI PubSub - localdev"
AZURE_WEB_PUB_SUB_CONNECTION_STRING=Endpoint=<???>
# "jwt token" from 1Password entry "OTAI JWT - localdev"
SERVICE_JWT_TOKEN=<???>
# "jwt public key" from 1Password entry "OTAI JWT - localdev"
JWT_PUBLIC_KEY=<???>


# Debug
DEBUG_ENABLE_ENDPOINTS=true
# Tests
TEST_DB_USE_STATIC_PORT=true
# Logging
LOGGING_SERVICES_ONETEAM_AI=NONE
LOGGING_EXPOSED=NONE
LOGGING_FLYWAY=NONE
# SpiceDB
SPICEDB_TARGET=localhost:50051
SPICEDB_TOKEN=development-token
SPICEDB_USEPLAINTEXT=true
# Netty
CONNECTION_GROUP_SIZE=11
WORKER_GROUP_SIZE=11
CALL_GROUP_SIZE=17
#NUMBER_OF_THREADS=1
#MAX_CONCURRENT_COROUTINES=2
FLOWS_FED_STORAGE_TYPE=BLOB
# Azure Storage
STORAGE_ACCOUNT_NAME=otaidevinnovation
# FilePress
FILE_PRESS_URL=https://otai-shared-dev-filepress-djghb9bzfghvb7gh.australiaeast-01.azurewebsites.net/api/generateDocxFromRemoteTemplate
# Flows
USE_EXECUTION_STEP_FACTORY_V1=false
# Automerge document service
DOCUMENT_STRATEGY=API
# Database
DB_POOL_SIZE=20
FRONTEND_URL=
WEBSITE_SITE_NAME=local
CORS_HOSTS="http://localhost:8000;http://localhost:8001;http://localhost:8002"
DEVELOPMENT=true
LOAD_SAMPLE_DATA=true
PASS_VAULT_KEY_VAULT_PROVIDER=IN_MEMORY
PASS_VAULT_KEY_VAULT_NAME=localdev-vault
PASS_VAULT_KEY_VAULT_SECRET_NAME_PREFIX=localdev
PASS_VAULT_KEY_VAULT_MEMORY_LOAD_SEED_DATA=true
`;

const envLocalOutputPath = `${SCRIPT_DIR}/../.env.local`;
echo(`Writing ${envLocalOutputPath}`);

fs.writeFile(envLocalOutputPath, content, err => {
    if (err) {
        echo("Failed to write .env.local");
        echo(err);
    } else {
        echo('.env.local written successfully');
    }
});
